import { db, getDatabase, USE_MOCK_DB } from '@/config/database';
import { PointTransaction, TransactionType, CountResult } from '@/types';
import { MockDatabase } from '@/services/mockDatabase';

export class PointTransactionModel {
  private static tableName = 'point_transactions';

  /**
   * 创建积分交易记录
   */
  static async create(transactionData: {
    user_id: number;
    task_id?: number;
    amount: number;
    transaction_type: TransactionType;
    description: string;
    metadata?: Record<string, any>;
    balance_after: number;
  }): Promise<PointTransaction> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.createPointTransaction({
        ...transactionData,
        metadata: transactionData.metadata || {},
      });
    }

    const [transaction] = await db!(this.tableName)
      .insert({
        ...transactionData,
        metadata: JSON.stringify(transactionData.metadata || {}),
      })
      .returning('*');

    return transaction;
  }

  /**
   * 获取用户积分流水
   */
  static async findByUser(
    userId: number,
    page: number = 1,
    limit: number = 20,
    transactionType?: TransactionType
  ): Promise<{
    transactions: PointTransaction[];
    total: number;
    totalPages: number;
  }> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.getUserPointTransactions(userId, page, limit, transactionType);
    }

    const offset = (page - 1) * limit;

    let query = db!(this.tableName)
      .select('point_transactions.*', 'tasks.title as task_title')
      .leftJoin('tasks', 'point_transactions.task_id', 'tasks.id')
      .where('point_transactions.user_id', userId);

    if (transactionType) {
      query = query.where('point_transactions.transaction_type', transactionType);
    }

    const countResult = await query.clone().count('* as count') as CountResult[];
    const total = Number(countResult[0]?.count) || 0;

    const transactions = await query
      .orderBy('point_transactions.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      transactions,
      total,
      totalPages,
    };
  }

  /**
   * 获取用户今日积分统计
   */
  static async getTodayStats(userId: number): Promise<{
    earned: number;
    spent: number;
    net: number;
  }> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.getUserTodayPointStats(userId);
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const earnedResult = await db!(this.tableName)
      .where('user_id', userId)
      .where('amount', '>', 0)
      .where('created_at', '>=', today)
      .where('created_at', '<', tomorrow)
      .sum('amount as total') as any[];

    const spentResult = await db!(this.tableName)
      .where('user_id', userId)
      .where('amount', '<', 0)
      .where('created_at', '>=', today)
      .where('created_at', '<', tomorrow)
      .sum('amount as total') as any[];

    const earned = Number(earnedResult[0]?.total) || 0;
    const spent = Math.abs(Number(spentResult[0]?.total)) || 0;

    return {
      earned,
      spent,
      net: earned - spent,
    };
  }

  /**
   * 获取用户积分统计
   */
  static async getUserPointsStats(userId: number): Promise<{
    totalEarned: number;
    totalSpent: number;
    currentBalance: number;
    transactionCount: number;
  }> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.getUserPointsStats(userId);
    }

    const earnedResult = await db!(this.tableName)
      .where('user_id', userId)
      .where('amount', '>', 0)
      .sum('amount as total');

    const spentResult = await db!(this.tableName)
      .where('user_id', userId)
      .where('amount', '<', 0)
      .sum('amount as total');

    const countResult = await db!(this.tableName)
      .where('user_id', userId)
      .count('* as count');

    const balanceResult = await db!('users')
      .where('id', userId)
      .select('points_balance')
      .first();

    return {
      totalEarned: Number(earnedResult[0]?.total) || 0,
      totalSpent: Math.abs(Number(spentResult[0]?.total)) || 0,
      currentBalance: balanceResult?.points_balance || 0,
      transactionCount: Number(countResult[0]?.count) || 0,
    };
  }

  /**
   * 执行积分交易（原子操作）
   */
  static async executeTransaction(
    userId: number,
    amount: number,
    transactionType: TransactionType,
    description: string,
    taskId?: number,
    metadata?: Record<string, any>
  ): Promise<PointTransaction | null> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.executePointTransaction(userId, amount, transactionType, description, taskId, metadata);
    }

    return db!.transaction(async (trx) => {
      // 获取当前用户余额
      const user = await trx!('users')
        .where('id', userId)
        .select('points_balance')
        .first();

      if (!user) {
        throw new Error('用户不存在');
      }

      const currentBalance = user.points_balance;
      const newBalance = currentBalance + amount;

      // 检查余额是否足够（对于负数交易）
      if (amount < 0 && newBalance < 0) {
        throw new Error('积分余额不足');
      }

      // 更新用户积分余额
      await trx!('users')
        .where('id', userId)
        .update({
          points_balance: newBalance,
          updated_at: new Date(),
        });

      // 创建交易记录
      const [transaction] = await trx!(this.tableName)
        .insert({
          user_id: userId,
          task_id: taskId,
          amount,
          transaction_type: transactionType,
          description,
          metadata: JSON.stringify(metadata || {}),
          balance_after: newBalance,
        })
        .returning('*');

      return transaction;
    });
  }

  /**
   * 获取系统积分统计
   */
  static async getSystemStats(): Promise<{
    totalTransactions: number;
    totalPointsIssued: number;
    totalPointsSpent: number;
    activeUsers: number;
  }> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.getSystemPointStats();
    }

    const transactionCount = await db!(this.tableName)
      .count('* as count');

    const pointsIssued = await db!(this.tableName)
      .where('amount', '>', 0)
      .sum('amount as total');

    const pointsSpent = await db!(this.tableName)
      .where('amount', '<', 0)
      .sum('amount as total');

    const activeUsers = await db!(this.tableName)
      .distinct('user_id')
      .count('user_id as count');

    return {
      totalTransactions: Number(transactionCount[0]?.count) || 0,
      totalPointsIssued: Number(pointsIssued[0]?.total) || 0,
      totalPointsSpent: Math.abs(Number(pointsSpent[0]?.total)) || 0,
      activeUsers: Number(activeUsers[0]?.count) || 0,
    };
  }

  /**
   * 获取指定时间范围内的交易记录
   */
  static async findByDateRange(
    userId: number,
    startDate: Date,
    endDate: Date
  ): Promise<PointTransaction[]> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.findPointTransactionsByDateRange(userId, startDate, endDate);
    }
    return db!(this.tableName)
      .select('point_transactions.*', 'tasks.title as task_title')
      .leftJoin('tasks', 'point_transactions.task_id', 'tasks.id')
      .where('point_transactions.user_id', userId)
      .where('point_transactions.created_at', '>=', startDate)
      .where('point_transactions.created_at', '<=', endDate)
      .orderBy('point_transactions.created_at', 'desc');
  }

  /**
   * 根据任务ID获取相关交易记录
   */
  static async findByTask(taskId: number): Promise<PointTransaction[]> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.findPointTransactionsByTaskId(taskId);
    }
    return db!(this.tableName)
      .select('point_transactions.*', 'users.username')
      .leftJoin('users', 'point_transactions.user_id', 'users.id')
      .where('point_transactions.task_id', taskId)
      .orderBy('point_transactions.created_at', 'desc');
  }
}
