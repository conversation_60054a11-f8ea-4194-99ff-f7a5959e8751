import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Upload,
  Camera,
  FileText,
  Loader2,
  Award
} from 'lucide-react';
import { TaskExecutionSession, ExecutionResult, ExecutionProof, taskExecutionService } from '@/services/taskExecutionService';
import { TaskType } from '@/types';
import { toast } from 'react-hot-toast';

interface TaskExecutionDialogProps {
  session: TaskExecutionSession | null;
  result: ExecutionResult | null;
  onClose: () => void;
}

const TaskExecutionDialog: React.FC<TaskExecutionDialogProps> = ({
  session,
  result,
  onClose
}) => {
  const [showProofUpload, setShowProofUpload] = useState(false);
  const [proof, setProof] = useState<ExecutionProof>({
    screenshots: [],
    description: '',
    timestamp: Date.now()
  });
  const queryClient = useQueryClient();

  const submitMutation = useMutation(
    (proofData?: ExecutionProof) => 
      taskExecutionService.submitTaskCompletion(session?.id || '', proofData),
    {
      onSuccess: (data) => {
        toast.success(`任务完成！获得 ${result?.pointsEarned || 0} 积分`);
        queryClient.invalidateQueries(['userPoints']);
        queryClient.invalidateQueries(['taskExecutions']);
        onClose();
      },
      onError: (error: any) => {
        toast.error(error.message || '提交失败，请重试');
      }
    }
  );

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setProof(prev => ({
      ...prev,
      screenshots: [...(prev.screenshots || []), ...files]
    }));
  };

  const removeFile = (index: number) => {
    setProof(prev => ({
      ...prev,
      screenshots: prev.screenshots?.filter((_, i) => i !== index) || []
    }));
  };

  const handleSubmit = () => {
    if (result?.proofRequired && (!proof.screenshots?.length || !proof.description)) {
      toast.error('请上传完成截图并填写说明');
      return;
    }

    submitMutation.mutate(result?.proofRequired ? proof : undefined);
  };

  const getTaskTypeLabel = (type: TaskType): string => {
    const labels: Record<TaskType, string> = {
      like: '点赞',
      share: '分享',
      comment: '评论',
      follow: '关注'
    };
    return labels[type] || type;
  };

  if (!session || !result) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
        {/* 标题和状态图标 */}
        <div className="flex items-center space-x-3 mb-4">
          {result.success ? (
            <div className="p-2 bg-green-100 rounded-full">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          ) : (
            <div className="p-2 bg-red-100 rounded-full">
              <XCircle className="w-6 h-6 text-red-600" />
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {result.success ? '任务完成检测' : '任务执行失败'}
            </h3>
            <p className="text-sm text-gray-500">
              执行时长: {Math.round((Date.now() - session.startTime) / 1000)}秒
            </p>
          </div>
        </div>

        {/* 结果消息 */}
        <div className="mb-4">
          <p className="text-gray-700">{result.message}</p>
        </div>

        {/* 完成的任务类型 */}
        {result.completedRequirements.length > 0 && (
          <div className="mb-4">
            <h4 className="font-medium text-gray-800 mb-2">已完成的任务:</h4>
            <div className="flex flex-wrap gap-2">
              {result.completedRequirements.map(req => (
                <span key={req} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                  {getTaskTypeLabel(req)}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* 失败的任务类型 */}
        {result.failedRequirements.length > 0 && (
          <div className="mb-4">
            <h4 className="font-medium text-gray-800 mb-2">未完成的任务:</h4>
            <div className="flex flex-wrap gap-2">
              {result.failedRequirements.map(req => (
                <span key={req} className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                  {getTaskTypeLabel(req)}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* 积分奖励 */}
        {result.pointsEarned > 0 && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-yellow-600" />
              <span className="font-medium text-yellow-800">
                获得积分: {result.pointsEarned}
              </span>
            </div>
          </div>
        )}

        {/* 需要人工审核提示 */}
        {result.needsManualReview && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-blue-600" />
              <span className="text-sm text-blue-800">
                任务需要人工审核，审核通过后将发放积分
              </span>
            </div>
          </div>
        )}

        {/* 证明上传 */}
        {result.proofRequired && (
          <div className="mb-4">
            <h4 className="font-medium text-gray-800 mb-2 flex items-center">
              <Camera className="w-4 h-4 mr-1" />
              上传完成证明
            </h4>
            
            {/* 文件上传 */}
            <div className="mb-3">
              <label className="block text-sm text-gray-600 mb-1">完成截图</label>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileUpload}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              
              {/* 已上传文件列表 */}
              {proof.screenshots && proof.screenshots.length > 0 && (
                <div className="mt-2 space-y-1">
                  {proof.screenshots.map((file, index) => (
                    <div key={index} className="flex items-center justify-between text-xs text-gray-600 bg-gray-50 p-2 rounded">
                      <span>{file.name}</span>
                      <button
                        onClick={() => removeFile(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        删除
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* 说明文字 */}
            <div className="mb-3">
              <label className="block text-sm text-gray-600 mb-1">完成说明</label>
              <textarea
                value={proof.description}
                onChange={(e) => setProof(prev => ({ ...prev, description: e.target.value }))}
                placeholder="请简要说明您完成了哪些操作..."
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
                rows={3}
              />
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={submitMutation.isLoading}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
          >
            {result.success ? '关闭' : '重新尝试'}
          </button>
          
          {result.success && (
            <button
              onClick={handleSubmit}
              disabled={submitMutation.isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
            >
              {submitMutation.isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>提交中...</span>
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4" />
                  <span>确认完成</span>
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskExecutionDialog;
