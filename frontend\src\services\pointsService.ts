import { ApiClient } from './api';
import { PointTransaction } from '@/types';

export interface PointsStats {
  total_points: number;
  earned_today: number;
  earned_this_week: number;
  earned_this_month: number;
  spent_today: number;
  spent_this_week: number;
  spent_this_month: number;
  pending_points: number;
}

export class PointsService {
  /**
   * 获取积分统计
   */
  static async getPointsStats() {
    const response = await ApiClient.get<PointsStats>('/points/stats');
    return response.data;
  }

  /**
   * 获取积分交易记录
   */
  static async getPointsTransactions(page = 1, limit = 20, type?: string) {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    if (type) {
      params.append('type', type);
    }
    
    const response = await ApiClient.get<{
      data: PointTransaction[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>(`/points/transactions?${params.toString()}`);
    return response.data;
  }

  /**
   * 获取积分交易详情
   */
  static async getTransactionById(id: number) {
    return ApiClient.get<PointTransaction>(`/points/transactions/${id}`);
  }

  /**
   * 转账积分
   */
  static async transferPoints(data: {
    to_user_id: number;
    amount: number;
    description?: string;
  }) {
    return ApiClient.post<PointTransaction>('/points/transfer', data);
  }

  /**
   * 兑换积分
   */
  static async redeemPoints(data: {
    item_id: number;
    amount: number;
    description?: string;
  }) {
    return ApiClient.post<PointTransaction>('/points/redeem', data);
  }

  /**
   * 扣除积分（用于任务发布）
   */
  static async deductPoints(data: {
    amount: number;
    task_id?: number;
    description: string;
  }) {
    return ApiClient.post<PointTransaction>('/points/deduct', {
      ...data,
      amount: -Math.abs(data.amount), // 确保是负数
      transaction_type: 'task_publish',
    });
  }

  /**
   * 添加积分（用于任务完成奖励）
   */
  static async addPoints(data: {
    amount: number;
    task_id?: number;
    description: string;
  }) {
    return ApiClient.post<PointTransaction>('/points/add', {
      ...data,
      amount: Math.abs(data.amount), // 确保是正数
      transaction_type: 'task_complete',
    });
  }

  /**
   * 临时方法：添加测试积分（仅用于开发测试）
   */
  static async addTestPoints(amount: number) {
    return ApiClient.post<PointTransaction>('/points/test-add', {
      amount: Math.abs(amount),
      description: '测试积分',
      transaction_type: 'test',
    });
  }
}