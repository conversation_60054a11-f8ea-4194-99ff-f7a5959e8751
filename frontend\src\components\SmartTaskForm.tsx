import React, { useState } from 'react';
import { 
  Link, 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Play, 
  User,
  Coins
} from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { getVideoInfo } from '../services/videoService';

interface VideoInfo {
  title: string;
  description: string;
  cover: string;
  platform: string;
  author: string;
  duration?: number;
  video_url: string;
}

interface SmartTaskFormProps {
  onSubmit: (data: {
    videoInfo: VideoInfo;
    taskType: string;
    customDescription: string;
    totalQuota: number;
    rewardPoints: number;
    totalCost: number;
    taskQuantities: Record<string, number>;
  }) => void;
  onCancel?: () => void;
}

const SmartTaskForm: React.FC<SmartTaskFormProps> = ({ onSubmit, onCancel }) => {
  const { user } = useAuthStore();
  const [videoUrl, setVideoUrl] = useState('');
  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null);
  const [customDescription, setCustomDescription] = useState('');
  const [taskQuantities, setTaskQuantities] = useState<Record<string, number>>({
    like: 0,
    share: 0,
    comment: 0,
    follow: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 任务类型配置（包含积分成本）
  const taskTypes = [
    { id: 'like', name: '点赞', icon: '❤️', color: 'bg-red-500', cost: 10, description: '为视频点赞支持' },
    { id: 'share', name: '分享', icon: '📤', color: 'bg-blue-500', cost: 15, description: '分享视频给朋友' },
    { id: 'comment', name: '评论', icon: '💬', color: 'bg-green-500', cost: 20, description: '发表评论互动' },
    { id: 'follow', name: '关注', icon: '👤', color: 'bg-purple-500', cost: 25, description: '关注作者账号' },
  ];

  // 计算总费用
  const totalCost = taskTypes.reduce((total, type) => {
    const quantity = taskQuantities[type.id] || 0;
    return total + (type.cost * quantity);
  }, 0);

  // 计算总任务数量
  const totalQuota = Object.values(taskQuantities).reduce((sum, qty) => sum + qty, 0);

  // 平台图标映射
  const getPlatformIcon = (platform: string) => {
    const icons: Record<string, string> = {
      douyin: '🎵',
      kuaishou: '⚡',
      xiaohongshu: '📖',
      bilibili: '📺',
      youtube: '📹',
      tiktok: '🎬'
    };
    return icons[platform] || '📱';
  };

  // 平台显示名称
  const getPlatformDisplayName = (platform: string) => {
    const names: Record<string, string> = {
      douyin: '抖音',
      kuaishou: '快手',
      xiaohongshu: '小红书',
      bilibili: 'B站',
      youtube: 'YouTube',
      tiktok: 'TikTok'
    };
    return names[platform] || platform;
  };

  // 验证视频URL
  const isValidVideoUrl = (url: string): boolean => {
    const patterns = [
      // 抖音
      /douyin\.com\/video/,
      /douyin\.com\/note/,
      /v\.douyin\.com/,
      /iesdouyin\.com/,
      // 快手
      /kuaishou\.com\/short-video/,
      /v\.kuaishou\.com/,
      // 小红书
      /xiaohongshu\.com\/explore/,
      /xiaohongshu\.com\/discovery/,
      /xhslink\.com/,
      // B站
      /bilibili\.com\/video/,
      /b23\.tv/,
      // YouTube
      /youtube\.com\/watch/,
      /youtube\.com\/shorts/,
      /youtu\.be/,
      // TikTok
      /tiktok\.com.*\/video/,
      /vm\.tiktok\.com/,
      /tiktok\.com\/t\//
    ];
    return patterns.some(pattern => pattern.test(url));
  };

  // 处理URL变化
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value;
    setVideoUrl(url);
    setError(null);
    
    if (url && isValidVideoUrl(url)) {
      handleVideoAnalysis(url);
    } else if (url) {
      setVideoInfo(null);
    }
  };

  // 真实视频解析
  const handleVideoAnalysis = async (url: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // 使用真实的视频服务获取视频信息
      const videoInfo = await getVideoInfo(url); // 使用真实API获取视频信息
      setVideoInfo(videoInfo);
    } catch (err) {
      setError('视频解析失败，请检查链接是否正确');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = () => {
    if (!videoInfo) return;

    // 检查是否选择了任务类型
    if (totalQuota === 0) {
      setError('请至少选择一种任务类型并设置数量');
      return;
    }

    // 检查积分余额
    if (user && user.points_balance < totalCost) {
      setError(`积分不足！需要 ${totalCost} 积分，当前余额 ${user.points_balance} 积分`);
      return;
    }

    onSubmit({
      videoInfo,
      taskType: 'mixed', // 混合任务类型
      customDescription,
      totalQuota,
      rewardPoints: Math.round(totalCost / totalQuota), // 平均奖励
      totalCost,
      taskQuantities
    });
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* PC端左右分栏布局 */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* 左侧主要内容区 */}
        <div className="xl:col-span-2 space-y-6">
          {/* 视频链接输入 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">智能发布任务</h2>
              <p className="text-gray-600 text-lg">只需输入视频链接，系统将自动识别平台并获取视频信息</p>
            </div>

            <div className="mb-8">
              <label className="block text-lg font-semibold text-gray-800 mb-3">
                <span className="flex items-center">
                  <Link className="h-5 w-5 mr-2 text-blue-600" />
                  视频链接
                  <span className="text-red-500 ml-1">*</span>
                </span>
              </label>
              <div className="relative">
                <input
                  type="url"
                  value={videoUrl}
                  onChange={handleUrlChange}
                  placeholder="请输入视频链接，如：https://www.douyin.com/video/123"
                  className="w-full px-4 py-4 pl-12 pr-12 text-lg border-2 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                />
                
                {isLoading && (
                  <div className="absolute right-4 top-4.5">
                    <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
                  </div>
                )}
                
                {videoInfo && !isLoading && (
                  <div className="absolute right-4 top-4.5">
                    <CheckCircle className="h-6 w-6 text-green-500" />
                  </div>
                )}
              </div>

              {error && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center text-red-700">
                  <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
                  <span className="text-sm font-medium">{error}</span>
                </div>
              )}

              {videoUrl && !isValidVideoUrl(videoUrl) && !error && (
                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-700 font-medium">
                    💡 支持的平台：抖音、快手、小红书、B站、YouTube、TikTok
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* 视频信息预览 */}
          {videoInfo && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <CheckCircle className="h-6 w-6 text-green-500 mr-2" />
                <h3 className="text-xl font-semibold text-gray-900">视频信息已识别</h3>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="md:col-span-1">
                  <div className="relative">
                    <img
                      src={videoInfo.cover}
                      alt="视频封面"
                      className="w-full aspect-[3/4] object-cover rounded-xl shadow-md"
                    />
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded-md text-xs flex items-center">
                      <Play className="h-3 w-3 mr-1" />
                      {videoInfo.duration ? `${Math.floor(videoInfo.duration / 60)}:${(videoInfo.duration % 60).toString().padStart(2, '0')}` : '--:--'}
                    </div>
                  </div>
                </div>

                <div className="md:col-span-2 space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                      <span className="text-lg mr-1">{getPlatformIcon(videoInfo.platform)}</span>
                      {getPlatformDisplayName(videoInfo.platform)}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2 leading-tight">
                      {videoInfo.title}
                    </h4>

                    <div className="flex items-center text-gray-600 mb-3">
                      <User className="h-4 w-4 mr-2" />
                      <span className="font-medium">{videoInfo.author}</span>
                    </div>

                    {videoInfo.description && (
                      <div className="bg-gray-50 rounded-lg p-3">
                        <p className="text-sm text-gray-700 leading-relaxed">
                          {videoInfo.description}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 任务类型选择 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <label className="block text-lg font-semibold text-gray-800 mb-4">
              <span className="flex items-center">
                <span className="text-2xl mr-2">🎯</span>
                任务类型与数量
                <span className="text-red-500 ml-1">*</span>
              </span>
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {taskTypes.map((type) => (
                <div
                  key={type.id}
                  className="p-4 rounded-xl border-2 border-gray-200 bg-white hover:border-gray-300 transition-all duration-200"
                >
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-full ${type.color} flex items-center justify-center text-white text-xl shadow-lg flex-shrink-0`}>
                      {type.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-semibold text-gray-900 text-lg">{type.name}</span>
                        <div className="flex items-center text-yellow-600 font-semibold">
                          <Coins className="w-4 h-4 mr-1" />
                          <span>{type.cost}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => setTaskQuantities(prev => ({
                              ...prev,
                              [type.id]: Math.max(0, (prev[type.id] || 0) - 1)
                            }))}
                            className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center text-gray-600 transition-colors"
                          >
                            -
                          </button>
                          <span className="w-12 text-center font-semibold text-lg">
                            {taskQuantities[type.id] || 0}
                          </span>
                          <button
                            onClick={() => setTaskQuantities(prev => ({
                              ...prev,
                              [type.id]: Math.min(100, (prev[type.id] || 0) + 1)
                            }))}
                            className="w-8 h-8 rounded-full bg-blue-500 hover:bg-blue-600 flex items-center justify-center text-white transition-colors"
                          >
                            +
                          </button>
                        </div>
                        <span className="text-sm text-gray-500">个任务</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <p className="mt-4 text-sm text-gray-500">
              💡 可以同时选择多种任务类型，每种类型设置不同的数量
            </p>
          </div>

          {/* 自定义描述 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <label className="block text-lg font-semibold text-gray-800 mb-3">
              <span className="flex items-center">
                <span className="text-2xl mr-2">📝</span>
                任务描述
              </span>
            </label>
            <textarea
              value={customDescription}
              onChange={(e) => setCustomDescription(e.target.value)}
              placeholder="请详细描述任务要求，如：为这个视频点赞并评论'你的内容很棒！'"
              rows={5}
              className="w-full px-4 py-4 border-2 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none text-base"
            />
            <p className="mt-2 text-sm text-gray-500">
              💡 详细的任务描述有助于提高任务完成质量
            </p>
          </div>


        </div>

        {/* 右侧信息栏 */}
        <div className="xl:col-span-1 space-y-6">
          {/* 费用预览 */}
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg shadow-sm border border-yellow-200 p-6">
            <div className="flex items-center mb-4">
              <Coins className="h-6 w-6 text-yellow-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">费用预览</h3>
            </div>

            <div className="space-y-3">
              {/* 任务类型明细 */}
              {taskTypes.map((type) => {
                const quantity = taskQuantities[type.id] || 0;
                const subtotal = type.cost * quantity;
                if (quantity === 0) return null;

                return (
                  <div key={type.id} className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{type.icon}</span>
                      <span className="text-gray-700">{type.name}:</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">{quantity} × {type.cost}</div>
                      <div className="font-semibold text-yellow-600">{subtotal} 积分</div>
                    </div>
                  </div>
                );
              })}

              {totalQuota > 0 && (
                <>
                  <div className="border-t border-yellow-200 pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-700">总任务数：</span>
                      <span className="font-semibold text-lg">{totalQuota} 个</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-900 font-semibold">总费用：</span>
                    <span className="font-bold text-2xl text-yellow-600">{totalCost} 积分</span>
                  </div>
                </>
              )}

              {totalQuota === 0 && (
                <div className="text-center py-4 text-gray-500">
                  请选择任务类型并设置数量
                </div>
              )}

              <div className="mt-4 p-3 bg-white rounded-lg border border-yellow-200">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">当前积分：</span>
                  <span className="font-semibold text-lg">{user?.points_balance || 0} 积分</span>
                </div>
                {user && user.points_balance < totalCost && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                    <AlertCircle className="w-4 h-4 inline mr-1" />
                    积分不足，还需要 {totalCost - user.points_balance} 积分
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="space-y-4">
              <button
                onClick={handleSubmit}
                disabled={!videoInfo || isLoading || totalQuota === 0 || (!!user && user.points_balance < totalCost)}
                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-4 px-8 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-blue-800 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-6 w-6 mr-3 animate-spin" />
                    解析中...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <span className="text-2xl mr-2">🚀</span>
                    发布任务
                  </div>
                )}
              </button>

              {onCancel && (
                <button
                  onClick={onCancel}
                  className="w-full px-8 py-3 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
                >
                  取消
                </button>
              )}
            </div>

            {!videoInfo && !isLoading && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800 text-center">
                  ⚠️ 请先输入视频链接并等待系统识别视频信息
                </p>
              </div>
            )}

            {videoInfo && totalQuota === 0 && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800 text-center">
                  🎯 请选择任务类型并设置数量
                </p>
              </div>
            )}

            {videoInfo && totalQuota > 0 && user && user.points_balance < totalCost && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800 text-center">
                  💰 积分不足，无法发布任务。需要 {totalCost} 积分，当前余额 {user.points_balance} 积分
                </p>
              </div>
            )}
          </div>

          {/* 支持的平台示例 */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg shadow-sm border border-blue-200 p-6">
            <div className="flex items-center mb-4">
              <span className="text-2xl mr-2">💡</span>
              <h4 className="text-lg font-semibold text-blue-900">支持的平台</h4>
            </div>
            <div className="space-y-3">
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <div className="flex items-center mb-1">
                  <span className="text-lg mr-2">🎵</span>
                  <span className="font-medium text-gray-800">抖音</span>
                </div>
                <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded block">
                  douyin.com/video/123
                </code>
              </div>
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <div className="flex items-center mb-1">
                  <span className="text-lg mr-2">⚡</span>
                  <span className="font-medium text-gray-800">快手</span>
                </div>
                <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded block">
                  kuaishou.com/short-video/123
                </code>
              </div>
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <div className="flex items-center mb-1">
                  <span className="text-lg mr-2">📖</span>
                  <span className="font-medium text-gray-800">小红书</span>
                </div>
                <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded block">
                  xiaohongshu.com/explore/abc
                </code>
              </div>
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <div className="flex items-center mb-1">
                  <span className="text-lg mr-2">📺</span>
                  <span className="font-medium text-gray-800">B站</span>
                </div>
                <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded block">
                  bilibili.com/video/BV123
                </code>
              </div>
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <div className="flex items-center mb-1">
                  <span className="text-lg mr-2">📹</span>
                  <span className="font-medium text-gray-800">YouTube</span>
                </div>
                <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded block">
                  youtube.com/watch?v=abc123
                </code>
              </div>
              <div className="bg-white rounded-lg p-3 border border-blue-100">
                <div className="flex items-center mb-1">
                  <span className="text-lg mr-2">🎬</span>
                  <span className="font-medium text-gray-800">TikTok</span>
                </div>
                <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded block">
                  tiktok.com/@user/video/123
                </code>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmartTaskForm;
