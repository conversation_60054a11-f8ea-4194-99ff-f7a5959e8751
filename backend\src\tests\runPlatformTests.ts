import { identifyPlatform } from '../utils/platformIdentifier';

// Test cases for manual verification
const testCases = [
  // Specific failing Bilibili URL
  { url: 'https://www.bilibili.com/video/BV1Hk7TzUEL1/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=3d345170a9f40f6dd7b839003d919f55', expected: 'bilibili', description: 'Bilibili video with complex query parameters' },
  // Douyin tests
  { url: 'https://www.douyin.com/video/1234567890', expected: 'douyin', description: 'Douyin video URL' },
  { url: 'https://v.douyin.com/AbC123/', expected: 'douyin', description: 'Douyin short URL' },
  { url: 'douyin.com/note/2222222222', expected: 'douyin', description: 'Douyin note URL without protocol' },
  
  // Kuaishou tests
  { url: 'https://www.kuaishou.com/short-video/1234567890', expected: 'kuaishou', description: 'Kuaishou video URL' },
  { url: 'https://v.kuaishou.com/XyZ789', expected: 'kuaishou', description: 'Kuaishou short URL' },
  
  // Xiaohongshu tests
  { url: 'https://www.xiaohongshu.com/explore/abc123def456', expected: 'xiaohongshu', description: 'Xiaohongshu explore URL' },
  { url: 'https://xhslink.com/AbC123', expected: 'xiaohongshu', description: 'Xiaohongshu short URL' },
  
  // Bilibili tests
  { url: 'https://www.bilibili.com/video/BV1234567890', expected: 'bilibili', description: 'Bilibili BV video URL' },
  { url: 'https://b23.tv/AbC123', expected: 'bilibili', description: 'Bilibili short URL' },
  
  // YouTube tests
  { url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', expected: 'youtube', description: 'YouTube watch URL' },
  { url: 'https://youtu.be/dQw4w9WgXcQ', expected: 'youtube', description: 'YouTube short URL' },
  { url: 'https://www.youtube.com/shorts/AbC123DeF456', expected: 'youtube', description: 'YouTube Shorts URL' },
  
  // TikTok tests
  { url: 'https://www.tiktok.com/@username/video/1234567890', expected: 'tiktok', description: 'TikTok video URL' },
  { url: 'https://vm.tiktok.com/AbC123/', expected: 'tiktok', description: 'TikTok short URL' },
  
  // Edge cases
  { url: 'douyin.com/video/123', expected: 'douyin', description: 'URL without protocol' },
  { url: '  https://youtube.com/watch?v=abc  ', expected: 'youtube', description: 'URL with spaces' },
  { url: 'https://unknown-platform.com/video/123', expected: 'Unknown', description: 'Unknown platform' },
  { url: '', expected: 'Unknown', description: 'Empty URL' },
];

console.log('🧪 Running Platform Identifier Tests...\n');

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  const result = identifyPlatform(testCase.url);
  const success = result === testCase.expected;
  
  if (success) {
    console.log(`✅ Test ${index + 1}: ${testCase.description}`);
    console.log(`   URL: ${testCase.url}`);
    console.log(`   Result: ${result}\n`);
    passed++;
  } else {
    console.log(`❌ Test ${index + 1}: ${testCase.description}`);
    console.log(`   URL: ${testCase.url}`);
    console.log(`   Expected: ${testCase.expected}`);
    console.log(`   Got: ${result}\n`);
    failed++;
  }
});

console.log('📊 Test Results:');
console.log(`✅ Passed: ${passed}`);
console.log(`❌ Failed: ${failed}`);
console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

if (failed === 0) {
  console.log('\n🎉 All tests passed! Platform identification is working correctly.');
} else {
  console.log('\n⚠️  Some tests failed. Please review the implementation.');
}
