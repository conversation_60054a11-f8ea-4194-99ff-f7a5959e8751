import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { useMutation } from 'react-query';
import { Eye, EyeOff, Mail, Lock, User, AlertTriangle } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { AuthService } from '@/services/authService';
import { RegisterForm } from '@/types';
import LoadingSpinner from '@/components/LoadingSpinner';
import toast from 'react-hot-toast';

const RegisterPage: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [dbStatus, setDbStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const navigate = useNavigate();
  const { login } = useAuthStore();

  // 检查数据库连接状态
  useEffect(() => {
    const checkDbStatus = async () => {
      try {
        const response = await fetch('http://localhost:3000/health');
        await response.json(); // 检查响应但不使用数据
        setDbStatus('connected');
      } catch (error) {
        setDbStatus('disconnected');
      }
    };

    checkDbStatus();
  }, []);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterForm>();

  const password = watch('password');

  const registerMutation = useMutation(AuthService.register, {
    onSuccess: (response) => {
      if (response.success && response.data) {
        login(response.data.user, response.data.tokens);
        toast.success('注册成功！欢迎加入互助点赞平台！');
        navigate('/dashboard');
      }
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.error || '注册失败，请重试';
      if (error.response?.status === 503) {
        toast.error('数据库服务不可用，请稍后再试');
      } else {
        toast.error(errorMessage);
      }
    },
  });

  const onSubmit = (data: RegisterForm) => {
    const { confirmPassword, ...submitData } = data;
    registerMutation.mutate(submitData as any);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* 头部 */}
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            创建账号
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            加入互助点赞平台，开始您的积分之旅
          </p>
        </div>

        {/* 数据库状态提示 */}
        {dbStatus === 'disconnected' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-yellow-800">
                  数据库服务不可用
                </h3>
                <p className="text-sm text-yellow-700 mt-1">
                  当前无法注册，请联系管理员启动数据库服务。
                  <br />
                  <span className="text-xs">
                    提示：需要启动PostgreSQL数据库并配置连接信息
                  </span>
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 注册表单 */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* 用户名输入 */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                用户名
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('username', {
                    required: '请输入用户名',
                    minLength: {
                      value: 3,
                      message: '用户名至少3位',
                    },
                    maxLength: {
                      value: 20,
                      message: '用户名最多20位',
                    },
                    pattern: {
                      value: /^[a-zA-Z0-9_]+$/,
                      message: '用户名只能包含字母、数字和下划线',
                    },
                  })}
                  type="text"
                  className={`input pl-10 ${errors.username ? 'input-error' : ''}`}
                  placeholder="请输入用户名"
                />
              </div>
              {errors.username && (
                <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>
              )}
            </div>

            {/* 邮箱输入 */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                邮箱地址
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('email', {
                    required: '请输入邮箱地址',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: '请输入有效的邮箱地址',
                    },
                  })}
                  type="email"
                  className={`input pl-10 ${errors.email ? 'input-error' : ''}`}
                  placeholder="请输入邮箱地址"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            {/* 密码输入 */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                密码
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('password', {
                    required: '请输入密码',
                    minLength: {
                      value: 6,
                      message: '密码至少6位',
                    },
                    pattern: {
                      value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                      message: '密码必须包含大小写字母和数字',
                    },
                  })}
                  type={showPassword ? 'text' : 'password'}
                  className={`input pl-10 pr-10 ${errors.password ? 'input-error' : ''}`}
                  placeholder="请输入密码"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>

            {/* 确认密码输入 */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                确认密码
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  {...register('confirmPassword', {
                    required: '请确认密码',
                    validate: (value) => value === password || '两次输入的密码不一致',
                  })}
                  type={showConfirmPassword ? 'text' : 'password'}
                  className={`input pl-10 pr-10 ${errors.confirmPassword ? 'input-error' : ''}`}
                  placeholder="请再次输入密码"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.confirmPassword.message}</p>
              )}
            </div>

            {/* 服务条款 */}
            <div className="flex items-center">
              <input
                id="agree-terms"
                name="agree-terms"
                type="checkbox"
                required
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="agree-terms" className="ml-2 block text-sm text-gray-900">
                我同意{' '}
                <a href="#" className="text-primary-600 hover:text-primary-500">
                  服务条款
                </a>{' '}
                和{' '}
                <a href="#" className="text-primary-600 hover:text-primary-500">
                  隐私政策
                </a>
              </label>
            </div>

            {/* 注册按钮 */}
            <button
              type="submit"
              disabled={registerMutation.isLoading || dbStatus === 'disconnected'}
              className={`btn w-full flex items-center justify-center ${
                dbStatus === 'disconnected'
                  ? 'btn-secondary cursor-not-allowed opacity-50'
                  : 'btn-primary'
              }`}
            >
              {registerMutation.isLoading ? (
                <LoadingSpinner size="sm" />
              ) : dbStatus === 'disconnected' ? (
                '数据库未连接'
              ) : (
                '创建账号'
              )}
            </button>
          </form>

          {/* 分割线 */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">或者</span>
              </div>
            </div>
          </div>

          {/* 登录链接 */}
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              已有账号？{' '}
              <Link
                to="/login"
                className="font-medium text-primary-600 hover:text-primary-500"
              >
                立即登录
              </Link>
            </p>
          </div>
        </div>

        {/* 注册福利提示 */}
        <div className="gradient-primary rounded-lg p-4 text-white text-center">
          <p className="text-sm font-medium">🎉 注册即送100积分</p>
          <p className="text-xs mt-1 opacity-90">
            立即开始您的互助点赞之旅
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
