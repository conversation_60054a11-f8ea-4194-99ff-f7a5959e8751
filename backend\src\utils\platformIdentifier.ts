import { Platform } from '@/types';

// 定义每个平台的识别规则
const platformRules: { platform: Platform; hostnames: string[]; paths: string[]; patterns?: RegExp[]; specialHostnames?: string[] }[] = [
  {
    platform: 'youtube',
    hostnames: ['youtube.com', 'youtu.be', 'm.youtube.com'],
    paths: ['/shorts/', '/watch'],
    patterns: [/\/watch\?v=[A-Za-z0-9_-]+/, /\/shorts\/[A-Za-z0-9_-]+/],
    specialHostnames: ['youtu.be', 'm.youtube.com'],
  },
  {
    platform: 'tiktok',
    hostnames: ['tiktok.com', 'vm.tiktok.com', 'm.tiktok.com'],
    paths: ['/video/', '/@', '/t/'],
    patterns: [/@[^\/]+\/video\/\d+/, /\/t\/[A-Za-z0-9]+/],
    specialHostnames: ['vm.tiktok.com', 'm.tiktok.com'],
  },
  {
    platform: 'douyin',
    hostnames: ['douyin.com', 'v.douyin.com', 'iesdouyin.com'],
    paths: ['/video/', '/note/', '/share/video/'],
    patterns: [/\/video\/\d+/, /\/note\/\d+/, /\?recommend=/, /\/share\/video\/\d+/],
    specialHostnames: ['v.douyin.com'],
  },
  {
    platform: 'kuaishou',
    hostnames: ['kuaishou.com', 'v.kuaishou.com'],
    paths: ['/short-video/', '/profile/'],
    patterns: [/\/short-video\/\d+/, /\/profile\/[^\/]+\/video\/\d+/],
    specialHostnames: ['v.kuaishou.com'],
  },
  {
    platform: 'xiaohongshu',
    hostnames: ['xiaohongshu.com', 'xhslink.com'],
    paths: ['/explore/', '/discovery/'],
    patterns: [/\/explore\/[A-Za-z0-9]+/, /\/discovery\/item\/[A-Za-z0-9]+/],
    specialHostnames: ['xhslink.com'],
  },
  {
    platform: 'bilibili',
    hostnames: ['bilibili.com', 'b23.tv'],
    paths: ['/video/'],
    patterns: [/\/video\/BV[a-zA-Z0-9]+/],
    specialHostnames: ['b23.tv'],
  },
];

/**
 * 根据URL使用规则集识别短视频平台
 * @param url a string that might be a URL
 * @returns a Platform string or 'Unknown'
 */
export const identifyPlatform = (url: string): Platform | 'Unknown' => {
  if (!url || typeof url !== 'string') {
    console.log(`[Platform Identifier] Empty or invalid URL: ${url}`);
    return 'Unknown';
  }

  // 清理URL，移除多余的空格和特殊字符
  const cleanUrl = url.trim().replace(/[<>"'`]/g, '');

  // 更宽松的URL匹配，支持不带协议的URL
  const urlRegex = /(https?:\/\/[^\s]+|(?:www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}[^\s]*)/;
  const match = cleanUrl.match(urlRegex);

  if (!match) {
    console.log(`[Platform Identifier] URL doesn't match regex: ${cleanUrl}`);
    return 'Unknown';
  }

  let extractedUrl = match[0];

  // 如果URL不包含协议，添加https://
  if (!extractedUrl.startsWith('http://') && !extractedUrl.startsWith('https://')) {
    extractedUrl = 'https://' + extractedUrl;
  }

  console.log(`[Platform Identifier] Extracted URL: ${extractedUrl}`);

  try {
    const parsedUrl = new URL(extractedUrl);
    const host = parsedUrl.hostname.toLowerCase().replace(/^www\./, ''); // 移除www前缀
    const fullPath = parsedUrl.pathname + parsedUrl.search + parsedUrl.hash;
    console.log(`[Platform Identifier] Host: ${host}, Path: ${fullPath}`);

    for (const rule of platformRules) {
      // 检查当前URL的域名是否与规则匹配 (完全匹配或作为子域名匹配)
      const matchedHostname = rule.hostnames.find(h => {
        const cleanHostname = h.replace(/^www\./, '');
        return host === cleanHostname || host.endsWith(`.${cleanHostname}`);
      });

      if (matchedHostname) {
        console.log(`[Platform Identifier] Matched hostname for ${rule.platform}: ${matchedHostname}`);

        // 如果是特殊短域名, 则无需检查路径, 直接返回平台
        const specialHost = rule.specialHostnames?.find(sh => {
          const cleanSpecialHost = sh.replace(/^www\./, '');
          return host === cleanSpecialHost || host.endsWith(`.${cleanSpecialHost}`);
        });

        if (specialHost) {
          console.log(`[Platform Identifier] Special hostname detected, returning ${rule.platform}`);
          return rule.platform;
        }

        // 检查路径是否匹配
        if (rule.paths.some(path => fullPath.includes(path))) {
          console.log(`[Platform Identifier] Path matched for ${rule.platform}`);
          return rule.platform;
        }

        // 检查正则表达式模式是否匹配
        if (rule.patterns && rule.patterns.some(pattern => pattern.test(fullPath))) {
          console.log(`[Platform Identifier] Pattern matched for ${rule.platform}`);
          return rule.platform;
        }

        // 如果域名匹配但路径不匹配，对于某些平台仍然可以识别
        if (['youtube', 'tiktok'].includes(rule.platform)) {
          console.log(`[Platform Identifier] Domain matched for ${rule.platform}, accepting despite path mismatch`);
          return rule.platform;
        }
      }
    }

    console.log(`[Platform Identifier] No platform matched for ${extractedUrl}`);
    return 'Unknown';
  } catch (error) {
    console.error(`[Platform Identifier] Invalid URL format: ${extractedUrl}`, error);
    return 'Unknown';
  }
};
