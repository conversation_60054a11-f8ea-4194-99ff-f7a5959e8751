import React from 'react';
import {
  Shield,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Award,
  Lock,
  Unlock
} from 'lucide-react';
import { UserCreditInfo, creditSystemService } from '@/services/creditSystemService';

interface CreditInfoCardProps {
  creditInfo: UserCreditInfo;
  showDetails?: boolean;
  className?: string;
}

const CreditInfoCard: React.FC<CreditInfoCardProps> = ({
  creditInfo,
  showDetails = false,
  className = ''
}) => {
  const getReputationColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 70) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (score >= 50) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getTrustLevelInfo = (level: UserCreditInfo['trust_level']) => {
    switch (level) {
      case 'verified':
        return {
          label: '已验证',
          icon: Award,
          color: 'text-purple-600 bg-purple-50 border-purple-200'
        };
      case 'high':
        return {
          label: '高信任',
          icon: Shield,
          color: 'text-green-600 bg-green-50 border-green-200'
        };
      case 'medium':
        return {
          label: '中等信任',
          icon: CheckCircle,
          color: 'text-blue-600 bg-blue-50 border-blue-200'
        };
      case 'low':
        return {
          label: '低信任',
          icon: AlertTriangle,
          color: 'text-yellow-600 bg-yellow-50 border-yellow-200'
        };
    }
  };

  const getStatusInfo = (status: UserCreditInfo['account_status']) => {
    switch (status) {
      case 'active':
        return {
          label: '正常',
          icon: CheckCircle,
          color: 'text-green-600'
        };
      case 'restricted':
        return {
          label: '受限',
          icon: AlertTriangle,
          color: 'text-yellow-600'
        };
      case 'suspended':
        return {
          label: '暂停',
          icon: XCircle,
          color: 'text-red-600'
        };
    }
  };

  const trustLevelInfo = getTrustLevelInfo(creditInfo.trust_level);
  const statusInfo = getStatusInfo(creditInfo.account_status);
  const TrustIcon = trustLevelInfo.icon;
  const StatusIcon = statusInfo.icon;

  const successRate = creditInfo.successful_verifications + creditInfo.failed_verifications > 0
    ? (creditInfo.successful_verifications / (creditInfo.successful_verifications + creditInfo.failed_verifications) * 100)
    : 0;

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      {/* 标题 */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">信用信息</h3>
        <div className="flex items-center space-x-2">
          <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />
          <span className={`text-sm font-medium ${statusInfo.color}`}>
            {statusInfo.label}
          </span>
        </div>
      </div>

      {/* 信誉度显示 */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">信誉度</span>
          <span className={`text-lg font-bold ${getReputationColor(creditInfo.reputation_score).split(' ')[0]}`}>
            {creditInfo.reputation_score}/100
          </span>
        </div>
        
        {/* 信誉度进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              creditInfo.reputation_score >= 90 ? 'bg-green-500' :
              creditInfo.reputation_score >= 70 ? 'bg-blue-500' :
              creditInfo.reputation_score >= 50 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${creditInfo.reputation_score}%` }}
          />
        </div>
      </div>

      {/* 信任等级 */}
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${trustLevelInfo.color} mb-4`}>
        <TrustIcon className="w-4 h-4 mr-1" />
        {trustLevelInfo.label}
      </div>

      {/* 详细信息 */}
      {showDetails && (
        <div className="space-y-3 pt-4 border-t border-gray-200">
          {/* 任务统计 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">
                {creditInfo.total_tasks_completed}
              </p>
              <p className="text-sm text-gray-500">完成任务</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {successRate.toFixed(1)}%
              </p>
              <p className="text-sm text-gray-500">验证通过率</p>
            </div>
          </div>

          {/* 验证统计 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">成功验证</span>
              <div className="flex items-center space-x-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium">{creditInfo.successful_verifications}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">失败验证</span>
              <div className="flex items-center space-x-1">
                <XCircle className="w-4 h-4 text-red-500" />
                <span className="text-sm font-medium">{creditInfo.failed_verifications}</span>
              </div>
            </div>
          </div>

          {/* 违规记录 */}
          {creditInfo.violation_count > 0 && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">
                  违规记录: {creditInfo.violation_count} 次
                </span>
              </div>
              {creditInfo.last_violation_date && (
                <p className="text-xs text-red-600 mt-1">
                  最近违规: {new Date(creditInfo.last_violation_date).toLocaleDateString()}
                </p>
              )}
            </div>
          )}

          {/* 冻结积分 */}
          {creditInfo.frozen_points > 0 && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <Lock className="w-4 h-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800">
                  冻结积分: {creditInfo.frozen_points}
                </span>
              </div>
              <p className="text-xs text-yellow-600 mt-1">
                冻结的积分将在审核完成后处理
              </p>
            </div>
          )}
        </div>
      )}

      {/* 信誉度趋势提示 */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">信誉度趋势</span>
          <div className="flex items-center space-x-1">
            {creditInfo.reputation_score >= 70 ? (
              <>
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-green-600 font-medium">良好</span>
              </>
            ) : creditInfo.reputation_score >= 50 ? (
              <>
                <TrendingUp className="w-4 h-4 text-yellow-500" />
                <span className="text-yellow-600 font-medium">稳定</span>
              </>
            ) : (
              <>
                <TrendingDown className="w-4 h-4 text-red-500" />
                <span className="text-red-600 font-medium">需改善</span>
              </>
            )}
          </div>
        </div>
        
        {/* 改善建议 */}
        {creditInfo.reputation_score < 70 && (
          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
            💡 提示: 完成更多任务并保持高质量验证可以提升信誉度
          </div>
        )}
      </div>
    </div>
  );
};

export default CreditInfoCard;
