import { Router, Request, Response } from 'express';
import { identifyPlatform } from '@/utils/platformIdentifier';
import { asyncHandler } from '@/middleware/errorHandler';
import { ApiResponse } from '@/types';

const router = Router();

/**
 * @route   POST /api/utils/identify-platform
 * @desc    从给定的URL中识别平台
 * @access  Public
 */
router.post('/identify-platform', asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { url } = req.body;

  if (!url) {
    return res.status(400).json({ success: false, message: 'URL is required' });
  }

  const platform = identifyPlatform(url);

  if (platform === 'Unknown') {
    return res.json({ success: true, data: { platform: 'Unknown' }, message: 'Could not identify platform from URL' });
  }

  return res.json({ success: true, data: { platform } });
}));

export default router; 