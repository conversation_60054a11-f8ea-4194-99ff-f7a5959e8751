import { TaskType } from '@/types';
import { taskVerificationService, automatedDetectionService } from '@/services/taskVerificationService';
import { taskExecutionService } from '@/services/taskExecutionService';

export interface TestScenario {
  id: string;
  name: string;
  platform: string;
  requirements: TaskType[];
  duration: number;
  expectedResult: 'success' | 'failure' | 'partial';
  mockDetections?: Array<{
    action: TaskType;
    confidence: number;
    method: string;
  }>;
  description: string;
}

export interface TestResult {
  scenario: TestScenario;
  actualResult: {
    success: boolean;
    completedRequirements: TaskType[];
    confidence: number;
    method: string;
  };
  passed: boolean;
  details: string;
}

export class TaskDetectionTester {
  private testScenarios: TestScenario[] = [
    {
      id: 'bilibili_share_success',
      name: 'B站分享任务成功',
      platform: 'bilibili',
      requirements: ['share'],
      duration: 25000, // 25秒
      expectedResult: 'success',
      mockDetections: [
        { action: 'share', confidence: 85, method: 'modal_detection' }
      ],
      description: '用户在B站完成分享操作，检测到分享弹窗'
    },
    {
      id: 'douyin_like_quick',
      name: '抖音快速点赞',
      platform: 'douyin',
      requirements: ['like'],
      duration: 3000, // 3秒
      expectedResult: 'failure',
      description: '用户在抖音快速点赞，时间过短'
    },
    {
      id: 'bilibili_multi_task',
      name: 'B站多任务',
      platform: 'bilibili',
      requirements: ['like', 'share', 'follow'],
      duration: 45000, // 45秒
      expectedResult: 'partial',
      mockDetections: [
        { action: 'like', confidence: 90, method: 'button_state' },
        { action: 'share', confidence: 75, method: 'modal_detection' }
      ],
      description: '用户完成点赞和分享，但未完成关注'
    },
    {
      id: 'cross_domain_intelligent',
      name: '跨域智能推断',
      platform: 'douyin',
      requirements: ['share'],
      duration: 30000, // 30秒
      expectedResult: 'success',
      description: '跨域场景下基于时间和行为模式的智能推断'
    },
    {
      id: 'kuaishou_comment_long',
      name: '快手评论任务（长时间）',
      platform: 'kuaishou',
      requirements: ['comment'],
      duration: 120000, // 2分钟
      expectedResult: 'success',
      description: '用户花费较长时间完成评论任务'
    },
    {
      id: 'share_no_detection',
      name: '分享任务无检测',
      platform: 'bilibili',
      requirements: ['share'],
      duration: 15000, // 15秒
      expectedResult: 'failure',
      description: '用户声称完成分享但未检测到任何操作'
    }
  ];

  /**
   * 运行所有测试场景
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 开始运行任务检测测试...');
    const results: TestResult[] = [];

    for (const scenario of this.testScenarios) {
      console.log(`\n📋 测试场景: ${scenario.name}`);
      const result = await this.runTestScenario(scenario);
      results.push(result);
      
      console.log(`${result.passed ? '✅' : '❌'} ${scenario.name}: ${result.passed ? '通过' : '失败'}`);
      if (!result.passed) {
        console.log(`   详情: ${result.details}`);
      }
    }

    this.printTestSummary(results);
    return results;
  }

  /**
   * 运行单个测试场景
   */
  private async runTestScenario(scenario: TestScenario): Promise<TestResult> {
    try {
      // 创建模拟会话
      const mockSession = {
        id: `test_${scenario.id}`,
        taskId: 1,
        selectedRequirements: scenario.requirements,
        windowRef: null,
        startTime: Date.now() - scenario.duration,
        minDuration: 5000,
        status: 'completed' as const,
        platform: scenario.platform,
        isTrackingBehavior: false
      };

      // 模拟检测结果
      if (scenario.mockDetections) {
        this.mockDetectionResults(mockSession.id, scenario);
      }

      // 执行验证
      const verificationResult = await (taskExecutionService as any).verifyTaskCompletion(mockSession);

      // 分析结果
      const actualResult = {
        success: verificationResult.success,
        completedRequirements: verificationResult.completedRequirements,
        confidence: this.extractConfidence(verificationResult.message),
        method: this.extractMethod(verificationResult.message)
      };

      // 判断测试是否通过
      const passed = this.evaluateTestResult(scenario, actualResult);

      return {
        scenario,
        actualResult,
        passed,
        details: this.generateTestDetails(scenario, actualResult, passed)
      };

    } catch (error) {
      return {
        scenario,
        actualResult: {
          success: false,
          completedRequirements: [],
          confidence: 0,
          method: 'error'
        },
        passed: false,
        details: `测试执行出错: ${error.message}`
      };
    }
  }

  /**
   * 模拟检测结果
   */
  private mockDetectionResults(sessionId: string, scenario: TestScenario): void {
    if (!scenario.mockDetections) return;

    const mockResults = {
      detectedActions: scenario.mockDetections.map(detection => ({
        action: detection.action,
        platform: scenario.platform,
        timestamp: Date.now(),
        confidence: detection.confidence,
        method: detection.method
      })),
      confidence: Math.max(...scenario.mockDetections.map(d => d.confidence)),
      method: 'direct_detection',
      platform: scenario.platform
    };

    // 直接设置检测结果
    (automatedDetectionService as any).detectionResults.set(sessionId, mockResults);
  }

  /**
   * 评估测试结果
   */
  private evaluateTestResult(scenario: TestScenario, actualResult: any): boolean {
    switch (scenario.expectedResult) {
      case 'success':
        return actualResult.success && actualResult.completedRequirements.length === scenario.requirements.length;
      
      case 'failure':
        return !actualResult.success;
      
      case 'partial':
        return actualResult.success && 
               actualResult.completedRequirements.length > 0 && 
               actualResult.completedRequirements.length < scenario.requirements.length;
      
      default:
        return false;
    }
  }

  /**
   * 生成测试详情
   */
  private generateTestDetails(scenario: TestScenario, actualResult: any, passed: boolean): string {
    if (passed) {
      return `测试通过 - 预期: ${scenario.expectedResult}, 实际: ${actualResult.success ? 'success' : 'failure'}`;
    }

    return `测试失败 - 预期: ${scenario.expectedResult}, 实际: ${actualResult.success ? 'success' : 'failure'}, ` +
           `完成任务: ${actualResult.completedRequirements.join(', ') || '无'}, ` +
           `置信度: ${actualResult.confidence}%`;
  }

  /**
   * 从消息中提取置信度
   */
  private extractConfidence(message: string): number {
    const match = message.match(/置信度[：:]?\s*(\d+)%/);
    return match ? parseInt(match[1]) : 0;
  }

  /**
   * 从消息中提取方法
   */
  private extractMethod(message: string): string {
    if (message.includes('智能验证') || message.includes('智能推断')) return 'intelligent';
    if (message.includes('自动验证') || message.includes('检测')) return 'automated';
    if (message.includes('时间验证')) return 'time_based';
    return 'unknown';
  }

  /**
   * 打印测试摘要
   */
  private printTestSummary(results: TestResult[]): void {
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    const passRate = Math.round((passed / total) * 100);

    console.log('\n📊 测试摘要:');
    console.log(`总测试数: ${total}`);
    console.log(`通过数: ${passed}`);
    console.log(`失败数: ${total - passed}`);
    console.log(`通过率: ${passRate}%`);

    if (passRate < 80) {
      console.log('⚠️  通过率低于80%，建议检查检测逻辑');
    } else {
      console.log('✅ 检测系统运行良好');
    }
  }

  /**
   * 运行特定平台的测试
   */
  async runPlatformTests(platform: string): Promise<TestResult[]> {
    const platformScenarios = this.testScenarios.filter(s => s.platform === platform);
    const results: TestResult[] = [];

    for (const scenario of platformScenarios) {
      const result = await this.runTestScenario(scenario);
      results.push(result);
    }

    return results;
  }
}

export const taskDetectionTester = new TaskDetectionTester();
