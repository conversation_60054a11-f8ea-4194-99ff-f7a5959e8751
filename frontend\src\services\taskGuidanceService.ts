import { TaskType } from '@/types';

export interface GuidanceMessage {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: number;
  duration?: number; // 显示时长（毫秒）
}

export interface DetectionFeedback {
  action: TaskType;
  detected: boolean;
  confidence: number;
  method: string;
  timestamp: number;
}

export class TaskGuidanceService {
  private listeners: Set<(message: GuidanceMessage) => void> = new Set();
  private detectionListeners: Set<(feedback: DetectionFeedback) => void> = new Set();
  private activeSession: string | null = null;

  /**
   * 开始引导会话
   */
  startGuidanceSession(sessionId: string, platform: string, requirements: TaskType[]): void {
    this.activeSession = sessionId;
    
    // 发送欢迎消息
    this.sendMessage({
      type: 'info',
      title: '任务开始',
      message: `正在监控您在${this.getPlatformName(platform)}上的操作，请按照要求完成任务。`,
      duration: 5000
    });

    // 发送具体指导
    setTimeout(() => {
      this.sendTaskGuidance(platform, requirements);
    }, 1000);

    // 设置定期提醒
    this.setupPeriodicReminders(requirements);
  }

  /**
   * 结束引导会话
   */
  endGuidanceSession(): void {
    this.activeSession = null;
    this.sendMessage({
      type: 'info',
      title: '检测结束',
      message: '任务检测已完成，正在处理结果...',
      duration: 3000
    });
  }

  /**
   * 报告检测结果
   */
  reportDetection(feedback: DetectionFeedback): void {
    if (!this.activeSession) return;

    // 通知检测监听器
    this.detectionListeners.forEach(listener => listener(feedback));

    // 发送成功消息
    if (feedback.detected && feedback.confidence >= 60) {
      this.sendMessage({
        type: 'success',
        title: '操作检测成功',
        message: `✅ 已检测到${this.getActionName(feedback.action)}操作（置信度：${feedback.confidence}%）`,
        duration: 4000
      });
    }
  }

  /**
   * 发送警告消息
   */
  sendWarning(title: string, message: string): void {
    this.sendMessage({
      type: 'warning',
      title,
      message,
      duration: 6000
    });
  }

  /**
   * 发送错误消息
   */
  sendError(title: string, message: string): void {
    this.sendMessage({
      type: 'error',
      title,
      message,
      duration: 8000
    });
  }

  /**
   * 添加消息监听器
   */
  addMessageListener(listener: (message: GuidanceMessage) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * 添加检测监听器
   */
  addDetectionListener(listener: (feedback: DetectionFeedback) => void): () => void {
    this.detectionListeners.add(listener);
    return () => this.detectionListeners.delete(listener);
  }

  /**
   * 发送消息
   */
  private sendMessage(message: Omit<GuidanceMessage, 'id' | 'timestamp'>): void {
    const fullMessage: GuidanceMessage = {
      ...message,
      id: this.generateId(),
      timestamp: Date.now()
    };

    this.listeners.forEach(listener => listener(fullMessage));
  }

  /**
   * 发送任务指导
   */
  private sendTaskGuidance(platform: string, requirements: TaskType[]): void {
    const platformName = this.getPlatformName(platform);
    const actions = requirements.map(req => this.getActionName(req)).join('、');
    
    this.sendMessage({
      type: 'info',
      title: '操作指导',
      message: `请在${platformName}页面完成以下操作：${actions}。完成后系统会自动检测。`,
      duration: 8000
    });

    // 发送具体的操作指导
    requirements.forEach((req, index) => {
      setTimeout(() => {
        this.sendSpecificGuidance(platform, req);
      }, (index + 1) * 2000);
    });
  }

  /**
   * 发送具体操作指导
   */
  private sendSpecificGuidance(platform: string, action: TaskType): void {
    const guidance = this.getActionGuidance(platform, action);
    this.sendMessage({
      type: 'info',
      title: `${this.getActionName(action)}指导`,
      message: guidance,
      duration: 6000
    });
  }

  /**
   * 设置定期提醒
   */
  private setupPeriodicReminders(requirements: TaskType[]): void {
    // 30秒后提醒
    setTimeout(() => {
      if (this.activeSession) {
        this.sendMessage({
          type: 'info',
          title: '进度提醒',
          message: '如果您已完成操作但未检测到，请稍等片刻或尝试重新操作。',
          duration: 5000
        });
      }
    }, 30000);

    // 60秒后警告
    setTimeout(() => {
      if (this.activeSession) {
        this.sendWarning(
          '检测提醒',
          '如果长时间未检测到操作，建议关闭窗口并提供完成截图作为证明。'
        );
      }
    }, 60000);
  }

  /**
   * 获取平台名称
   */
  private getPlatformName(platform: string): string {
    const names = {
      bilibili: 'B站',
      douyin: '抖音',
      kuaishou: '快手',
      xiaohongshu: '小红书',
      youtube: 'YouTube'
    };
    return names[platform] || platform;
  }

  /**
   * 获取操作名称
   */
  private getActionName(action: TaskType): string {
    const names = {
      like: '点赞',
      share: '分享',
      comment: '评论',
      follow: '关注'
    };
    return names[action] || action;
  }

  /**
   * 获取操作指导
   */
  private getActionGuidance(platform: string, action: TaskType): string {
    const guidance = {
      bilibili: {
        like: '点击视频播放器下方的点赞按钮（❤️图标），按钮会变红表示成功',
        share: '点击分享按钮，在弹出的分享面板中选择任意分享方式',
        comment: '在视频下方评论区输入评论内容并点击发送',
        follow: '点击UP主头像旁边的"关注"按钮'
      },
      douyin: {
        like: '点击视频右侧的红心按钮，按钮会变红并有动画效果',
        share: '点击右侧的分享按钮，选择分享到任意平台',
        comment: '点击评论图标，在弹出的评论框中输入内容',
        follow: '点击作者头像，然后点击"关注"按钮'
      },
      kuaishou: {
        like: '点击视频右侧的点赞按钮（双击视频也可以点赞）',
        share: '点击分享按钮，选择分享方式',
        comment: '在评论区输入评论并发送',
        follow: '点击作者头像旁的关注按钮'
      }
    };

    return guidance[platform]?.[action] || `请完成${this.getActionName(action)}操作`;
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `guidance_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const taskGuidanceService = new TaskGuidanceService();
