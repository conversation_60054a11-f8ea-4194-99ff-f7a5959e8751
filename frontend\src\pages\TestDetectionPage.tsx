import React, { useState } from 'react';
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock, 
  BarChart3,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  AlertTriangle
} from 'lucide-react';
import { taskDetectionTester, TestResult } from '@/utils/taskDetectionTester';

const TestDetectionPage: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');

  const platforms = [
    { value: 'all', label: '全部平台' },
    { value: 'bilibili', label: 'B站' },
    { value: 'douyin', label: '抖音' },
    { value: 'kuaishou', label: '快手' },
    { value: 'xiaohongshu', label: '小红书' }
  ];

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      let results: TestResult[];
      
      if (selectedPlatform === 'all') {
        results = await taskDetectionTester.runAllTests();
      } else {
        results = await taskDetectionTester.runPlatformTests(selectedPlatform);
      }
      
      setTestResults(results);
    } catch (error) {
      console.error('测试运行失败:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getResultIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    );
  };

  const getResultColor = (passed: boolean) => {
    return passed ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
  };

  const calculateStats = () => {
    if (testResults.length === 0) return null;

    const passed = testResults.filter(r => r.passed).length;
    const total = testResults.length;
    const passRate = Math.round((passed / total) * 100);

    return { passed, total, passRate };
  };

  const stats = calculateStats();

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">任务检测系统测试</h1>
          <p className="text-gray-600 mt-1">验证视频分享任务检测机制的可靠性</p>
        </div>
      </div>

      {/* 测试控制面板 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">测试配置</h2>
        
        <div className="flex items-center space-x-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择测试平台
            </label>
            <select
              value={selectedPlatform}
              onChange={(e) => setSelectedPlatform(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              disabled={isRunning}
            >
              {platforms.map(platform => (
                <option key={platform.value} value={platform.value}>
                  {platform.label}
                </option>
              ))}
            </select>
          </div>

          <div className="flex-1" />

          <button
            onClick={runTests}
            disabled={isRunning}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? (
              <>
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span>运行中...</span>
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                <span>开始测试</span>
              </>
            )}
          </button>
        </div>

        {/* 测试统计 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <BarChart3 className="w-5 h-5 text-blue-500 mr-2" />
                <span className="text-sm font-medium text-blue-900">总测试数</span>
              </div>
              <p className="text-2xl font-bold text-blue-900 mt-1">{stats.total}</p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                <span className="text-sm font-medium text-green-900">通过数</span>
              </div>
              <p className="text-2xl font-bold text-green-900 mt-1">{stats.passed}</p>
            </div>

            <div className={`border rounded-lg p-4 ${
              stats.passRate >= 80 ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'
            }`}>
              <div className="flex items-center">
                {stats.passRate >= 80 ? (
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-yellow-500 mr-2" />
                )}
                <span className={`text-sm font-medium ${
                  stats.passRate >= 80 ? 'text-green-900' : 'text-yellow-900'
                }`}>
                  通过率
                </span>
              </div>
              <p className={`text-2xl font-bold mt-1 ${
                stats.passRate >= 80 ? 'text-green-900' : 'text-yellow-900'
              }`}>
                {stats.passRate}%
              </p>
            </div>
          </div>
        )}
      </div>

      {/* 测试结果 */}
      {testResults.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">测试结果</h2>
          </div>
          
          <div className="divide-y divide-gray-200">
            {testResults.map((result, index) => (
              <div key={result.scenario.id} className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 mt-1">
                    {getResultIcon(result.passed)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-medium text-gray-900">
                        {result.scenario.name}
                      </h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        result.passed 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {result.passed ? '通过' : '失败'}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">
                      {result.scenario.description}
                    </p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-gray-700">测试参数</h4>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p>平台: {result.scenario.platform}</p>
                          <p>任务类型: {result.scenario.requirements.join(', ')}</p>
                          <p>执行时长: {Math.round(result.scenario.duration / 1000)}秒</p>
                          <p>预期结果: {result.scenario.expectedResult}</p>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-gray-700">实际结果</h4>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p>验证状态: {result.actualResult.success ? '成功' : '失败'}</p>
                          <p>完成任务: {result.actualResult.completedRequirements.join(', ') || '无'}</p>
                          <p>置信度: {result.actualResult.confidence}%</p>
                          <p>检测方法: {result.actualResult.method}</p>
                        </div>
                      </div>
                    </div>
                    
                    {!result.passed && (
                      <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-800">
                          <strong>失败原因:</strong> {result.details}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 测试说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">测试说明</h3>
        <div className="text-sm text-blue-800 space-y-2">
          <p>• 本测试工具用于验证视频分享任务检测机制的可靠性和准确性</p>
          <p>• 测试覆盖不同平台、任务类型和执行时长的场景</p>
          <p>• 包括成功检测、失败检测和部分检测的情况</p>
          <p>• 通过率建议保持在80%以上以确保系统可靠性</p>
        </div>
      </div>
    </div>
  );
};

export default TestDetectionPage;
