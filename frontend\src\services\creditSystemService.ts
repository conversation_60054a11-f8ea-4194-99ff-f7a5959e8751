import api from './api';

export interface UserCreditInfo {
  user_id: number;
  reputation_score: number; // 0-100
  trust_level: 'low' | 'medium' | 'high' | 'verified';
  total_tasks_completed: number;
  successful_verifications: number;
  failed_verifications: number;
  violation_count: number;
  frozen_points: number;
  last_violation_date?: string;
  account_status: 'active' | 'restricted' | 'suspended';
}

export interface ViolationRecord {
  id: number;
  user_id: number;
  violation_type: 'fake_completion' | 'spam_behavior' | 'suspicious_activity' | 'multiple_accounts';
  description: string;
  evidence?: string[];
  points_deducted: number;
  created_at: string;
  status: 'pending' | 'confirmed' | 'dismissed';
  reviewed_by?: number;
  reviewed_at?: string;
}

export interface AppealRequest {
  id: number;
  user_id: number;
  violation_id: number;
  reason: string;
  evidence?: File[];
  status: 'pending' | 'approved' | 'rejected';
  submitted_at: string;
  reviewed_at?: string;
  admin_response?: string;
}

export interface TaskVerification {
  id: number;
  task_execution_id: number;
  verification_type: 'automatic' | 'manual' | 'screenshot' | 'api_check';
  status: 'pending' | 'passed' | 'failed' | 'requires_review';
  confidence_score: number; // 0-100
  verification_data?: any;
  verified_at?: string;
  verified_by?: number;
}

export interface FraudDetectionResult {
  is_suspicious: boolean;
  risk_score: number; // 0-100
  detected_patterns: string[];
  recommended_action: 'allow' | 'review' | 'block';
  confidence: number;
}

class CreditSystemService {
  /**
   * 获取用户信用信息
   */
  async getUserCreditInfo(userId: number): Promise<UserCreditInfo> {
    try {
      const response = await api.get(`/credit/user/${userId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取信用信息失败');
    }
  }

  /**
   * 更新用户信誉度
   */
  async updateReputationScore(userId: number, change: number, reason: string): Promise<UserCreditInfo> {
    try {
      const response = await api.post(`/credit/reputation/${userId}`, {
        change,
        reason
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新信誉度失败');
    }
  }

  /**
   * 检测可疑行为
   */
  async detectFraudulentBehavior(
    userId: number, 
    taskId: number, 
    executionData: any
  ): Promise<FraudDetectionResult> {
    try {
      const response = await api.post('/credit/fraud-detection', {
        user_id: userId,
        task_id: taskId,
        execution_data: executionData
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '行为检测失败');
    }
  }

  /**
   * 记录违规行为
   */
  async recordViolation(
    userId: number,
    violationType: ViolationRecord['violation_type'],
    description: string,
    evidence?: string[],
    pointsDeducted?: number
  ): Promise<ViolationRecord> {
    try {
      const response = await api.post('/credit/violations', {
        user_id: userId,
        violation_type: violationType,
        description,
        evidence,
        points_deducted: pointsDeducted
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '记录违规失败');
    }
  }

  /**
   * 获取用户违规记录
   */
  async getUserViolations(userId: number): Promise<ViolationRecord[]> {
    try {
      const response = await api.get(`/credit/violations/user/${userId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取违规记录失败');
    }
  }

  /**
   * 冻结用户积分
   */
  async freezeUserPoints(userId: number, amount: number, reason: string): Promise<void> {
    try {
      await api.post(`/credit/freeze-points/${userId}`, {
        amount,
        reason
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '冻结积分失败');
    }
  }

  /**
   * 解冻用户积分
   */
  async unfreezeUserPoints(userId: number, amount: number, reason: string): Promise<void> {
    try {
      await api.post(`/credit/unfreeze-points/${userId}`, {
        amount,
        reason
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '解冻积分失败');
    }
  }

  /**
   * 扣除用户积分（惩罚）
   */
  async deductPoints(userId: number, amount: number, reason: string): Promise<void> {
    try {
      await api.post(`/credit/deduct-points/${userId}`, {
        amount,
        reason
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '扣除积分失败');
    }
  }

  /**
   * 提交申诉
   */
  async submitAppeal(
    violationId: number,
    reason: string,
    evidence?: File[]
  ): Promise<AppealRequest> {
    try {
      const formData = new FormData();
      formData.append('violation_id', violationId.toString());
      formData.append('reason', reason);
      
      if (evidence) {
        evidence.forEach((file, index) => {
          formData.append(`evidence_${index}`, file);
        });
      }

      const response = await api.post('/credit/appeals', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '提交申诉失败');
    }
  }

  /**
   * 获取用户申诉记录
   */
  async getUserAppeals(userId: number): Promise<AppealRequest[]> {
    try {
      const response = await api.get(`/credit/appeals/user/${userId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取申诉记录失败');
    }
  }

  /**
   * 验证任务完成
   */
  async verifyTaskCompletion(
    taskExecutionId: number,
    verificationType: TaskVerification['verification_type'],
    verificationData?: any
  ): Promise<TaskVerification> {
    try {
      const response = await api.post('/credit/verify-task', {
        task_execution_id: taskExecutionId,
        verification_type: verificationType,
        verification_data: verificationData
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '任务验证失败');
    }
  }

  /**
   * 获取任务验证记录
   */
  async getTaskVerifications(taskExecutionId: number): Promise<TaskVerification[]> {
    try {
      const response = await api.get(`/credit/verifications/${taskExecutionId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取验证记录失败');
    }
  }

  /**
   * 计算用户信誉度
   */
  calculateReputationScore(creditInfo: UserCreditInfo): number {
    const {
      total_tasks_completed,
      successful_verifications,
      failed_verifications,
      violation_count
    } = creditInfo;

    if (total_tasks_completed === 0) return 50; // 新用户默认50分

    const successRate = successful_verifications / (successful_verifications + failed_verifications);
    const violationPenalty = Math.min(violation_count * 10, 50); // 每次违规扣10分，最多扣50分
    const experienceBonus = Math.min(total_tasks_completed * 0.1, 20); // 经验加分，最多20分

    const baseScore = 50;
    const finalScore = Math.max(0, Math.min(100, 
      baseScore + (successRate * 30) + experienceBonus - violationPenalty
    ));

    return Math.round(finalScore);
  }

  /**
   * 获取信任等级
   */
  getTrustLevel(reputationScore: number): UserCreditInfo['trust_level'] {
    if (reputationScore >= 90) return 'verified';
    if (reputationScore >= 70) return 'high';
    if (reputationScore >= 50) return 'medium';
    return 'low';
  }

  /**
   * 检查用户是否可以执行任务
   */
  canUserExecuteTask(creditInfo: UserCreditInfo, taskValue: number): {
    allowed: boolean;
    reason?: string;
    restrictions?: string[];
  } {
    const restrictions: string[] = [];

    // 检查账户状态
    if (creditInfo.account_status === 'suspended') {
      return {
        allowed: false,
        reason: '账户已被暂停，无法执行任务'
      };
    }

    if (creditInfo.account_status === 'restricted') {
      restrictions.push('账户受限，只能执行低价值任务');
    }

    // 检查信誉度限制
    if (creditInfo.reputation_score < 30) {
      return {
        allowed: false,
        reason: '信誉度过低，请提高信誉度后再试'
      };
    }

    // 高价值任务需要更高信誉度
    if (taskValue > 50 && creditInfo.reputation_score < 70) {
      return {
        allowed: false,
        reason: '此任务需要更高的信誉度才能执行'
      };
    }

    // 低信誉度用户需要额外验证
    if (creditInfo.reputation_score < 50) {
      restrictions.push('需要提供额外的完成证明');
    }

    return {
      allowed: true,
      restrictions: restrictions.length > 0 ? restrictions : undefined
    };
  }
}

export const creditSystemService = new CreditSystemService();
