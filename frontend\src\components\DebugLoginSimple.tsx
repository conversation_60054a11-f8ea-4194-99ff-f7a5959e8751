import React, { useState } from 'react';
import { X, User, Settings } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';

const DebugLoginSimple: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { login, isAuthenticated, user } = useAuthStore();

  const testAccounts = [
    {
      username: 'demo_user',
      label: '演示用户',
    },
    {
      username: 'test_user', 
      label: '测试用户',
    },
    {
      username: 'admin_user',
      label: '管理员',
    },
  ];

  const handleTestLogin = (account: typeof testAccounts[0]) => {
    const mockUser = {
      id: 1,
      username: account.username,
      email: `${account.username}@example.com`,
      points_balance: 150,
      email_verified: true,
      social_accounts: {},
      created_at: new Date().toISOString(),
    };
    
    const mockTokens = {
      accessToken: `mock_access_token_${Date.now()}`,
      refreshToken: `mock_refresh_token_${Date.now()}`,
    };
    
    login(mockUser, mockTokens);
    setIsOpen(false);
    
    // 直接跳转
    setTimeout(() => {
      window.location.href = '/dashboard';
    }, 100);
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsOpen(true)}
          className="bg-red-500 hover:bg-red-600 text-white p-3 rounded-full shadow-lg transition-colors"
          title="调试登录"
        >
          <Settings className="h-5 w-5" />
        </button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">调试登录</h3>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <p className="text-sm text-gray-600 mb-4">
          选择一个用户直接登录
        </p>

        {isAuthenticated && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="text-sm text-green-800">
              已登录: {user?.username}
            </div>
          </div>
        )}

        <div className="space-y-3">
          {testAccounts.map((account, index) => (
            <button
              key={index}
              onClick={() => handleTestLogin(account)}
              className="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center">
                <User className="h-8 w-8 text-gray-400 mr-3" />
                <div>
                  <div className="font-medium text-gray-900">{account.label}</div>
                  <div className="text-sm text-gray-500">用户名: {account.username}</div>
                </div>
              </div>
            </button>
          ))}
        </div>

        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <button
            onClick={() => {
              window.location.href = '/dashboard';
            }}
            className="w-full p-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm"
          >
            直接跳转到首页
          </button>
        </div>
      </div>
    </div>
  );
};

export default DebugLoginSimple;
