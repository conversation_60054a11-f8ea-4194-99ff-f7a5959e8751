import { TaskType, TaskExecution, ExecutionStatus } from '@/types';
import api from './api';
import { taskVerificationService, automatedDetectionService, VerificationEvidence, VerificationResult } from './taskVerificationService';
import { behaviorTrackingService } from './behaviorTrackingService';
import { taskGuidanceService } from './taskGuidanceService';
import { crossDomainClickDetector, ClickDetectionResult } from './crossDomainClickDetector';
import { browserExtensionDetector, ExtensionDetectionData } from './browserExtensionDetector';
import { realCrossDomainDetector, RealClickData } from './realCrossDomainDetector';
import { detectionDiagnosticsService } from './detectionDiagnostics';

export interface TaskExecutionSession {
  id: string;
  taskId: number;
  selectedRequirements: TaskType[];
  windowRef: Window | null;
  startTime: number;
  minDuration: number; // 最小停留时间（毫秒）
  status: 'pending' | 'monitoring' | 'completed' | 'failed' | 'timeout';
  platform?: string; // 平台信息
  isTrackingBehavior?: boolean; // 是否正在跟踪行为
  userConfirmedCompletion?: boolean; // 用户是否确认完成
  windowActivityData?: { // 窗口活动数据
    urlChangeCount: number;
    focusChangeCount: number;
    windowActiveTime: number;
    userInteractionIndicators: number;
    totalDuration: number;
  };
  clickDetectionData?: ClickDetectionResult; // 点击检测数据
  extensionDetectionData?: ExtensionDetectionData; // 扩展式检测数据
  realDetectionData?: RealClickData; // 真实检测数据
  detectionMethod?: 'extension' | 'real' | 'legacy' | 'hybrid'; // 使用的检测方法
}

export interface ExecutionResult {
  success: boolean;
  completedRequirements: TaskType[];
  failedRequirements: TaskType[];
  pointsEarned: number;
  message: string;
  needsManualReview?: boolean;
  proofRequired?: boolean;
}

export interface ExecutionProof {
  screenshots?: File[];
  description?: string;
  timestamp: number;
}

class TaskExecutionService {
  private activeSessions = new Map<string, TaskExecutionSession>();
  private readonly MIN_TASK_DURATION = 3000; // 3秒最小停留时间（降低要求）
  private readonly TRUSTED_USER_MIN_DURATION = 2000; // 信誉用户2秒最小时间
  private readonly MAX_SESSION_TIMEOUT = 300000; // 5分钟超时
  private readonly QUICK_COMPLETION_THRESHOLD = 5000; // 5秒内完成视为快速完成

  /**
   * 获取用户最小执行时间（基于信誉度）
   */
  private getMinDurationForUser(userReputationScore?: number): number {
    // 如果用户信誉度高于70分，使用更宽松的时间要求
    if (userReputationScore && userReputationScore >= 70) {
      return this.TRUSTED_USER_MIN_DURATION;
    }
    return this.MIN_TASK_DURATION;
  }

  /**
   * 检查是否为可信用户
   */
  private isTrustedUser(userReputationScore?: number): boolean {
    return userReputationScore !== undefined && userReputationScore >= 70;
  }

  /**
   * 检测视频平台
   */
  private detectPlatform(videoUrl: string): string {
    const url = videoUrl.toLowerCase();
    if (url.includes('bilibili.com') || url.includes('b23.tv')) {
      return 'bilibili';
    } else if (url.includes('douyin.com')) {
      return 'douyin';
    } else if (url.includes('kuaishou.com')) {
      return 'kuaishou';
    } else if (url.includes('xiaohongshu.com')) {
      return 'xiaohongshu';
    }
    return 'unknown';
  }

  /**
   * 初始化行为跟踪
   */
  private initializeBehaviorTracking(session: TaskExecutionSession): void {
    // 等待窗口加载后开始跟踪
    setTimeout(() => {
      try {
        if (session.windowRef && !session.windowRef.closed) {
          // 检查是否为同域窗口
          try {
            // 尝试访问窗口的document来检测是否为同域
            const testAccess = session.windowRef.document;
            behaviorTrackingService.startTracking(session.windowRef);
            session.isTrackingBehavior = true;
            console.log('同域窗口，启动行为跟踪');
          } catch (crossOriginError) {
            // 跨域窗口，无法进行行为跟踪
            console.log('跨域窗口，无法进行行为跟踪，将使用替代验证方法');
            session.isTrackingBehavior = false;
          }
        }
      } catch (error) {
        console.warn('无法启动行为跟踪:', error);
        session.isTrackingBehavior = false;
      }
    }, 2000); // 等待2秒让页面加载
  }

  /**
   * 初始化自动检测
   */
  private initializeAutomatedDetection(session: TaskExecutionSession): void {
    setTimeout(() => {
      try {
        if (session.windowRef && !session.windowRef.closed && session.platform) {
          automatedDetectionService.injectDetectionScript(
            session.windowRef,
            session.platform,
            session.id
          );
          console.log(`自动检测脚本已注入到 ${session.platform} 平台`);
        }
      } catch (error) {
        console.warn('注入自动检测脚本失败:', error);
      }
    }, 3000); // 等待3秒让页面完全加载
  }

  /**
   * 初始化高级检测系统（扩展式 + 真实检测 + 传统检测）
   */
  private initializeAdvancedDetection(session: TaskExecutionSession): void {
    if (!session.windowRef || !session.platform) {
      console.warn('无法初始化检测：缺少窗口引用或平台信息');
      return;
    }

    console.log(`🚀 启动高级检测系统 - 平台: ${session.platform}, 任务: ${session.selectedRequirements.join(', ')}`);

    // 方案1: 扩展式检测器（最高优先级）
    this.startExtensionDetection(session);

    // 方案2: 真实跨域检测器（备用方案）
    this.startRealDetection(session);

    // 方案3: 传统检测器（兜底方案）
    this.startLegacyDetection(session);

    // 设置检测方法选择逻辑
    this.setupDetectionMethodSelection(session);
  }

  /**
   * 启动扩展式检测
   */
  private startExtensionDetection(session: TaskExecutionSession): void {
    setTimeout(() => {
      try {
        browserExtensionDetector.startExtensionDetection(
          session.id,
          session.windowRef!,
          session.platform!,
          session.selectedRequirements
        );

        session.detectionMethod = 'extension';
        console.log(`✅ 扩展式检测已启动 - 会话: ${session.id}`);
      } catch (error) {
        console.warn('扩展式检测启动失败:', error);
        this.fallbackToRealDetection(session);
      }
    }, 1000); // 1秒后启动
  }

  /**
   * 启动真实检测
   */
  private startRealDetection(session: TaskExecutionSession): void {
    setTimeout(() => {
      try {
        realCrossDomainDetector.startRealDetection(
          session.id,
          session.windowRef!,
          session.platform!,
          session.selectedRequirements
        );

        console.log(`✅ 真实检测已启动 - 会话: ${session.id}`);
      } catch (error) {
        console.warn('真实检测启动失败:', error);
      }
    }, 2000); // 2秒后启动
  }

  /**
   * 启动传统检测（兜底方案）
   */
  private startLegacyDetection(session: TaskExecutionSession): void {
    setTimeout(() => {
      try {
        // 启动传统的自动检测
        automatedDetectionService.injectDetectionScript(
          session.windowRef!,
          session.platform!,
          session.id
        );

        // 启动传统的点击检测
        crossDomainClickDetector.startClickDetection(
          session.id,
          session.windowRef!,
          session.platform!,
          session.selectedRequirements
        );

        console.log(`✅ 传统检测已启动 - 会话: ${session.id}`);
      } catch (error) {
        console.warn('传统检测启动失败:', error);
      }
    }, 3000); // 3秒后启动
  }

  /**
   * 回退到真实检测
   */
  private fallbackToRealDetection(session: TaskExecutionSession): void {
    if (session.detectionMethod !== 'real') {
      session.detectionMethod = 'real';
      console.log(`🔄 回退到真实检测模式 - 会话: ${session.id}`);
    }
  }

  /**
   * 设置检测方法选择逻辑
   */
  private setupDetectionMethodSelection(session: TaskExecutionSession): void {
    // 5秒后检查哪种检测方法效果最好
    setTimeout(() => {
      this.selectBestDetectionMethod(session);
    }, 5000);

    // 10秒后再次检查并确定最终方法
    setTimeout(() => {
      this.finalizeDetectionMethod(session);
    }, 10000);
  }

  /**
   * 选择最佳检测方法
   */
  private selectBestDetectionMethod(session: TaskExecutionSession): void {
    const extensionData = browserExtensionDetector.getExtensionDetectionResult(session.id);
    const realData = realCrossDomainDetector.getRealDetectionResult(session.id);
    const legacyData = crossDomainClickDetector.getDetectionResult(session.id);

    console.log(`🔍 检测方法评估 - 会话: ${session.id}`);
    console.log(`扩展式检测:`, extensionData);
    console.log(`真实检测:`, realData);
    console.log(`传统检测:`, legacyData);

    // 根据数据质量选择最佳方法
    if (extensionData && extensionData.confidence > 80) {
      session.detectionMethod = 'extension';
      console.log(`✅ 选择扩展式检测 - 置信度: ${extensionData.confidence}%`);
    } else if (realData && realData.confidence > 70) {
      session.detectionMethod = 'real';
      console.log(`✅ 选择真实检测 - 置信度: ${realData.confidence}%`);
    } else if (legacyData && legacyData.totalClicks > 0) {
      session.detectionMethod = 'legacy';
      console.log(`✅ 选择传统检测 - 点击数: ${legacyData.totalClicks}`);
    } else {
      session.detectionMethod = 'hybrid';
      console.log(`✅ 选择混合检测模式`);
    }
  }

  /**
   * 确定最终检测方法
   */
  private finalizeDetectionMethod(session: TaskExecutionSession): void {
    console.log(`🎯 最终检测方法: ${session.detectionMethod} - 会话: ${session.id}`);

    // 保存检测数据到会话
    session.extensionDetectionData = browserExtensionDetector.getExtensionDetectionResult(session.id);
    session.realDetectionData = realCrossDomainDetector.getRealDetectionResult(session.id);
    session.clickDetectionData = crossDomainClickDetector.getDetectionResult(session.id);
  }

  /**
   * 开始任务执行会话
   */
  async startExecution(
    taskId: number,
    videoUrl: string,
    selectedRequirements: TaskType[],
    userReputationScore?: number
  ): Promise<TaskExecutionSession> {
    const sessionId = this.generateSessionId();

    // 检测平台
    const platform = this.detectPlatform(videoUrl);

    // 打开新窗口
    const windowRef = window.open(videoUrl, '_blank', 'width=1200,height=800');

    if (!windowRef) {
      throw new Error('无法打开任务窗口，请检查浏览器弹窗设置');
    }

    const minDuration = this.getMinDurationForUser(userReputationScore);
    const session: TaskExecutionSession = {
      id: sessionId,
      taskId,
      selectedRequirements,
      windowRef,
      startTime: Date.now(),
      minDuration,
      status: 'monitoring',
      platform,
      isTrackingBehavior: false
    };

    this.activeSessions.set(sessionId, session);

    // 开始用户引导
    taskGuidanceService.startGuidanceSession(sessionId, platform, selectedRequirements);

    // 开始监控窗口状态
    this.monitorWindow(session);

    // 尝试开始行为跟踪（需要等待窗口加载）
    this.initializeBehaviorTracking(session);

    // 注入自动检测脚本
    this.initializeAutomatedDetection(session);

    // 启动新的扩展式检测系统
    this.initializeAdvancedDetection(session);

    // 设置超时处理
    setTimeout(() => {
      this.handleSessionTimeout(sessionId);
    }, this.MAX_SESSION_TIMEOUT);

    return session;
  }

  /**
   * 监控窗口状态（增强版 - 跨域友好）
   */
  private monitorWindow(session: TaskExecutionSession): void {
    let lastUrl = '';
    let urlChangeCount = 0;
    let focusChangeCount = 0;
    let windowActiveTime = 0;
    let lastActiveCheck = Date.now();
    let userInteractionIndicators = 0;

    const checkInterval = setInterval(() => {
      if (!session.windowRef || session.windowRef.closed) {
        clearInterval(checkInterval);

        // 保存窗口活动数据
        session.windowActivityData = {
          urlChangeCount,
          focusChangeCount,
          windowActiveTime,
          userInteractionIndicators,
          totalDuration: Date.now() - session.startTime
        };

        this.handleWindowClosed(session);
        return;
      }

      // 检测窗口活动状态
      try {
        // 检查窗口焦点状态
        if (!session.windowRef.closed) {
          const currentTime = Date.now();

          // 尝试检测URL变化（如果可能）
          try {
            const currentUrl = session.windowRef.location.href;
            if (currentUrl !== lastUrl && lastUrl !== '') {
              urlChangeCount++;
              userInteractionIndicators += 2; // URL变化表明用户操作
              console.log(`🔄 检测到URL变化: ${urlChangeCount}次`);
            }
            lastUrl = currentUrl;
          } catch (e) {
            // 跨域无法访问URL，这是正常的
          }

          // 检测窗口焦点变化
          try {
            if (session.windowRef.document.hasFocus()) {
              windowActiveTime += currentTime - lastActiveCheck;
              focusChangeCount++;
            }
          } catch (e) {
            // 跨域场景，假设窗口是活跃的
            windowActiveTime += currentTime - lastActiveCheck;
          }

          lastActiveCheck = currentTime;
        }
      } catch (error) {
        // 跨域错误是正常的，继续监控
      }
    }, 1000);

    // 额外的用户活动检测
    this.setupAdvancedWindowMonitoring(session);
  }

  /**
   * 设置高级窗口监控
   */
  private setupAdvancedWindowMonitoring(session: TaskExecutionSession): void {
    // 监听窗口焦点事件
    const handleFocus = () => {
      if (session.windowActivityData) {
        session.windowActivityData.focusChangeCount++;
        session.windowActivityData.userInteractionIndicators++;
      }
    };

    const handleBlur = () => {
      if (session.windowActivityData) {
        session.windowActivityData.focusChangeCount++;
      }
    };

    // 监听主窗口的焦点变化（间接检测用户在任务窗口的活动）
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    // 清理监听器
    setTimeout(() => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    }, this.MAX_SESSION_TIMEOUT);
  }

  /**
   * 处理窗口关闭事件
   */
  private async handleWindowClosed(session: TaskExecutionSession): Promise<void> {
    const duration = Date.now() - session.startTime;
    const durationSeconds = Math.round(duration / 1000);
    const minDurationSeconds = Math.round(session.minDuration / 1000);

    console.log(`🔍 窗口关闭检测 - 执行时间: ${durationSeconds}秒, 最小要求: ${minDurationSeconds}秒, 平台: ${session.platform}`);

    // 如果时间非常短（小于1秒），可能是意外关闭
    if (duration < 1000) {
      session.status = 'failed';
      this.showCompletionDialog(session, {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: `窗口关闭过快（${durationSeconds}秒），可能是意外关闭。请重新打开任务窗口完成操作。`,
        needsManualReview: false
      });
      return;
    }

    // 对于跨域场景且时间合理的情况，先询问用户是否完成
    const isCrossDomain = !session.isTrackingBehavior;
    const userConfirmationEnabled = localStorage.getItem('userConfirmationEnabled') === 'true';

    if ((isCrossDomain && durationSeconds >= 8 && durationSeconds <= 300) || userConfirmationEnabled) {
      const userConfirmed = await this.askUserConfirmation(session, durationSeconds);
      if (userConfirmed) {
        session.userConfirmedCompletion = true;
      }
    }

    session.status = 'completed';

    // 执行任务验证（包括智能检测）
    try {
      const result = await this.verifyTaskCompletion(session);
      this.showCompletionDialog(session, result);
    } catch (error) {
      console.error('任务验证失败:', error);
      this.showCompletionDialog(session, {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: '任务验证失败，请稍后重试或联系客服',
        needsManualReview: true
      });
    }
  }

  /**
   * 验证任务完成情况
   */
  private async verifyTaskCompletion(session: TaskExecutionSession): Promise<ExecutionResult> {
    const duration = Date.now() - session.startTime;
    const durationSeconds = Math.round(duration / 1000);

    // 结束用户引导
    taskGuidanceService.endGuidanceSession();

    // 检查执行时间是否在合理范围内
    if (duration > this.MAX_SESSION_TIMEOUT) {
      return {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: '任务执行时间过长，可能存在异常，请重新完成'
      };
    }

    // 停止行为跟踪并收集数据
    let behaviorData = null;
    if (session.isTrackingBehavior) {
      try {
        behaviorData = behaviorTrackingService.getCurrentData();
        behaviorTrackingService.stopTracking();
      } catch (error) {
        console.warn('获取行为数据失败:', error);
      }
    }

    // 检查是否为跨域场景（无法获取行为数据）
    const isCrossDomain = !session.isTrackingBehavior || !behaviorData || !behaviorData.metrics;

    console.log(`🔍 验证场景判断 - 跨域: ${isCrossDomain}, 平台: ${session.platform}, 时长: ${durationSeconds}秒`);

    // 获取所有检测结果
    const detectionResults = automatedDetectionService.getDetectionResults(session.id);
    const extensionData = browserExtensionDetector.getExtensionDetectionResult(session.id);
    const realData = realCrossDomainDetector.getRealDetectionResult(session.id);
    const legacyClickData = crossDomainClickDetector.getDetectionResult(session.id);
    const windowActivity = crossDomainClickDetector.getWindowActivity(session.id);

    console.log('🧠 智能检测结果:', detectionResults);
    console.log('🔌 扩展式检测结果:', extensionData);
    console.log('🎯 真实检测结果:', realData);
    console.log('🖱️ 传统点击检测结果:', legacyClickData);
    console.log('🪟 窗口活动数据:', windowActivity);

    // 保存所有检测数据到会话
    session.extensionDetectionData = extensionData;
    session.realDetectionData = realData;
    session.clickDetectionData = legacyClickData;

    // 高级验证：使用最佳检测数据进行验证
    const hasRealInteraction = this.validateAdvancedUserInteraction(
      session,
      detectionResults,
      behaviorData,
      extensionData,
      realData,
      legacyClickData
    );

    if (!hasRealInteraction && durationSeconds < 8) {
      // 只有在时间很短且无交互证据时才拒绝
      console.log('🚫 时间过短且未检测到用户交互，拒绝验证');

      // 显示检测问题修复工具
      this.showDetectionFixTool(session, extensionData, realData, legacyClickData);

      return {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: `❌ 检测系统无法确认您的操作。这可能是由于跨域限制导致的。请点击"修复检测问题"按钮获取解决方案。`,
        proofRequired: true,
        needsManualReview: true
      };
    }

    if (isCrossDomain) {
      // 跨域场景：优先使用智能检测，否则使用时间验证
      console.log('🌐 跨域场景，使用智能检测或时间验证');

      // 使用新的高级检测结果处理
      return this.processAdvancedDetectionResults(session, duration, durationSeconds);
    }

    // 同域场景：进行严格的行为验证
    const hasBasicInteraction = behaviorData.metrics.clickCount > 0 && behaviorData.metrics.mouseMovements > 0;
    if (!hasBasicInteraction) {
      return {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: '未检测到基本用户交互（点击和鼠标移动），验证失败。请确保实际执行了所需操作。',
        proofRequired: true,
        needsManualReview: true
      };
    }

    // 同域场景：使用综合验证系统
    if (session.platform && session.platform !== 'unknown') {
      try {
        const evidence: VerificationEvidence = {
          behaviorMetrics: behaviorData.metrics,
          userInteractions: behaviorData.interactions,
          domChanges: behaviorData.domChanges
        };

        // 如果有智能检测结果，优先使用
        if (detectionResults) {
          console.log('✅ 使用智能检测结果');
          return this.processDetectionResults(session, detectionResults, durationSeconds);
        }

        // 否则使用传统验证方法
        console.log('📊 使用传统验证方法');
        const verificationResults = await taskVerificationService.verifyTaskCompletion(
          session.platform,
          session.selectedRequirements,
          evidence,
          session.id
        );

        return this.processVerificationResults(session, verificationResults, durationSeconds);
      } catch (error) {
        console.error('综合验证失败:', error);
        return {
          success: false,
          completedRequirements: [],
          failedRequirements: session.selectedRequirements,
          pointsEarned: 0,
          message: '验证系统出现错误，请稍后重试或联系客服。',
          proofRequired: true,
          needsManualReview: true
        };
      }
    }

    // 如果没有平台信息，使用严格的基础验证
    return this.performBasicVerification(session, duration, durationSeconds);
  }

  /**
   * 处理高级检测结果（扩展式 + 真实检测 + 传统检测）
   */
  private processAdvancedDetectionResults(
    session: TaskExecutionSession,
    duration: number,
    durationSeconds: number
  ): ExecutionResult {
    console.log(`🔍 处理高级检测结果 - 方法: ${session.detectionMethod}, 时长: ${durationSeconds}秒`);

    // 根据检测方法选择最佳数据源
    switch (session.detectionMethod) {
      case 'extension':
        if (session.extensionDetectionData) {
          return this.processExtensionResults(session, session.extensionDetectionData, durationSeconds);
        }
        break;

      case 'real':
        if (session.realDetectionData) {
          return this.processRealDetectionResults(session, session.realDetectionData, durationSeconds);
        }
        break;

      case 'legacy':
        if (session.clickDetectionData) {
          return this.processClickDetectionResults(session, session.clickDetectionData, durationSeconds);
        }
        break;

      case 'hybrid':
        return this.processHybridResults(session, durationSeconds);
    }

    // 回退到传统方法
    console.log('⏱️ 回退到传统验证方法');
    return this.performCrossDomainVerification(session, duration, durationSeconds);
  }

  /**
   * 处理扩展式检测结果
   */
  private processExtensionResults(
    session: TaskExecutionSession,
    extensionData: ExtensionDetectionData,
    durationSeconds: number
  ): ExecutionResult {
    console.log(`🔌 处理扩展式检测结果 - 总点击: ${extensionData.clicks.total}, 置信度: ${extensionData.confidence}%`);

    const completedRequirements: TaskType[] = [];
    const failedRequirements: TaskType[] = [];

    session.selectedRequirements.forEach(requirement => {
      let completed = false;
      let confidence = 0;

      // 检查特定任务的点击
      if (extensionData.clicks.byType[requirement] > 0) {
        completed = true;
        confidence = 90;
      }

      // 检查目标点击
      if (!completed) {
        const targetClick = extensionData.clicks.targets.find(
          target => target.action === requirement
        );
        if (targetClick) {
          completed = true;
          confidence = 85;
        }
      }

      // 基于总体活动推断
      if (!completed && extensionData.clicks.total > 0 && extensionData.confidence > 70) {
        // 如果有足够的点击和高置信度，推断任务完成
        const taskIndex = session.selectedRequirements.indexOf(requirement);
        if (taskIndex < extensionData.clicks.total) {
          completed = true;
          confidence = 75;
        }
      }

      if (completed) {
        completedRequirements.push(requirement);
        console.log(`✅ 扩展式检测确认完成: ${requirement} (置信度: ${confidence}%)`);
      } else {
        failedRequirements.push(requirement);
        console.log(`❌ 扩展式检测未确认: ${requirement}`);
      }
    });

    const pointsEarned = this.calculatePoints(completedRequirements);
    const successRate = completedRequirements.length / session.selectedRequirements.length;

    let message = '';
    let needsManualReview = false;

    if (successRate === 1) {
      message = `🔌 扩展式检测验证成功！检测到 ${extensionData.clicks.total} 次点击，完成所有任务（置信度：${extensionData.confidence}%，执行时间：${durationSeconds}秒）`;
    } else if (successRate > 0) {
      message = `🔌 部分任务通过扩展式检测验证（${completedRequirements.length}/${session.selectedRequirements.length}）。检测到 ${extensionData.clicks.total} 次点击，置信度：${extensionData.confidence}%`;
      needsManualReview = successRate < 0.8;
    } else {
      message = `❌ 扩展式检测未发现有效操作。检测到 ${extensionData.clicks.total} 次点击，但无法确认任务完成`;
      needsManualReview = true;
    }

    return {
      success: completedRequirements.length > 0,
      completedRequirements,
      failedRequirements,
      pointsEarned,
      message,
      needsManualReview,
      proofRequired: successRate < 1
    };
  }

  /**
   * 处理真实检测结果
   */
  private processRealDetectionResults(
    session: TaskExecutionSession,
    realData: RealClickData,
    durationSeconds: number
  ): ExecutionResult {
    console.log(`🎯 处理真实检测结果 - 总点击: ${realData.totalClicks}, 置信度: ${realData.confidence}%`);

    const completedRequirements: TaskType[] = [];
    const failedRequirements: TaskType[] = [];

    session.selectedRequirements.forEach(requirement => {
      let completed = false;
      let confidence = 0;

      // 检查特定任务的操作
      const taskData = realData.taskActions[requirement as keyof typeof realData.taskActions];
      if (taskData && taskData.count > 0) {
        completed = true;
        confidence = 85;
      }

      // 基于总体活动和时间推断
      if (!completed && realData.totalClicks > 0 && realData.confidence > 60) {
        // 根据任务类型和活动模式推断
        const taskIndex = session.selectedRequirements.indexOf(requirement);
        if (taskIndex < realData.totalClicks && durationSeconds >= 8) {
          completed = true;
          confidence = 70;
        }
      }

      if (completed) {
        completedRequirements.push(requirement);
        console.log(`✅ 真实检测确认完成: ${requirement} (置信度: ${confidence}%)`);
      } else {
        failedRequirements.push(requirement);
        console.log(`❌ 真实检测未确认: ${requirement}`);
      }
    });

    const pointsEarned = this.calculatePoints(completedRequirements);
    const successRate = completedRequirements.length / session.selectedRequirements.length;

    let message = '';
    let needsManualReview = false;

    if (successRate === 1) {
      message = `🎯 真实检测验证成功！检测到 ${realData.totalClicks} 次点击，完成所有任务（置信度：${realData.confidence}%，执行时间：${durationSeconds}秒）`;
    } else if (successRate > 0) {
      message = `🎯 部分任务通过真实检测验证（${completedRequirements.length}/${session.selectedRequirements.length}）。检测到 ${realData.totalClicks} 次点击，置信度：${realData.confidence}%`;
      needsManualReview = successRate < 0.7;
    } else {
      message = `❌ 真实检测未发现有效操作。检测到 ${realData.totalClicks} 次点击，但无法确认任务完成`;
      needsManualReview = true;
    }

    return {
      success: completedRequirements.length > 0,
      completedRequirements,
      failedRequirements,
      pointsEarned,
      message,
      needsManualReview,
      proofRequired: successRate < 1
    };
  }

  /**
   * 处理混合检测结果
   */
  private processHybridResults(
    session: TaskExecutionSession,
    durationSeconds: number
  ): ExecutionResult {
    console.log(`🔀 处理混合检测结果`);

    const completedRequirements: TaskType[] = [];
    const failedRequirements: TaskType[] = [];
    const evidenceSources: string[] = [];

    session.selectedRequirements.forEach(requirement => {
      let completed = false;
      let confidence = 0;
      let sources: string[] = [];

      // 检查扩展式检测
      if (session.extensionDetectionData) {
        const extData = session.extensionDetectionData;
        if (extData.clicks.byType[requirement] > 0 ||
            extData.clicks.targets.some(t => t.action === requirement)) {
          completed = true;
          confidence = Math.max(confidence, 85);
          sources.push('扩展式检测');
        }
      }

      // 检查真实检测
      if (session.realDetectionData) {
        const realData = session.realDetectionData;
        const taskData = realData.taskActions[requirement as keyof typeof realData.taskActions];
        if (taskData && taskData.count > 0) {
          completed = true;
          confidence = Math.max(confidence, 80);
          sources.push('真实检测');
        }
      }

      // 检查传统检测
      if (session.clickDetectionData) {
        const legacyData = session.clickDetectionData;
        let hasLegacyEvidence = false;

        switch (requirement) {
          case 'like': hasLegacyEvidence = legacyData.likeClicks > 0; break;
          case 'share': hasLegacyEvidence = legacyData.shareClicks > 0; break;
          case 'follow': hasLegacyEvidence = legacyData.followClicks > 0; break;
          case 'comment': hasLegacyEvidence = legacyData.commentClicks > 0; break;
        }

        if (hasLegacyEvidence) {
          completed = true;
          confidence = Math.max(confidence, 75);
          sources.push('传统检测');
        }
      }

      // 如果多个检测源都确认，提高置信度
      if (sources.length > 1) {
        confidence = Math.min(confidence + (sources.length - 1) * 10, 95);
      }

      if (completed) {
        completedRequirements.push(requirement);
        evidenceSources.push(`${requirement}(${sources.join('+')}, ${confidence}%)`);
        console.log(`✅ 混合检测确认完成: ${requirement} - 来源: ${sources.join(', ')} (置信度: ${confidence}%)`);
      } else {
        failedRequirements.push(requirement);
        console.log(`❌ 混合检测未确认: ${requirement}`);
      }
    });

    const pointsEarned = this.calculatePoints(completedRequirements);
    const successRate = completedRequirements.length / session.selectedRequirements.length;

    let message = '';
    let needsManualReview = false;

    if (successRate === 1) {
      message = `🔀 混合检测验证成功！完成所有任务（执行时间：${durationSeconds}秒）\n证据来源：${evidenceSources.join(', ')}`;
    } else if (successRate > 0) {
      message = `🔀 部分任务通过混合检测验证（${completedRequirements.length}/${session.selectedRequirements.length}）\n证据来源：${evidenceSources.join(', ')}`;
      needsManualReview = successRate < 0.8;
    } else {
      message = `❌ 混合检测未发现有效操作，无法确认任务完成`;
      needsManualReview = true;
    }

    return {
      success: completedRequirements.length > 0,
      completedRequirements,
      failedRequirements,
      pointsEarned,
      message,
      needsManualReview,
      proofRequired: successRate < 1
    };
  }

  /**
   * 清理所有检测器
   */
  private cleanupAllDetectors(sessionId: string): void {
    try {
      automatedDetectionService.clearDetectionResults(sessionId);
    } catch (e) {
      console.warn('清理智能检测失败:', e);
    }

    try {
      crossDomainClickDetector.stopDetection(sessionId);
    } catch (e) {
      console.warn('清理传统检测失败:', e);
    }

    try {
      browserExtensionDetector.stopExtensionDetection(sessionId);
    } catch (e) {
      console.warn('清理扩展式检测失败:', e);
    }

    try {
      realCrossDomainDetector.stopRealDetection(sessionId);
    } catch (e) {
      console.warn('清理真实检测失败:', e);
    }

    console.log(`🧹 所有检测器已清理 - 会话: ${sessionId}`);
  }

  /**
   * 处理检测结果（保留原方法作为兜底）
   */
  private processDetectionResults(
    session: TaskExecutionSession,
    detectionResults: any,
    durationSeconds: number,
    clickDetectionData?: ClickDetectionResult | null
  ): ExecutionResult {
    const detectedActions = detectionResults.detectedActions || [];
    const confidence = detectionResults.confidence || 0;
    const method = detectionResults.method || 'unknown';

    const completedRequirements: TaskType[] = [];
    const failedRequirements: TaskType[] = [];

    console.log(`🔍 处理检测结果 - 方法: ${method}, 置信度: ${confidence}%, 检测到 ${detectedActions.length} 个操作`);

    if (clickDetectionData) {
      console.log(`🖱️ 点击检测补充数据 - 总点击: ${clickDetectionData.totalClicks}, 检测操作: ${clickDetectionData.detectedActions.length}`);
    }

    // 检查每个要求是否被检测到
    session.selectedRequirements.forEach(requirement => {
      let detected = detectedActions.find((action: any) => action.action === requirement);

      // 如果传统检测没有发现，尝试从点击检测数据中查找
      if (!detected && clickDetectionData) {
        const clickAction = clickDetectionData.detectedActions.find(
          action => action.action === requirement
        );
        if (clickAction) {
          detected = {
            action: requirement,
            confidence: clickAction.confidence,
            source: 'click_detection'
          };
          console.log(`🖱️ 从点击检测中发现操作: ${requirement} (置信度: ${clickAction.confidence}%)`);
        }
      }

      if (detected) {
        // 检查置信度阈值
        const actionConfidence = detected.confidence || confidence;
        const threshold = this.getConfidenceThreshold(requirement, method);

        if (actionConfidence >= threshold) {
          completedRequirements.push(requirement);
          console.log(`✅ 检测到操作: ${requirement} (置信度: ${actionConfidence}%, 阈值: ${threshold}%)`);
        } else {
          failedRequirements.push(requirement);
          console.log(`❌ 操作置信度不足: ${requirement} (置信度: ${actionConfidence}%, 需要: ${threshold}%)`);
        }
      } else {
        // 对于分享任务，尝试额外的验证方法
        if (requirement === 'share') {
          const shareVerification = this.verifyShareAction(detectionResults, durationSeconds);
          if (shareVerification.completed) {
            completedRequirements.push(requirement);
            console.log(`✅ 分享任务通过额外验证 (置信度: ${shareVerification.confidence}%)`);
          } else {
            failedRequirements.push(requirement);
            console.log(`❌ 未检测到操作: ${requirement}`);
          }
        } else {
          failedRequirements.push(requirement);
          console.log(`❌ 未检测到操作: ${requirement}`);
        }
      }
    });

    const pointsEarned = this.calculatePoints(completedRequirements);
    const successRate = completedRequirements.length / session.selectedRequirements.length;

    let message = '';
    let needsManualReview = false;
    let proofRequired = false;

    if (method === 'intelligent_cross_domain') {
      // 智能推断结果
      if (successRate === 1 && confidence >= 70) {
        message = `🧠 任务智能验证成功！推断您已完成所有操作（执行时间：${durationSeconds}秒，置信度：${confidence}%）`;
      } else if (successRate > 0 && confidence >= 60) {
        message = `🔍 部分任务智能验证成功。推断已完成：${completedRequirements.join('、')}（执行时间：${durationSeconds}秒，置信度：${confidence}%）`;
        needsManualReview = confidence < 80;
      } else if (confidence >= 40) {
        message = `⚠️ 检测到用户活动但无法确认具体操作。建议提供完成截图或重新执行任务（执行时间：${durationSeconds}秒）`;
        needsManualReview = true;
        proofRequired = true;
      } else {
        message = `❌ 未检测到明显的用户操作。请确保您实际执行了所需操作：${session.selectedRequirements.join('、')}`;
        needsManualReview = true;
        proofRequired = true;
      }
    } else {
      // 传统检测结果
      if (successRate === 1) {
        message = `🎉 任务自动验证成功！检测到所有要求的操作（执行时间：${durationSeconds}秒）`;
      } else if (successRate > 0) {
        message = `✅ 部分任务自动验证成功（${completedRequirements.length}/${session.selectedRequirements.length}）。已检测到：${completedRequirements.join('、')}`;
        needsManualReview = true;
      } else {
        message = `❌ 未检测到任何要求的操作。请确保您实际执行了所需操作：${session.selectedRequirements.join('、')}`;
        needsManualReview = true;
        proofRequired = true;
      }
    }

    // 清除所有检测结果
    this.cleanupAllDetectors(session.id);

    return {
      success: completedRequirements.length > 0,
      completedRequirements,
      failedRequirements,
      pointsEarned,
      message,
      needsManualReview,
      proofRequired
    };
  }

  /**
   * 处理自动检测结果（保留向后兼容）
   */
  private processAutomatedDetectionResults(
    session: TaskExecutionSession,
    detectionResults: any,
    durationSeconds: number
  ): ExecutionResult {
    return this.processDetectionResults(session, detectionResults, durationSeconds);
  }

  /**
   * 处理验证结果
   */
  private processVerificationResults(
    session: TaskExecutionSession,
    verificationResults: Map<TaskType, VerificationResult>,
    durationSeconds: number
  ): ExecutionResult {
    const completedRequirements: TaskType[] = [];
    const failedRequirements: TaskType[] = [];
    let totalConfidence = 0;
    let needsManualReview = false;
    let proofRequired = false;
    const messages: string[] = [];

    for (const [taskType, result] of verificationResults) {
      if (result.success && result.confidence >= 60) {
        completedRequirements.push(taskType);
      } else {
        failedRequirements.push(taskType);
      }

      totalConfidence += result.confidence;

      if (result.needsManualReview) {
        needsManualReview = true;
      }

      if (result.failureReason) {
        messages.push(`${taskType}: ${result.failureReason}`);
      }
    }

    const avgConfidence = verificationResults.size > 0 ? totalConfidence / verificationResults.size : 0;
    const pointsEarned = this.calculatePoints(completedRequirements);

    // 检查是否需要截图证明
    proofRequired = this.shouldRequireProof(session) || avgConfidence < 80;

    let message = '';
    if (completedRequirements.length === session.selectedRequirements.length) {
      message = `任务验证成功！执行时长：${durationSeconds}秒，验证置信度：${Math.round(avgConfidence)}%`;
    } else if (completedRequirements.length > 0) {
      message = `部分任务验证成功（${completedRequirements.length}/${session.selectedRequirements.length}）`;
    } else {
      message = `任务验证失败。${messages.join('; ')}`;
    }

    return {
      success: completedRequirements.length > 0,
      completedRequirements,
      failedRequirements,
      pointsEarned,
      message,
      needsManualReview,
      proofRequired
    };
  }

  /**
   * 跨域验证方法（用于外部视频平台）- 集成智能推断 + 点击检测
   */
  private performCrossDomainVerification(
    session: TaskExecutionSession,
    duration: number,
    durationSeconds: number,
    clickDetectionData?: ClickDetectionResult | null
  ): ExecutionResult {
    console.log(`🌐 执行跨域验证 - 时长: ${durationSeconds}秒, 平台: ${session.platform}`);

    if (clickDetectionData) {
      console.log(`🖱️ 跨域验证包含点击数据 - 总点击: ${clickDetectionData.totalClicks}, 操作: ${clickDetectionData.detectedActions.length}`);
    }

    // 优先使用点击检测结果
    if (clickDetectionData && this.hasValidClickData(clickDetectionData, session.selectedRequirements)) {
      console.log('🖱️ 使用点击检测结果进行跨域验证');
      return this.processClickDetectionResults(session, clickDetectionData, durationSeconds);
    }

    // 生成智能推断结果
    const intelligentResult = this.generateIntelligentDetection(session, duration);

    if (intelligentResult) {
      console.log('🧠 使用智能推断结果');
      return this.processDetectionResults(session, intelligentResult, durationSeconds, clickDetectionData);
    }

    // 使用增强的回退验证逻辑
    return this.performEnhancedFallbackVerification(session, duration, durationSeconds, clickDetectionData);
  }

  /**
   * 检查点击数据是否有效
   */
  private hasValidClickData(clickData: ClickDetectionResult, requiredTasks: TaskType[]): boolean {
    // 检查是否有总点击数
    if (clickData.totalClicks > 0) return true;

    // 检查是否有任务相关的点击
    const hasTaskClicks = requiredTasks.some(task => {
      switch (task) {
        case 'like': return clickData.likeClicks > 0;
        case 'share': return clickData.shareClicks > 0;
        case 'follow': return clickData.followClicks > 0;
        case 'comment': return clickData.commentClicks > 0;
        default: return false;
      }
    });

    // 检查是否有检测到的操作
    const hasDetectedActions = clickData.detectedActions.some(action =>
      requiredTasks.includes(action.action as TaskType) && action.confidence >= 60
    );

    return hasTaskClicks || hasDetectedActions;
  }

  /**
   * 处理点击检测结果
   */
  private processClickDetectionResults(
    session: TaskExecutionSession,
    clickData: ClickDetectionResult,
    durationSeconds: number
  ): ExecutionResult {
    console.log(`🖱️ 处理点击检测结果 - 总点击: ${clickData.totalClicks}, 检测操作: ${clickData.detectedActions.length}`);

    const completedRequirements: TaskType[] = [];
    const failedRequirements: TaskType[] = [];

    session.selectedRequirements.forEach(requirement => {
      let completed = false;
      let confidence = 0;

      // 检查特定任务的点击
      switch (requirement) {
        case 'like':
          if (clickData.likeClicks > 0) {
            completed = true;
            confidence = 85;
          }
          break;
        case 'share':
          if (clickData.shareClicks > 0) {
            completed = true;
            confidence = 80;
          }
          break;
        case 'follow':
          if (clickData.followClicks > 0) {
            completed = true;
            confidence = 85;
          }
          break;
        case 'comment':
          if (clickData.commentClicks > 0) {
            completed = true;
            confidence = 75;
          }
          break;
      }

      // 检查检测到的操作
      if (!completed) {
        const detectedAction = clickData.detectedActions.find(
          action => action.action === requirement && action.confidence >= 60
        );
        if (detectedAction) {
          completed = true;
          confidence = detectedAction.confidence;
        }
      }

      // 如果有总点击但没有特定操作，给予部分分数
      if (!completed && clickData.totalClicks > 0 && durationSeconds >= 8) {
        completed = true;
        confidence = 65; // 较低置信度
      }

      if (completed) {
        completedRequirements.push(requirement);
        console.log(`✅ 点击检测确认完成: ${requirement} (置信度: ${confidence}%)`);
      } else {
        failedRequirements.push(requirement);
        console.log(`❌ 点击检测未确认: ${requirement}`);
      }
    });

    const pointsEarned = this.calculatePoints(completedRequirements);
    const successRate = completedRequirements.length / session.selectedRequirements.length;

    let message = '';
    let needsManualReview = false;

    if (successRate === 1) {
      message = `🖱️ 点击检测验证成功！检测到 ${clickData.totalClicks} 次点击，完成所有任务（执行时间：${durationSeconds}秒）`;
    } else if (successRate > 0) {
      message = `🖱️ 部分任务通过点击检测验证（${completedRequirements.length}/${session.selectedRequirements.length}）。检测到 ${clickData.totalClicks} 次点击`;
      needsManualReview = true;
    } else {
      message = `❌ 点击检测未发现有效操作。检测到 ${clickData.totalClicks} 次点击，但无法确认任务完成`;
      needsManualReview = true;
    }

    return {
      success: completedRequirements.length > 0,
      completedRequirements,
      failedRequirements,
      pointsEarned,
      message,
      needsManualReview,
      proofRequired: successRate < 1
    };
  }

  /**
   * 增强的回退验证逻辑
   */
  private performEnhancedFallbackVerification(
    session: TaskExecutionSession,
    duration: number,
    durationSeconds: number,
    clickDetectionData?: ClickDetectionResult | null
  ): ExecutionResult {
    console.log(`🔄 执行增强回退验证 - 时长: ${durationSeconds}秒`);

    const verification = this.analyzeTaskExecutionPattern(session, duration);

    if (verification.isValid) {
      // 基于模式分析的成功验证
      const completedRequirements = verification.likelyCompletedTasks;
      const pointsEarned = this.calculatePoints(completedRequirements);

      return {
        success: true,
        completedRequirements,
        failedRequirements: session.selectedRequirements.filter(req => !completedRequirements.includes(req)),
        pointsEarned,
        message: verification.message,
        needsManualReview: verification.needsReview,
        proofRequired: verification.requiresProof
      };
    } else {
      // 验证失败，需要用户提供证明
      return {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: verification.message,
        proofRequired: true,
        needsManualReview: true
      };
    }
  }

  /**
   * 分析任务执行模式（严格版本 - 修复安全漏洞）
   */
  private analyzeTaskExecutionPattern(
    session: TaskExecutionSession,
    duration: number
  ): {
    isValid: boolean;
    likelyCompletedTasks: TaskType[];
    message: string;
    needsReview: boolean;
    requiresProof: boolean;
    confidence: number;
  } {
    const durationSeconds = Math.round(duration / 1000);
    let confidence = 0;
    const likelyCompletedTasks: TaskType[] = [];
    let needsReview = true; // 默认需要审核
    let requiresProof = true; // 默认需要证明

    // 严格的时间模式分析
    const timeAnalysis = this.analyzeTimePattern(duration, session.selectedRequirements);
    confidence += Math.min(timeAnalysis.confidence, 30); // 限制时间分析的最大贡献

    // 严格的任务类型特定分析
    for (const requirement of session.selectedRequirements) {
      const taskAnalysis = this.analyzeTaskSpecificPattern(requirement, duration, session.platform);

      // 提高任务完成的判断标准
      if (taskAnalysis.likely && taskAnalysis.confidence >= 60) {
        likelyCompletedTasks.push(requirement);
        confidence += Math.min(taskAnalysis.confidence, 20); // 限制单个任务的最大贡献
      }
    }

    // 严格限制平台加分
    const platformBonus = Math.min(this.getPlatformReliabilityBonus(session.platform), 5);
    confidence += platformBonus;

    // 移除用户信誉加分，防止被滥用
    // const reputationBonus = this.getReputationBonus(session);
    // confidence += reputationBonus;

    // 严格的验证结果判断 - 大幅提高阈值
    const isValid = confidence >= 80 && likelyCompletedTasks.length > 0 && durationSeconds >= 20;

    // 更严格的审核和证明要求
    if (confidence >= 90 && durationSeconds >= 30) {
      needsReview = false;
      requiresProof = false;
    } else if (confidence >= 80 && durationSeconds >= 20) {
      needsReview = true;
      requiresProof = false;
    } else {
      needsReview = true;
      requiresProof = true;
    }

    let message = '';
    if (isValid) {
      if (likelyCompletedTasks.length === session.selectedRequirements.length) {
        message = `⚠️ 任务可能已完成（执行时间：${durationSeconds}秒，置信度：${Math.round(confidence)}%），需要人工审核确认。`;
      } else {
        message = `⚠️ 部分任务可能完成：${likelyCompletedTasks.join('、')}（执行时间：${durationSeconds}秒，置信度：${Math.round(confidence)}%），需要人工审核确认。`;
      }
    } else {
      message = `❌ 无法确认任务完成（执行时间：${durationSeconds}秒，置信度：${Math.round(confidence)}%）。请提供完成截图证明或重新执行任务并确保实际完成操作。`;
    }

    console.log(`🔒 严格模式分析结果: 有效=${isValid}, 置信度=${confidence}%, 完成任务=${likelyCompletedTasks.length}/${session.selectedRequirements.length}`);

    return {
      isValid,
      likelyCompletedTasks,
      message,
      needsReview,
      requiresProof,
      confidence
    };
  }

  /**
   * 生成智能检测结果（平衡版本 - 安全与可用性并重）
   */
  private generateIntelligentDetection(session: TaskExecutionSession, duration: number): any {
    if (!session.platform || session.platform === 'unknown') {
      return null;
    }

    // 计算智能推断置信度
    const confidence = this.calculateIntelligentConfidence(session, duration);

    // 平衡的置信度阈值
    if (confidence < 50) {
      console.log(`🚫 智能推断置信度不足: ${confidence}% (需要50%+)`);
      return null; // 置信度太低，不生成结果
    }

    // 平衡的安全检查
    const durationSeconds = duration / 1000;

    // 对于不同任务类型，设置合理的最低时间要求
    const minTimeRequirements = {
      'like': 5,      // 点赞至少5秒
      'share': 12,    // 分享至少12秒
      'comment': 20,  // 评论至少20秒
      'follow': 6     // 关注至少6秒
    };

    // 检查是否满足最低时间要求
    for (const requirement of session.selectedRequirements) {
      const minTime = minTimeRequirements[requirement] || 8;
      if (durationSeconds < minTime) {
        console.log(`🚫 ${requirement}任务时间不足: ${durationSeconds}秒 (需要${minTime}秒+)`);
        return null;
      }
    }

    // 生成检测结果
    const detectedActions = session.selectedRequirements.map(requirement => ({
      action: requirement,
      platform: session.platform,
      timestamp: Date.now(),
      confidence: Math.min(confidence, 85), // 允许更高置信度
      method: 'intelligent_inference_balanced'
    }));

    console.log(`✅ 智能推断通过平衡验证: ${confidence}%`);

    return {
      detectedActions,
      confidence: Math.min(confidence, 85),
      method: 'intelligent_cross_domain_balanced',
      indicators: {
        timeSpent: duration,
        platformOptimized: true,
        crossDomainInference: true,
        balancedValidation: true
      }
    };
  }

  /**
   * 分析时间模式（平衡版本 - 安全与可用性并重）
   */
  private analyzeTimePattern(duration: number, requirements: TaskType[]): { confidence: number; analysis: string } {
    const durationSeconds = duration / 1000;
    let confidence = 0;
    let analysis = '';

    // 平衡的基础时间评估
    if (durationSeconds < 5) {
      confidence = 0;
      analysis = '时间过短，不可能完成真实操作';
    } else if (durationSeconds < 8) {
      confidence = 20;
      analysis = '时间较短，可能完成简单操作';
    } else if (durationSeconds < 15) {
      confidence = 40;
      analysis = '时间基本合理，可能完成基本操作';
    } else if (durationSeconds < 30) {
      confidence = 60;
      analysis = '时间适中，很可能完成操作';
    } else if (durationSeconds < 60) {
      confidence = 70;
      analysis = '时间充足，很可能完成所需操作';
    } else if (durationSeconds < 120) {
      confidence = 65;
      analysis = '时间较长，可能完成复杂操作';
    } else if (durationSeconds < 300) {
      confidence = 50;
      analysis = '时间很长，可能完成复杂操作或存在其他活动';
    } else {
      confidence = 30;
      analysis = '时间过长，可能存在其他活动';
    }

    // 平衡的任务数量调整
    const taskCount = requirements.length;
    if (taskCount > 1) {
      const expectedMinTime = taskCount * 8; // 每个任务至少8秒
      if (durationSeconds >= expectedMinTime) {
        confidence += 15; // 适度加分
      } else {
        confidence -= 20; // 适度减分
      }
    }

    // 平衡限制最高置信度
    return { confidence: Math.max(0, Math.min(confidence, 75)), analysis };
  }

  /**
   * 分析特定任务类型的模式（平衡版本 - 安全与可用性并重）
   */
  private analyzeTaskSpecificPattern(
    taskType: TaskType,
    duration: number,
    platform: string
  ): { likely: boolean; confidence: number; reason: string } {
    const durationSeconds = duration / 1000;

    // 平衡的任务类型时间范围
    const taskTimeRanges = {
      like: { min: 5, max: 30, optimal: 10 },      // 点赞至少5秒
      share: { min: 12, max: 120, optimal: 30 },   // 分享至少12秒
      comment: { min: 20, max: 180, optimal: 45 }, // 评论至少20秒
      follow: { min: 6, max: 60, optimal: 15 }     // 关注至少6秒
    };

    const range = taskTimeRanges[taskType];
    if (!range) {
      return { likely: false, confidence: 0, reason: '未知任务类型' };
    }

    let confidence = 0;
    let reason = '';

    if (durationSeconds >= range.min && durationSeconds <= range.max) {
      // 在合理时间范围内
      const distanceFromOptimal = Math.abs(durationSeconds - range.optimal);
      const maxDistance = Math.max(range.optimal - range.min, range.max - range.optimal);
      confidence = 70 - (distanceFromOptimal / maxDistance) * 30; // 恢复合理的基础分数
      reason = `时间在${taskType}操作的合理范围内`;
    } else if (durationSeconds < range.min) {
      confidence = 10; // 给予少量分数而不是直接0分
      reason = `时间可能不足以完成${taskType}操作（建议至少${range.min}秒）`;
    } else {
      confidence = 40; // 时间过长但仍有可能完成
      reason = `时间超出${taskType}操作的典型范围，但仍有可能完成`;
    }

    // 平衡的平台调整
    const platformMultiplier = Math.min(this.getPlatformTaskMultiplier(platform, taskType), 1.2);
    confidence *= platformMultiplier;

    // 平衡的判断标准
    return {
      likely: confidence >= 50, // 降低判断阈值
      confidence: Math.round(Math.min(confidence, 80)), // 允许更高置信度
      reason
    };
  }

  /**
   * 获取平台可靠性加分
   */
  private getPlatformReliabilityBonus(platform: string): number {
    const bonuses = {
      'bilibili': 15,    // B站用户行为较规范
      'douyin': 10,      // 抖音用户活跃度高
      'kuaishou': 8,     // 快手用户参与度中等
      'xiaohongshu': 12, // 小红书用户互动性强
      'youtube': 10
    };
    return bonuses[platform] || 5;
  }

  /**
   * 获取平台任务倍数
   */
  private getPlatformTaskMultiplier(platform: string, taskType: TaskType): number {
    const multipliers = {
      'bilibili': {
        like: 1.2,
        share: 1.1,
        comment: 1.3,
        follow: 1.1
      },
      'douyin': {
        like: 1.3,
        share: 1.0,
        comment: 0.9,
        follow: 1.2
      },
      'kuaishou': {
        like: 1.2,
        share: 1.0,
        comment: 0.9,
        follow: 1.1
      }
    };

    return multipliers[platform]?.[taskType] || 1.0;
  }

  /**
   * 高级用户交互验证（扩展式 + 真实检测 + 传统检测）
   */
  private validateAdvancedUserInteraction(
    session: TaskExecutionSession,
    detectionResults: any,
    behaviorData: any,
    extensionData?: ExtensionDetectionData | null,
    realData?: RealClickData | null,
    legacyClickData?: ClickDetectionResult | null
  ): boolean {
    console.log('🔍 开始高级用户交互验证...');

    // 诊断检测器状态
    this.diagnoseDetectionIssues(session, extensionData, realData, legacyClickData, detectionResults);

    // 优先级1: 扩展式检测结果（最可靠）
    if (extensionData && this.validateExtensionDetection(extensionData, session.selectedRequirements)) {
      console.log('✅ 扩展式检测验证通过');
      return true;
    }

    // 优先级2: 真实检测结果
    if (realData && this.validateRealDetection(realData, session.selectedRequirements)) {
      console.log('✅ 真实检测验证通过');
      return true;
    }

    // 优先级3: 传统检测结果
    if (legacyClickData && this.validateLegacyDetection(legacyClickData, session.selectedRequirements)) {
      console.log('✅ 传统检测验证通过');
      return true;
    }

    // 优先级4: 智能检测结果
    if (detectionResults && this.validateIntelligentDetection(detectionResults)) {
      console.log('✅ 智能检测验证通过');
      return true;
    }

    // 优先级5: 行为数据验证
    if (behaviorData && this.validateBehaviorData(behaviorData)) {
      console.log('✅ 行为数据验证通过');
      return true;
    }

    // 优先级6: 混合验证（综合多种证据）
    if (this.validateHybridEvidence(session, extensionData, realData, legacyClickData, detectionResults)) {
      console.log('✅ 混合证据验证通过');
      return true;
    }

    // 优先级7: 强制验证模式（用于解决检测失效问题）
    if (this.shouldUseForceValidation(session)) {
      console.log('🔧 启用强制验证模式');
      return this.performForceValidation(session);
    }

    console.log('❌ 所有验证方法均未通过');
    return false;
  }

  /**
   * 诊断检测问题
   */
  private diagnoseDetectionIssues(
    session: TaskExecutionSession,
    extensionData?: ExtensionDetectionData | null,
    realData?: RealClickData | null,
    legacyClickData?: ClickDetectionResult | null,
    detectionResults?: any
  ): void {
    console.log('🔧 开始诊断检测问题...');

    const issues: string[] = [];

    // 检查扩展式检测器状态
    if (!extensionData) {
      issues.push('扩展式检测器未返回数据');
      console.warn('⚠️ 扩展式检测器问题:');
      console.warn('  - 跨域脚本注入可能失败');
      console.warn('  - postMessage通信可能被阻止');
      console.warn('  - 目标网站可能阻止了脚本执行');
    } else if (extensionData.clicks.total === 0) {
      issues.push('扩展式检测器未检测到点击');
      console.warn('⚠️ 扩展式检测器已启动但未检测到点击');
    }

    // 检查真实检测器状态
    if (!realData) {
      issues.push('真实检测器未返回数据');
    } else if (realData.totalClicks === 0) {
      issues.push('真实检测器未检测到点击');
    }

    // 检查传统检测器状态
    if (!legacyClickData) {
      issues.push('传统检测器未返回数据');
    } else if (legacyClickData.totalClicks === 0) {
      issues.push('传统检测器未检测到点击');
    }

    // 检查智能检测状态
    if (!detectionResults) {
      issues.push('智能检测器未返回数据');
    } else if (!detectionResults.detectedActions || detectionResults.detectedActions.length === 0) {
      issues.push('智能检测器未检测到操作');
    }

    if (issues.length > 0) {
      console.warn('🚨 检测问题汇总:', issues);
      console.log('💡 建议的解决方案:');
      console.log('  1. 启用强制验证模式');
      console.log('  2. 使用时间+用户确认验证');
      console.log('  3. 要求用户提供完成截图');

      // 保存诊断信息到会话
      (session as any).detectionIssues = issues;
    } else {
      console.log('✅ 所有检测器都正常工作');
    }
  }

  /**
   * 验证扩展式检测结果
   */
  private validateExtensionDetection(
    extensionData: ExtensionDetectionData,
    requiredTasks: TaskType[]
  ): boolean {
    // 检查总点击数
    if (extensionData.clicks.total === 0) {
      console.log('扩展式检测：无点击记录');
      return false;
    }

    // 检查置信度
    if (extensionData.confidence < 70) {
      console.log(`扩展式检测：置信度过低 (${extensionData.confidence}%)`);
      return false;
    }

    // 检查任务特定操作
    const completedTasks = requiredTasks.filter(task => {
      return extensionData.clicks.byType[task] > 0;
    });

    if (completedTasks.length > 0) {
      console.log(`扩展式检测：完成任务 ${completedTasks.join(', ')}`);
      return true;
    }

    // 检查目标点击
    const hasTargetClicks = extensionData.clicks.targets.some(target =>
      requiredTasks.includes(target.action as TaskType)
    );

    if (hasTargetClicks) {
      console.log('扩展式检测：检测到目标操作');
      return true;
    }

    // 如果有足够的点击和用户交互
    if (extensionData.clicks.total >= 2 && extensionData.performance.userInteractions > 0) {
      console.log('扩展式检测：足够的用户交互');
      return true;
    }

    return false;
  }

  /**
   * 验证真实检测结果
   */
  private validateRealDetection(
    realData: RealClickData,
    requiredTasks: TaskType[]
  ): boolean {
    // 检查总点击数
    if (realData.totalClicks === 0) {
      console.log('真实检测：无点击记录');
      return false;
    }

    // 检查置信度
    if (realData.confidence < 60) {
      console.log(`真实检测：置信度过低 (${realData.confidence}%)`);
      return false;
    }

    // 检查任务特定操作
    const completedTasks = requiredTasks.filter(task => {
      const taskData = realData.taskActions[task as keyof typeof realData.taskActions];
      return taskData && taskData.count > 0;
    });

    if (completedTasks.length > 0) {
      console.log(`真实检测：完成任务 ${completedTasks.join(', ')}`);
      return true;
    }

    // 检查窗口活动
    if (realData.totalClicks >= 1 && realData.windowActivity.mouseActivity > 5) {
      console.log('真实检测：有效的窗口活动');
      return true;
    }

    return false;
  }

  /**
   * 验证传统检测结果
   */
  private validateLegacyDetection(
    legacyData: ClickDetectionResult,
    requiredTasks: TaskType[]
  ): boolean {
    // 检查总点击数
    if (legacyData.totalClicks === 0) {
      return false;
    }

    // 检查任务特定点击
    const taskClicks = requiredTasks.some(task => {
      switch (task) {
        case 'like': return legacyData.likeClicks > 0;
        case 'share': return legacyData.shareClicks > 0;
        case 'follow': return legacyData.followClicks > 0;
        case 'comment': return legacyData.commentClicks > 0;
        default: return false;
      }
    });

    if (taskClicks) {
      console.log('传统检测：检测到任务相关点击');
      return true;
    }

    // 检查检测到的操作
    const hasDetectedActions = legacyData.detectedActions.some(action =>
      requiredTasks.includes(action.action as TaskType) && action.confidence >= 60
    );

    if (hasDetectedActions) {
      console.log('传统检测：检测到高置信度操作');
      return true;
    }

    return false;
  }

  /**
   * 验证智能检测结果
   */
  private validateIntelligentDetection(detectionResults: any): boolean {
    if (!detectionResults) return false;

    // 检查置信度
    if (detectionResults.confidence >= 60) {
      console.log(`智能检测：置信度达标 (${detectionResults.confidence}%)`);
      return true;
    }

    // 检查检测到的操作
    if (detectionResults.detectedActions && detectionResults.detectedActions.length > 0) {
      const highConfidenceActions = detectionResults.detectedActions.filter(
        (action: any) => action.confidence >= 70
      );
      if (highConfidenceActions.length > 0) {
        console.log('智能检测：检测到高置信度操作');
        return true;
      }
    }

    return false;
  }

  /**
   * 验证行为数据
   */
  private validateBehaviorData(behaviorData: any): boolean {
    if (!behaviorData || !behaviorData.interactions) return false;

    const realInteractions = behaviorData.interactions.filter((interaction: any) =>
      ['click', 'input', 'scroll', 'focus', 'keydown'].includes(interaction.type)
    );

    if (realInteractions.length >= 1) {
      console.log('行为数据：检测到真实交互');
      return true;
    }

    return false;
  }

  /**
   * 混合证据验证
   */
  private validateHybridEvidence(
    session: TaskExecutionSession,
    extensionData?: ExtensionDetectionData | null,
    realData?: RealClickData | null,
    legacyData?: ClickDetectionResult | null,
    detectionResults?: any
  ): boolean {
    const duration = Date.now() - session.startTime;
    const durationSeconds = duration / 1000;

    // 时间合理性检查
    if (durationSeconds < 5) {
      console.log('混合验证：时间过短');
      return false;
    }

    let evidenceScore = 0;

    // 扩展式检测证据
    if (extensionData) {
      if (extensionData.clicks.total > 0) evidenceScore += 30;
      if (extensionData.confidence > 50) evidenceScore += 20;
      if (extensionData.performance.userInteractions > 0) evidenceScore += 15;
    }

    // 真实检测证据
    if (realData) {
      if (realData.totalClicks > 0) evidenceScore += 25;
      if (realData.confidence > 40) evidenceScore += 15;
      if (realData.windowActivity.mouseActivity > 3) evidenceScore += 10;
    }

    // 传统检测证据
    if (legacyData) {
      if (legacyData.totalClicks > 0) evidenceScore += 20;
      if (legacyData.detectedActions.length > 0) evidenceScore += 15;
    }

    // 智能检测证据
    if (detectionResults) {
      if (detectionResults.confidence > 30) evidenceScore += 10;
    }

    // 时间加分
    if (durationSeconds >= 8) evidenceScore += 10;
    if (durationSeconds >= 15) evidenceScore += 5;

    // 用户确认加分
    if (session.userConfirmedCompletion) evidenceScore += 25;

    console.log(`混合验证：证据分数 ${evidenceScore}/100`);

    return evidenceScore >= 60; // 60分以上通过
  }

  /**
   * 判断是否应该使用强制验证模式
   */
  private shouldUseForceValidation(session: TaskExecutionSession): boolean {
    const duration = Date.now() - session.startTime;
    const durationSeconds = duration / 1000;

    // 检查是否手动启用了强制验证模式
    if (localStorage.getItem('forceValidationEnabled') === 'true') {
      console.log('🔧 检测到手动启用的强制验证模式');
      return true;
    }

    // 条件1: 执行时间合理（至少8秒）
    if (durationSeconds < 8) {
      return false;
    }

    // 条件2: 检测器都失效了
    const hasDetectionIssues = (session as any).detectionIssues && (session as any).detectionIssues.length >= 3;

    // 条件3: 是跨域场景
    const isCrossDomain = !session.isTrackingBehavior;

    // 条件4: 是已知平台
    const isKnownPlatform = session.platform && session.platform !== 'unknown';

    return hasDetectionIssues && isCrossDomain && isKnownPlatform && durationSeconds >= 8;
  }

  /**
   * 执行强制验证
   */
  private performForceValidation(session: TaskExecutionSession): boolean {
    const duration = Date.now() - session.startTime;
    const durationSeconds = duration / 1000;

    console.log(`🔧 执行强制验证 - 时长: ${durationSeconds}秒, 平台: ${session.platform}`);

    // 基于时间和平台的强制验证
    let validationScore = 0;

    // 时间评分
    if (durationSeconds >= 8) validationScore += 30;
    if (durationSeconds >= 15) validationScore += 20;
    if (durationSeconds >= 30) validationScore += 10;

    // 平台评分
    const platformScores = {
      'bilibili': 25,
      'douyin': 20,
      'kuaishou': 20,
      'xiaohongshu': 15
    };
    validationScore += platformScores[session.platform as keyof typeof platformScores] || 10;

    // 任务类型评分
    const taskTypeScores = {
      'like': 15,
      'follow': 10,
      'share': 5,
      'comment': 5
    };
    session.selectedRequirements.forEach(task => {
      validationScore += taskTypeScores[task] || 5;
    });

    // 用户确认加分
    if (session.userConfirmedCompletion) {
      validationScore += 30;
    }

    console.log(`🔧 强制验证分数: ${validationScore}/100`);

    // 70分以上通过强制验证
    return validationScore >= 70;
  }

  /**
   * 显示检测问题修复工具
   */
  private showDetectionFixTool(
    session: TaskExecutionSession,
    extensionData?: ExtensionDetectionData | null,
    realData?: RealClickData | null,
    legacyClickData?: ClickDetectionResult | null
  ): void {
    console.log('🔧 显示检测问题修复工具');

    // 生成诊断报告
    const diagnostics = detectionDiagnosticsService.diagnoseDetectionSystem(
      session.id,
      session.platform || 'unknown',
      Date.now() - session.startTime,
      extensionData,
      realData,
      legacyClickData,
      null
    );

    // 创建修复工具界面
    const fixToolModal = document.createElement('div');
    fixToolModal.className = 'detection-fix-modal';
    fixToolModal.innerHTML = `
      <div class="modal-overlay">
        <div class="modal-content">
          <div class="modal-header">
            <h3>🔧 检测问题修复工具</h3>
            <button class="close-btn" onclick="this.closest('.detection-fix-modal').remove()">×</button>
          </div>
          <div class="modal-body">
            <div class="problem-description">
              <h4>❌ 检测到的问题</h4>
              <p>由于浏览器跨域安全限制，我们无法直接检测您在${session.platform || '目标网站'}上的操作。</p>
              <ul>
                ${diagnostics.issues.map(issue => `<li>• ${issue}</li>`).join('')}
              </ul>
            </div>

            <div class="solutions-section">
              <h4>💡 解决方案</h4>
              <div class="solution-buttons">
                <button class="solution-btn primary" onclick="enableForceValidation('${session.id}')">
                  🔧 启用强制验证模式
                </button>
                <button class="solution-btn secondary" onclick="enableUserConfirmation('${session.id}')">
                  🤔 启用用户确认模式
                </button>
                <button class="solution-btn info" onclick="openDetectionFixPage()">
                  🛠️ 打开完整修复工具
                </button>
              </div>
            </div>

            <div class="explanation-section">
              <h4>📋 说明</h4>
              <ul>
                <li><strong>强制验证模式</strong>：基于执行时间和行为模式进行智能验证</li>
                <li><strong>用户确认模式</strong>：系统将询问您是否完成了所需操作</li>
                <li><strong>完整修复工具</strong>：提供更多高级修复选项和诊断信息</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .detection-fix-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .modal-content {
        background: white;
        border-radius: 12px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
      }
      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 25px;
        border-bottom: 1px solid #e5e7eb;
      }
      .modal-header h3 {
        margin: 0;
        color: #1f2937;
      }
      .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #6b7280;
      }
      .modal-body {
        padding: 25px;
      }
      .problem-description, .solutions-section, .explanation-section {
        margin-bottom: 25px;
      }
      .problem-description h4, .solutions-section h4, .explanation-section h4 {
        margin: 0 0 15px 0;
        color: #1f2937;
      }
      .problem-description ul, .explanation-section ul {
        margin: 10px 0;
        padding-left: 20px;
      }
      .problem-description li, .explanation-section li {
        margin-bottom: 8px;
        color: #6b7280;
      }
      .solution-buttons {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }
      .solution-btn {
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
      }
      .solution-btn.primary {
        background: #3b82f6;
        color: white;
      }
      .solution-btn.primary:hover {
        background: #2563eb;
      }
      .solution-btn.secondary {
        background: #10b981;
        color: white;
      }
      .solution-btn.secondary:hover {
        background: #059669;
      }
      .solution-btn.info {
        background: #f59e0b;
        color: white;
      }
      .solution-btn.info:hover {
        background: #d97706;
      }
    `;

    document.head.appendChild(style);
    document.body.appendChild(fixToolModal);

    // 添加全局函数
    (window as any).enableForceValidation = (sessionId: string) => {
      localStorage.setItem('forceValidationEnabled', 'true');
      localStorage.setItem('validationMode', 'force');
      alert('✅ 强制验证模式已启用！请重新执行任务。');
      fixToolModal.remove();
    };

    (window as any).enableUserConfirmation = (sessionId: string) => {
      localStorage.setItem('userConfirmationEnabled', 'true');
      localStorage.setItem('validationMode', 'user_confirmation');
      alert('✅ 用户确认模式已启用！系统将在任务结束时询问您是否完成。');
      fixToolModal.remove();
    };

    (window as any).openDetectionFixPage = () => {
      window.open('/detection-fix.html', '_blank', 'width=900,height=700');
    };
  }

  /**
   * 验证真实用户交互（保留原方法作为兜底）
   */
  private validateRealUserInteraction(
    session: TaskExecutionSession,
    detectionResults: any,
    behaviorData: any,
    clickDetectionData?: ClickDetectionResult | null
  ): boolean {
    console.log('🔍 验证真实用户交互...');

    // 检查1: 点击检测数据（新增 - 最高优先级）
    if (clickDetectionData) {
      console.log(`🖱️ 点击检测数据: 总点击=${clickDetectionData.totalClicks}, 点赞=${clickDetectionData.likeClicks}, 分享=${clickDetectionData.shareClicks}`);

      // 检查是否有足够的点击次数
      if (clickDetectionData.totalClicks >= 1) {
        console.log('✅ 检测到用户点击行为');
        return true;
      }

      // 检查是否有特定任务的点击
      const taskClicks = session.selectedRequirements.some(req => {
        switch (req) {
          case 'like': return clickDetectionData.likeClicks > 0;
          case 'share': return clickDetectionData.shareClicks > 0;
          case 'follow': return clickDetectionData.followClicks > 0;
          case 'comment': return clickDetectionData.commentClicks > 0;
          default: return false;
        }
      });

      if (taskClicks) {
        console.log('✅ 检测到任务相关点击');
        return true;
      }

      // 检查检测到的操作
      if (clickDetectionData.detectedActions.length > 0) {
        const highConfidenceActions = clickDetectionData.detectedActions.filter(
          action => action.confidence >= 70
        );
        if (highConfidenceActions.length > 0) {
          console.log('✅ 点击检测发现高置信度操作');
          return true;
        }
      }
    }

    // 检查2: 是否有直接检测到的操作
    if (detectionResults && detectionResults.detectedActions && detectionResults.detectedActions.length > 0) {
      const goodConfidenceActions = detectionResults.detectedActions.filter(
        (action: any) => action.confidence >= 60
      );
      if (goodConfidenceActions.length > 0) {
        console.log('✅ 检测到较高置信度操作');
        return true;
      }
    }

    // 检查2: 是否有行为数据证明用户交互
    if (behaviorData && behaviorData.interactions) {
      const realInteractions = behaviorData.interactions.filter((interaction: any) =>
        ['click', 'input', 'scroll', 'focus', 'keydown'].includes(interaction.type)
      );

      if (realInteractions.length >= 1) {
        console.log('✅ 检测到用户交互行为');
        return true;
      }
    }

    // 检查3: 窗口活动数据（新增 - 跨域友好）
    if (session.windowActivityData) {
      const activity = session.windowActivityData;
      const durationSeconds = activity.totalDuration / 1000;

      // 基于窗口活动的验证
      if (durationSeconds >= 10 && (
        activity.urlChangeCount > 0 ||
        activity.focusChangeCount > 2 ||
        activity.userInteractionIndicators > 1 ||
        activity.windowActiveTime > 5000
      )) {
        console.log('✅ 检测到窗口活动证据');
        return true;
      }
    }

    // 检查4: 执行时间和检测结果的组合验证
    const duration = Date.now() - session.startTime;
    const durationSeconds = duration / 1000;

    if (durationSeconds >= 12) {
      // 检查是否有窗口状态变化
      if (detectionResults && detectionResults.indicators) {
        const indicators = detectionResults.indicators;
        if (indicators.windowFocusTime > 6000 || indicators.urlChanges > 0 || indicators.focusEvents > 0) {
          console.log('✅ 长时间执行且有窗口活动');
          return true;
        }
      }
    }

    // 检查5: DOM变化证明
    if (behaviorData && behaviorData.domChanges && behaviorData.domChanges.length > 0) {
      const meaningfulChanges = behaviorData.domChanges.filter((change: any) =>
        ['click', 'input', 'form_submit', 'navigation'].includes(change.type)
      );

      if (meaningfulChanges.length > 0) {
        console.log('✅ 检测到有意义的DOM变化');
        return true;
      }
    }

    // 检查6: 跨域场景的智能验证（增强）
    if (!behaviorData || !behaviorData.interactions) {
      // 跨域场景下的多重验证
      if (durationSeconds >= 8) {
        // 条件1: 有检测结果且置信度合理
        if (detectionResults && detectionResults.confidence >= 35) {
          console.log('✅ 跨域场景：检测结果验证通过');
          return true;
        }

        // 条件2: 时间足够长且有窗口活动
        if (durationSeconds >= 15 && session.windowActivityData) {
          const activity = session.windowActivityData;
          if (activity.windowActiveTime > 8000 || activity.userInteractionIndicators > 0) {
            console.log('✅ 跨域场景：时间和活动验证通过');
            return true;
          }
        }

        // 条件3: 用户主动确认（如果实现了确认机制）
        if (session.userConfirmedCompletion) {
          console.log('✅ 跨域场景：用户主动确认');
          return true;
        }
      }
    }

    console.log('❌ 未检测到足够的真实用户交互证据');
    return false;
  }

  /**
   * 询问用户确认任务完成
   */
  private async askUserConfirmation(session: TaskExecutionSession, durationSeconds: number): Promise<boolean> {
    return new Promise((resolve) => {
      const taskNames = {
        'like': '点赞',
        'share': '分享',
        'comment': '评论',
        'follow': '关注'
      };

      const taskList = session.selectedRequirements.map(req => taskNames[req] || req).join('、');

      const modal = document.createElement('div');
      modal.className = 'task-confirmation-modal';
      modal.innerHTML = `
        <div class="modal-overlay">
          <div class="modal-content">
            <div class="modal-header">
              <h3>🤔 任务完成确认</h3>
            </div>
            <div class="modal-body">
              <p>您刚才在任务窗口中执行了 <strong>${durationSeconds}秒</strong>，</p>
              <p>请确认是否已经完成了以下操作：</p>
              <ul class="task-list">
                ${session.selectedRequirements.map(req => `
                  <li class="task-item">
                    <span class="task-icon">${this.getTaskIcon(req)}</span>
                    <span class="task-name">${taskNames[req] || req}</span>
                  </li>
                `).join('')}
              </ul>
              <div class="confirmation-note">
                <p>💡 <strong>提示：</strong>如果您确实完成了上述操作，请点击"是的，我已完成"。</p>
                <p>如果没有完成或不确定，请点击"没有完成"并重新执行任务。</p>
              </div>
            </div>
            <div class="modal-footer">
              <button class="btn btn-success" id="confirm-yes">
                ✅ 是的，我已完成
              </button>
              <button class="btn btn-secondary" id="confirm-no">
                ❌ 没有完成
              </button>
            </div>
          </div>
        </div>
      `;

      // 添加样式
      const style = document.createElement('style');
      style.textContent = `
        .task-confirmation-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 10000;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .modal-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .modal-content {
          background: white;
          border-radius: 12px;
          padding: 24px;
          max-width: 480px;
          width: 90%;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .modal-header h3 {
          margin: 0 0 16px 0;
          color: #333;
          font-size: 20px;
        }
        .modal-body p {
          margin: 8px 0;
          color: #666;
          line-height: 1.5;
        }
        .task-list {
          list-style: none;
          padding: 0;
          margin: 16px 0;
          background: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
        }
        .task-item {
          display: flex;
          align-items: center;
          margin: 8px 0;
          font-weight: 500;
          color: #333;
        }
        .task-icon {
          margin-right: 8px;
          font-size: 18px;
        }
        .confirmation-note {
          background: #e3f2fd;
          border-radius: 8px;
          padding: 12px;
          margin: 16px 0;
          border-left: 4px solid #2196f3;
        }
        .confirmation-note p {
          margin: 4px 0;
          font-size: 14px;
        }
        .modal-footer {
          display: flex;
          gap: 12px;
          margin-top: 24px;
        }
        .btn {
          flex: 1;
          padding: 12px 24px;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }
        .btn-success {
          background: #4caf50;
          color: white;
        }
        .btn-success:hover {
          background: #45a049;
        }
        .btn-secondary {
          background: #6c757d;
          color: white;
        }
        .btn-secondary:hover {
          background: #5a6268;
        }
      `;

      document.head.appendChild(style);
      document.body.appendChild(modal);

      // 绑定事件
      const confirmYes = modal.querySelector('#confirm-yes');
      const confirmNo = modal.querySelector('#confirm-no');

      const cleanup = () => {
        document.body.removeChild(modal);
        document.head.removeChild(style);
      };

      confirmYes?.addEventListener('click', () => {
        cleanup();
        resolve(true);
      });

      confirmNo?.addEventListener('click', () => {
        cleanup();
        resolve(false);
      });

      // 10秒后自动关闭，默认为未完成
      setTimeout(() => {
        if (document.body.contains(modal)) {
          cleanup();
          resolve(false);
        }
      }, 10000);
    });
  }

  /**
   * 获取任务图标
   */
  private getTaskIcon(taskType: TaskType): string {
    const icons = {
      'like': '👍',
      'share': '📤',
      'comment': '💬',
      'follow': '➕'
    };
    return icons[taskType] || '✅';
  }

  /**
   * 获取用户信誉加分
   */
  private getReputationBonus(session: TaskExecutionSession): number {
    // 移除信誉加分，防止被滥用
    return 0;
  }

  /**
   * 计算智能推断置信度（平衡版本 - 安全与可用性并重）
   */
  private calculateIntelligentConfidence(session: TaskExecutionSession, duration: number): number {
    let confidence = 0;

    // 平衡的基础时间分数
    if (duration > 8000) confidence += 20;   // 8秒以上给分
    if (duration > 15000) confidence += 25;  // 15秒以上
    if (duration > 30000) confidence += 20;  // 30秒以上
    if (duration > 60000) confidence += 10;  // 1分钟以上

    // 适度的时间惩罚
    if (duration < 5000) confidence -= 50;   // 5秒以下严重减分
    if (duration < 8000) confidence -= 20;   // 8秒以下减分
    if (duration > 300000) confidence -= 30; // 5分钟以上减分

    // 适度的平台加分
    const platformBonus = this.getPlatformReliabilityBonus(session.platform);
    confidence += Math.min(platformBonus, 10); // 最多给10分平台加分

    // 平衡的任务类型调整
    const taskBonus = {
      'like': 15,      // 点赞相对容易
      'share': 12,     // 分享稍难
      'follow': 10,    // 关注需要更多操作
      'comment': 8     // 评论最复杂
    };

    const avgTaskBonus = session.selectedRequirements.reduce((sum, req) =>
      sum + (taskBonus[req] || 0), 0) / session.selectedRequirements.length;
    confidence += avgTaskBonus;

    // 任务数量调整
    if (session.selectedRequirements.length === 1) confidence += 8; // 单任务加分
    if (session.selectedRequirements.length > 2) confidence -= 15;  // 多任务减分

    // 平衡限制：最高75%，最低通过阈值50%
    return Math.max(0, Math.min(75, confidence));
  }

  /**
   * 基础验证（平衡版本 - 安全与可用性并重）
   */
  private performBasicVerification(
    session: TaskExecutionSession,
    duration: number,
    durationSeconds: number
  ): ExecutionResult {
    console.log(`🔍 执行平衡基础验证 - 时长: ${durationSeconds}秒`);

    // 拒绝明显过短的执行时间
    if (durationSeconds < 5) {
      return {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: `❌ 任务执行时间过短（${durationSeconds}秒），请确保实际完成所需操作。`,
        proofRequired: true,
        needsManualReview: true
      };
    }

    // 尝试智能推断，使用平衡的阈值
    const intelligentResult = this.generateIntelligentDetection(session, duration);
    if (intelligentResult && intelligentResult.confidence >= 50) {
      console.log('🧠 平衡基础验证使用智能推断结果');
      return this.processDetectionResults(session, intelligentResult, durationSeconds);
    }

    // 如果智能推断失败，使用增强的回退验证
    console.log('🔄 智能推断未通过，使用回退验证');
    return this.performEnhancedFallbackVerification(session, duration, durationSeconds);
  }

  /**
   * 获取置信度阈值
   */
  private getConfidenceThreshold(requirement: TaskType, method: string): number {
    const baseThresholds = {
      like: 50,
      share: 45,    // 分享检测更困难，降低阈值
      comment: 55,
      follow: 50
    };

    let threshold = baseThresholds[requirement] || 50;

    // 根据检测方法调整阈值
    if (method === 'intelligent_cross_domain') {
      threshold -= 10; // 智能推断降低阈值
    }

    return Math.max(30, threshold); // 最低30%
  }

  /**
   * 验证分享操作
   */
  private verifyShareAction(detectionResults: any, durationSeconds: number): { completed: boolean; confidence: number } {
    let confidence = 0;
    const indicators = detectionResults.indicators || {};

    // 检查是否有导航行为（可能表示分享操作）
    const hasNavigation = detectionResults.detectedActions?.some((action: any) =>
      action.action === 'navigation'
    );
    if (hasNavigation) {
      confidence += 25;
    }

    // 检查时间模式（分享通常需要较长时间）
    if (durationSeconds >= 10 && durationSeconds <= 120) {
      confidence += 20;
      if (durationSeconds >= 15) {
        confidence += 10; // 15秒以上额外加分
      }
    }

    // 检查用户交互指标
    if (indicators.userInteractionLikely) {
      confidence += 20;
    }

    // 检查窗口焦点时间
    if (indicators.windowFocusTime > 8000) {
      confidence += 15;
    }

    // 检查平台特定指标
    if (indicators.platformOptimized) {
      confidence += 10;
    }

    return {
      completed: confidence >= 45, // 分享任务的特殊阈值
      confidence: Math.min(confidence, 85)
    };
  }

  /**
   * 计算积分奖励
   */
  private calculatePoints(requirements: TaskType[]): number {
    const pointsMap: Record<TaskType, number> = {
      like: 10,
      share: 15,
      comment: 20,
      follow: 25
    };

    return requirements.reduce((total, req) => total + (pointsMap[req] || 0), 0);
  }

  /**
   * 判断是否需要提供证明
   */
  private shouldRequireProof(session: TaskExecutionSession): boolean {
    const totalPoints = this.calculatePoints(session.selectedRequirements);
    const duration = Date.now() - session.startTime;

    // 需要证明的情况：
    // 1. 高价值任务（50积分以上）
    // 2. 多种需求类型（超过2种）
    // 3. 快速完成（5秒内）
    // 4. 新用户或低信誉用户的任务

    return (
      totalPoints >= 50 ||
      session.selectedRequirements.length > 2 ||
      duration < this.QUICK_COMPLETION_THRESHOLD
    );
  }

  /**
   * 显示完成确认对话框
   */
  private showCompletionDialog(session: TaskExecutionSession, result: ExecutionResult): void {
    // 触发自定义事件，让UI组件处理对话框显示
    const event = new CustomEvent('taskExecutionComplete', {
      detail: { session, result }
    });
    window.dispatchEvent(event);
  }

  /**
   * 提交任务完成
   */
  async submitTaskCompletion(
    sessionId: string,
    proof?: ExecutionProof
  ): Promise<TaskExecution> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error('会话不存在或已过期');
    }

    try {
      // 尝试调用真实API
      const response = await api.post('/tasks/execute', {
        task_id: session.taskId,
        selected_requirements: session.selectedRequirements,
        execution_duration: Date.now() - session.startTime,
        proof: proof ? {
          screenshots: proof.screenshots?.map(file => file.name),
          description: proof.description,
          timestamp: proof.timestamp
        } : undefined
      });

      this.activeSessions.delete(sessionId);
      return response.data;
    } catch (error: any) {
      // 如果是404错误（API不存在），创建模拟响应
      if (error.response?.status === 404) {
        console.log('API endpoint not found, using mock response');

        // 创建模拟的任务执行记录
        const mockTaskExecution: TaskExecution = {
          id: Date.now(),
          task_id: session.taskId,
          user_id: 1, // 模拟用户ID
          selected_requirements: session.selectedRequirements,
          execution_duration: Date.now() - session.startTime,
          status: 'completed' as ExecutionStatus,
          points_earned: this.calculatePoints(session.selectedRequirements),
          proof: proof ? {
            screenshots: proof.screenshots?.map(file => file.name) || [],
            description: proof.description || '',
            timestamp: proof.timestamp
          } : undefined,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        this.activeSessions.delete(sessionId);

        // 模拟延迟以提供真实感
        await new Promise(resolve => setTimeout(resolve, 500));

        return mockTaskExecution;
      }

      // 对于其他错误，抛出原始错误
      throw new Error(error.response?.data?.message || '提交任务失败');
    }
  }

  /**
   * 处理会话超时
   */
  private handleSessionTimeout(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (session && session.status === 'monitoring') {
      session.status = 'timeout';
      if (session.windowRef && !session.windowRef.closed) {
        session.windowRef.close();
      }
      
      this.showCompletionDialog(session, {
        success: false,
        completedRequirements: [],
        failedRequirements: session.selectedRequirements,
        pointsEarned: 0,
        message: '任务执行超时，请重新开始'
      });
      
      this.activeSessions.delete(sessionId);
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取活跃会话
   */
  getActiveSession(sessionId: string): TaskExecutionSession | undefined {
    return this.activeSessions.get(sessionId);
  }

  /**
   * 取消任务执行
   */
  cancelExecution(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      if (session.windowRef && !session.windowRef.closed) {
        session.windowRef.close();
      }
      this.activeSessions.delete(sessionId);
    }
  }
}

export const taskExecutionService = new TaskExecutionService();
