# 互助点赞平台 - 项目初始化脚本 (PowerShell)

Write-Host "🚀 开始初始化互助点赞平台..." -ForegroundColor Green

# 检查Node.js版本
function Check-NodeVersion {
    try {
        $nodeVersion = node -v
        $versionNumber = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')
        
        if ($versionNumber -lt 18) {
            Write-Host "❌ Node.js 版本过低，需要 18+，当前版本: $nodeVersion" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "✅ Node.js 版本检查通过: $nodeVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Node.js 未安装，请先安装 Node.js 18+" -ForegroundColor Red
        exit 1
    }
}

# 检查PostgreSQL
function Check-PostgreSQL {
    try {
        psql --version | Out-Null
        Write-Host "✅ PostgreSQL 已安装" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  PostgreSQL 未安装，请确保数据库服务可用" -ForegroundColor Yellow
        Write-Host "   可以使用 Docker: docker run --name postgres -e POSTGRES_PASSWORD=postgres123 -p 5432:5432 -d postgres:15" -ForegroundColor Yellow
    }
}

# 安装依赖
function Install-Dependencies {
    Write-Host "📦 安装项目依赖..." -ForegroundColor Blue
    
    # 安装根目录依赖
    npm install
    
    # 安装后端依赖
    Write-Host "📦 安装后端依赖..." -ForegroundColor Blue
    Set-Location backend
    npm install
    Set-Location ..
    
    # 安装前端依赖
    Write-Host "📦 安装前端依赖..." -ForegroundColor Blue
    Set-Location frontend
    npm install
    Set-Location ..
    
    Write-Host "✅ 依赖安装完成" -ForegroundColor Green
}

# 配置环境变量
function Setup-Environment {
    Write-Host "⚙️  配置环境变量..." -ForegroundColor Blue
    
    # 后端环境变量
    if (-not (Test-Path "backend\.env")) {
        Copy-Item "backend\.env.example" "backend\.env"
        Write-Host "✅ 后端环境变量文件已创建: backend\.env" -ForegroundColor Green
        Write-Host "   请根据需要修改数据库连接等配置" -ForegroundColor Yellow
    }
    else {
        Write-Host "✅ 后端环境变量文件已存在" -ForegroundColor Green
    }
    
    # 前端环境变量
    if (-not (Test-Path "frontend\.env")) {
        Copy-Item "frontend\.env.example" "frontend\.env"
        Write-Host "✅ 前端环境变量文件已创建: frontend\.env" -ForegroundColor Green
    }
    else {
        Write-Host "✅ 前端环境变量文件已存在" -ForegroundColor Green
    }
}

# 初始化数据库
function Setup-Database {
    Write-Host "🗄️  初始化数据库..." -ForegroundColor Blue
    
    Set-Location backend
    
    # 等待数据库连接
    Write-Host "等待数据库连接..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    
    # 运行数据库迁移
    try {
        npm run db:migrate
        Write-Host "✅ 数据库迁移完成" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  数据库迁移失败，请检查数据库连接配置" -ForegroundColor Yellow
        Set-Location ..
        return $false
    }
    
    # 插入种子数据
    try {
        npm run db:seed
        Write-Host "✅ 种子数据插入完成" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  种子数据插入失败" -ForegroundColor Yellow
    }
    
    Set-Location ..
    return $true
}

# 构建项目
function Build-Project {
    Write-Host "🔨 构建项目..." -ForegroundColor Blue
    
    # 构建后端
    Set-Location backend
    npm run build
    Set-Location ..
    Write-Host "✅ 后端构建完成" -ForegroundColor Green
    
    # 构建前端
    Set-Location frontend
    npm run build
    Set-Location ..
    Write-Host "✅ 前端构建完成" -ForegroundColor Green
}

# 显示启动信息
function Show-StartupInfo {
    Write-Host ""
    Write-Host "🎉 项目初始化完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 接下来的步骤：" -ForegroundColor Cyan
    Write-Host "1. 检查并修改环境变量配置：" -ForegroundColor White
    Write-Host "   - backend\.env (数据库连接、JWT密钥等)" -ForegroundColor Gray
    Write-Host "   - frontend\.env (API地址等)" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. 启动开发服务器：" -ForegroundColor White
    Write-Host "   npm run dev" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "3. 访问应用：" -ForegroundColor White
    Write-Host "   - 前端: http://localhost:5173" -ForegroundColor Gray
    Write-Host "   - 后端API: http://localhost:3000" -ForegroundColor Gray
    Write-Host "   - API文档: http://localhost:3000/api-docs" -ForegroundColor Gray
    Write-Host ""
    Write-Host "4. 测试账号：" -ForegroundColor White
    Write-Host "   - 邮箱: <EMAIL>" -ForegroundColor Gray
    Write-Host "   - 密码: Password123" -ForegroundColor Gray
    Write-Host ""
    Write-Host "📚 更多信息请查看 README.md 和 docs\ 目录" -ForegroundColor Cyan
}

# 主函数
function Main {
    Write-Host "互助点赞平台 - 项目初始化" -ForegroundColor Magenta
    Write-Host "================================" -ForegroundColor Magenta
    
    Check-NodeVersion
    Check-PostgreSQL
    Install-Dependencies
    Setup-Environment
    
    # 询问是否初始化数据库
    $initDb = Read-Host "是否初始化数据库？(y/N)"
    if ($initDb -eq "y" -or $initDb -eq "Y") {
        Setup-Database
    }
    else {
        Write-Host "⏭️  跳过数据库初始化" -ForegroundColor Yellow
    }
    
    # 询问是否构建项目
    $buildProject = Read-Host "是否构建项目？(y/N)"
    if ($buildProject -eq "y" -or $buildProject -eq "Y") {
        Build-Project
    }
    else {
        Write-Host "⏭️  跳过项目构建" -ForegroundColor Yellow
    }
    
    Show-StartupInfo
}

# 运行主函数
Main
