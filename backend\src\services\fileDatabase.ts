import { promises as fs } from 'fs';
import path from 'path';

const dataDir = path.join(__dirname, '..', '..', 'data');

type Entity = 'users' | 'tasks' | 'taskExecutions' | 'pointTransactions';

// Ensure data directory and files exist
const initialize = async () => {
    try {
        await fs.access(dataDir);
    } catch {
        await fs.mkdir(dataDir);
    }

    const files: Entity[] = ['users', 'tasks', 'taskExecutions', 'pointTransactions'];
    for (const file of files) {
        const filePath = path.join(dataDir, `${file}.json`);
        try {
            await fs.access(filePath);
        } catch {
            await fs.writeFile(filePath, '[]', 'utf-8');
        }
    }
};

initialize();

async function readData<T>(entity: Entity): Promise<T[]> {
    const filePath = path.join(dataDir, `${entity}.json`);
    try {
        const fileContent = await fs.readFile(filePath, 'utf-8');
        return JSON.parse(fileContent) as T[];
    } catch (error) {
        // If file doesn't exist or is empty, return an empty array
        if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
            return [];
        }
        throw error;
    }
}

async function writeData<T>(entity: Entity, data: T[]): Promise<void> {
    const filePath = path.join(dataDir, `${entity}.json`);
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
}

export const FileDB = {
    getAll: async <T>(entity: Entity): Promise<T[]> => {
        return await readData<T>(entity);
    },

    getById: async <T extends { id: number }>(entity: Entity, id: number): Promise<T | null> => {
        const items = await readData<T>(entity);
        return items.find(item => item.id === id) || null;
    },
    
    find: async <T>(entity: Entity, predicate: (item: T) => boolean): Promise<T[]> => {
        const items = await readData<T>(entity);
        return items.filter(predicate);
    },
    
    findOne: async <T>(entity: Entity, predicate: (item: T) => boolean): Promise<T | null> => {
        const items = await readData<T>(entity);
        return items.find(predicate) || null;
    },

    create: async <T extends { id: number }>(entity: Entity, newItem: Omit<T, 'id'>): Promise<T> => {
        const items = await readData<T>(entity);
        const maxId = items.reduce((max, item) => item.id > max ? item.id : max, 0);
        const newEntry = { ...newItem, id: maxId + 1 } as T;
        items.push(newEntry);
        await writeData(entity, items);
        return newEntry;
    },

    update: async <T extends { id: number }>(entity: Entity, id: number, updates: Partial<T>): Promise<T | null> => {
        const items = await readData<T>(entity);
        const index = items.findIndex(item => item.id === id);
        if (index === -1) {
            return null;
        }
        const updatedItem = { ...items[index], ...updates };
        items[index] = updatedItem;
        await writeData(entity, items);
        return updatedItem;
    },
    
    delete: async <T extends { id: number }>(entity: Entity, id: number): Promise<boolean> => {
        let items = await readData<T>(entity);
        const initialLength = items.length;
        items = items.filter(item => item.id !== id);
        if (items.length === initialLength) {
            return false; // No item was deleted
        }
        await writeData(entity, items);
        return true;
    }
}; 