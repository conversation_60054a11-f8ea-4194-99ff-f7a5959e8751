import React, { useState } from 'react';
import { useQuery } from 'react-query';
import {
  Coins,
  TrendingUp,
  TrendingDown,
  Filter,
  Search,
  Download,
  RefreshCw,
  Award,
  ShoppingCart,
  Gift,
  Target,
  Users,
  ArrowUpRight,
  ArrowDownRight,
} from 'lucide-react';
import { PointTransaction, TransactionType } from '@/types';
import { useAuthStore } from '@/store/authStore';
import { PointsService } from '@/services/pointsService';

type FilterType = 'all' | 'earned' | 'spent';
type PeriodType = 'week' | 'month' | 'quarter' | 'year';

const PointsPage: React.FC = () => {
  const { user, addTestPoints } = useAuthStore();
  const [filter, setFilter] = useState<FilterType>('all');
  const [period, setPeriod] = useState<PeriodType>('month');
  const [searchQuery, setSearchQuery] = useState('');

  // 获取积分统计
  const { data: pointStats, isLoading: pointStatsLoading } = useQuery(
    ['point-stats', user?.id],
    async () => {
      return await PointsService.getPointsStats();
    },
    {
      enabled: !!user,
      staleTime: 5 * 60 * 1000, // 5分钟
    }
  );

  // 获取积分交易记录
  const { data: pointTransactions, isLoading: pointTransactionsLoading } = useQuery(
    ['point-transactions', user?.id, filter],
    async () => {
      return await PointsService.getPointsTransactions();
    },
    {
      enabled: !!user,
      staleTime: 2 * 60 * 1000, // 2分钟
    }
  );

  // 过滤交易记录
  const getFilteredTransactions = () => {
      if (!pointTransactions?.data) return [];

      let filtered = pointTransactions.data;

      // 类型过滤
      if (filter === 'earned') {
        filtered = pointTransactions.data.filter(t => t.amount > 0);
      } else if (filter === 'spent') {
        filtered = pointTransactions.data.filter(t => t.amount < 0);
      }

    return filtered;
  };

  // 获取交易类型样式
  const getTransactionTypeStyle = (type: TransactionType) => {
    switch (type) {
      case 'task_reward':
        return { icon: Target, color: 'text-green-600', bg: 'bg-green-100' };
      case 'task_publish':
        return { icon: ShoppingCart, color: 'text-blue-600', bg: 'bg-blue-100' };
      case 'daily_bonus':
        return { icon: Gift, color: 'text-purple-600', bg: 'bg-purple-100' };
      case 'referral_bonus':
        return { icon: Users, color: 'text-orange-600', bg: 'bg-orange-100' };
      default:
        return { icon: Coins, color: 'text-gray-600', bg: 'bg-gray-100' };
    }
  };

  // 获取交易类型文本
  const getTransactionTypeText = (type: TransactionType) => {
    switch (type) {
      case 'task_reward':
        return '任务奖励';
      case 'task_publish':
        return '发布任务';
      case 'daily_bonus':
        return '每日奖励';
      case 'referral_bonus':
        return '邀请奖励';
      default:
        return '其他';
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 格式化相对时间
  const formatRelativeTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInHours < 48) {
      return '昨天';
    } else {
      return formatDate(dateString);
    }
  };

  const filteredTransactions = getFilteredTransactions();
  const isLoading = pointStatsLoading || pointTransactionsLoading;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">积分记录</h1>
          <p className="text-gray-600 mt-1">查看您的积分获取和消费记录</p>
        </div>
        <div className="flex items-center space-x-2">
          {/* Debug button for testing */}
          <button
            onClick={() => addTestPoints(100)}
            className="px-3 py-1 text-xs bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
          >
            +100 测试积分
          </button>
          <button className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
            <RefreshCw className="w-4 h-4" />
          </button>
          <button className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
            <Download className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 积分统计卡片 */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded mb-1"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* 当前余额 */}
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">当前余额</p>
                <p className="text-2xl font-bold text-gray-900">
                  {pointStats?.total_points || 0}
                </p>
                <p className="text-xs text-gray-500 mt-1">积分</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Coins className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          {/* 本期获得 */}
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">本期获得</p>
                <p className="text-2xl font-bold text-green-600">
                  +{pointStats?.earned_this_month || 0}
                </p>
                <div className="flex items-center mt-1">
                  <ArrowUpRight className="w-3 h-3 text-green-500 mr-1" />
                  <p className="text-xs text-green-500">
                    {(pointStats?.earned_this_week || 0) > (pointStats?.earned_today || 0) ? '+' : ''}0%
                  </p>
                </div>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          {/* 本期消费 */}
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">本期消费</p>
                <p className="text-2xl font-bold text-red-600">
                  -{pointStats?.spent_this_month || 0}
                </p>
                <div className="flex items-center mt-1">
                  <ArrowDownRight className="w-3 h-3 text-red-500 mr-1" />
                  <p className="text-xs text-red-500">
                    {(pointStats?.spent_this_week || 0) > (pointStats?.spent_today || 0) ? '+' : ''}0%
                  </p>
                </div>
              </div>
              <div className="p-3 bg-red-100 rounded-full">
                <TrendingDown className="w-6 h-6 text-red-600" />
              </div>
            </div>
          </div>

          {/* 累计获得 */}
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">累计获得</p>
                <p className="text-2xl font-bold text-gray-900">
                  {pointStats?.total_points || 0}
                </p>
                <p className="text-xs text-gray-500 mt-1">历史总计</p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Award className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 过滤和搜索 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="搜索交易记录..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as FilterType)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">全部记录</option>
            <option value="earned">收入记录</option>
            <option value="spent">支出记录</option>
          </select>
          <select
            value={period}
            onChange={(e) => setPeriod(e.target.value as PeriodType)}
            className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="quarter">本季度</option>
            <option value="year">本年</option>
          </select>
        </div>
      </div>

      {/* 交易记录列表 */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">交易记录</h2>
          <span className="text-sm text-gray-500">
            共 {filteredTransactions.length} 条记录
          </span>
        </div>

        {pointTransactionsLoading ? (
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3 p-3 animate-pulse">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="h-6 bg-gray-200 rounded w-16"></div>
              </div>
            ))}
          </div>
        ) : filteredTransactions.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-4xl">💰</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || filter !== 'all' ? '没有找到匹配的记录' : '暂无交易记录'}
            </h3>
            <p className="text-gray-500">
              {filter === 'earned'
                ? '您还没有获得任何积分'
                : filter === 'spent'
                ? '您还没有消费任何积分'
                : '开始完成任务来获得积分吧'}
            </p>
          </div>
        ) : (
          <div className="space-y-1">
            {filteredTransactions.map((transaction: PointTransaction) => {
              const typeStyle = getTransactionTypeStyle(transaction.transaction_type);
              const Icon = typeStyle.icon;
              
              return (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 ${typeStyle.bg} rounded-full`}>
                      <Icon className={`w-4 h-4 ${typeStyle.color}`} />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">
                        {transaction.description}
                      </p>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <span>{getTransactionTypeText(transaction.transaction_type)}</span>
                        <span>·</span>
                        <span>{formatRelativeTime(transaction.created_at)}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                    </p>
                    <p className="text-xs text-gray-500">积分</p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default PointsPage;
