import React, { useState, useRef } from 'react';
import { X, Upload, Camera, AlertCircle, CheckCircle } from 'lucide-react';
import { ExecutionResult, ExecutionProof } from '@/services/taskExecutionService';
import { taskVerificationService } from '@/services/taskVerificationService';

interface ProofSubmissionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (proof: ExecutionProof) => void;
  result: ExecutionResult;
  sessionId: string;
}

export const ProofSubmissionDialog: React.FC<ProofSubmissionDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  result,
  sessionId
}) => {
  const [screenshots, setScreenshots] = useState<File[]>([]);
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  if (!isOpen) return null;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter(file => {
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        alert(`文件 ${file.name} 不是有效的图片格式`);
        return false;
      }
      // 检查文件大小（最大10MB）
      if (file.size > 10 * 1024 * 1024) {
        alert(`文件 ${file.name} 过大，请选择小于10MB的图片`);
        return false;
      }
      return true;
    });

    if (validFiles.length > 0) {
      setScreenshots(prev => [...prev, ...validFiles]);
      
      // 生成预览URL
      const newPreviewUrls = validFiles.map(file => URL.createObjectURL(file));
      setPreviewUrls(prev => [...prev, ...newPreviewUrls]);
    }
  };

  const removeScreenshot = (index: number) => {
    setScreenshots(prev => prev.filter((_, i) => i !== index));
    setPreviewUrls(prev => {
      // 释放URL对象
      URL.revokeObjectURL(prev[index]);
      return prev.filter((_, i) => i !== index);
    });
  };

  const handleSubmit = async () => {
    if (result.proofRequired && screenshots.length === 0) {
      alert('请至少上传一张完成截图');
      return;
    }

    setIsSubmitting(true);
    try {
      const proof: ExecutionProof = {
        screenshots,
        description: description.trim() || undefined,
        timestamp: Date.now()
      };

      // 如果需要人工审核，提交审核请求
      if (result.needsManualReview) {
        await taskVerificationService.submitManualReview(
          sessionId,
          result.completedRequirements[0], // 使用第一个完成的任务类型
          {
            screenshots,
            userInteractions: [], // 这里可以传入更多证据
            domChanges: [],
            behaviorMetrics: {
              totalTime: 0,
              activeTime: 0,
              clickCount: 0,
              scrollDistance: 0,
              mouseMovements: 0,
              keystrokes: 0,
              focusEvents: 0
            }
          },
          description
        );
      }

      onSubmit(proof);
    } catch (error) {
      console.error('提交证明失败:', error);
      alert('提交失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // 清理预览URL
    previewUrls.forEach(url => URL.revokeObjectURL(url));
    setPreviewUrls([]);
    setScreenshots([]);
    setDescription('');
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {result.needsManualReview ? '提交审核证明' : '任务完成证明'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6">
          {/* 结果状态 */}
          <div className={`flex items-center gap-3 p-4 rounded-lg mb-6 ${
            result.success 
              ? 'bg-green-50 text-green-800' 
              : 'bg-yellow-50 text-yellow-800'
          }`}>
            {result.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            <div>
              <p className="font-medium">{result.message}</p>
              {result.needsManualReview && (
                <p className="text-sm mt-1">
                  您的任务将进入人工审核流程，审核通过后积分将自动发放。
                </p>
              )}
            </div>
          </div>

          {/* 任务详情 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">任务详情</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-600">完成的操作：</span>
                  <p className="font-medium">
                    {result.completedRequirements.map(req => {
                      const names = { like: '点赞', share: '分享', comment: '评论', follow: '关注' };
                      return names[req];
                    }).join('、') || '无'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-600">获得积分：</span>
                  <p className="font-medium text-blue-600">{result.pointsEarned} 分</p>
                </div>
              </div>
            </div>
          </div>

          {/* 截图上传 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">
              上传完成截图
              {result.proofRequired && <span className="text-red-500 ml-1">*</span>}
            </h3>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleFileSelect}
                className="hidden"
              />
              
              <div className="flex flex-col items-center">
                <Upload className="w-12 h-12 text-gray-400 mb-4" />
                <p className="text-gray-600 mb-2">
                  点击上传或拖拽图片到此处
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  支持 JPG、PNG、GIF 格式，单个文件不超过 10MB
                </p>
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  选择文件
                </button>
              </div>
            </div>

            {/* 截图预览 */}
            {screenshots.length > 0 && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">已选择 {screenshots.length} 张图片：</p>
                <div className="grid grid-cols-3 gap-4">
                  {previewUrls.map((url, index) => (
                    <div key={index} className="relative">
                      <img
                        src={url}
                        alt={`截图 ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg border"
                      />
                      <button
                        onClick={() => removeScreenshot(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* 说明文字 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">补充说明</h3>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="请描述您完成的操作，或提供其他相关信息..."
              className="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              maxLength={500}
            />
            <p className="text-sm text-gray-500 mt-1">
              {description.length}/500 字符
            </p>
          </div>

          {/* 提示信息 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-blue-900 mb-2">截图要求：</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 截图应清晰显示您完成的操作（如点赞按钮变色、关注状态变化等）</li>
              <li>• 请确保截图包含页面URL或其他可识别信息</li>
              <li>• 多个操作请分别截图，便于审核</li>
              <li>• 截图应为原始图片，请勿使用修图软件处理</li>
            </ul>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || (result.proofRequired && screenshots.length === 0)}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? '提交中...' : '确认提交'}
          </button>
        </div>
      </div>
    </div>
  );
};
