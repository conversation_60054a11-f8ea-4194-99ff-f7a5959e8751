import { createClient } from 'redis';
import logger from '@/utils/logger';

export const redisClient = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});

redisClient.on('error', (err) => logger.error('Redis Client Error', err));

export const initRedis = async () => {
  try {
    await redisClient.connect();
    logger.info('Redis connected successfully.');
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
  }
}; 