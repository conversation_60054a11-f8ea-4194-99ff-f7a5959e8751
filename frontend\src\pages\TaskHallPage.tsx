import React, { useState, useEffect } from 'react';
import { useMutation } from 'react-query';
import {
  Heart,
  Share,
  UserPlus,
  MessageCircle,
  Coins,
  RefreshCw,
  Zap,
  Clapperboard,
  BookOpen,
  Tv,
  Loader2,
} from 'lucide-react';
import { Platform, Task, TaskType, TaskRequirement } from '@/types';
import { TaskExecutionSession, ExecutionResult, taskExecutionService } from '@/services/taskExecutionService';
import TaskExecutionDialog from '@/components/TaskExecutionDialog';
import TaskGuidanceOverlay from '@/components/TaskGuidanceOverlay';
import GuidanceNotification from '@/components/GuidanceNotification';
import LoadingSpinner from '@/components/LoadingSpinner';
import { TaskService } from '@/services/taskService';
import { toast } from 'react-hot-toast';
import { getVideoInfo, VideoInfo } from '@/services/videoService';
import { useAuthStore } from '@/store/authStore';

const platformOptions: {
  value: Platform;
  label: string;
  icon: React.ElementType;
  color: string;
  hoverColor: string;
}[] = [
  { value: 'douyin', label: '抖音', icon: Clapperboard, color: 'text-cyan-500', hoverColor: 'hover:bg-cyan-50' },
  { value: 'kuaishou', label: '快手', icon: Zap, color: 'text-orange-500', hoverColor: 'hover:bg-orange-50' },
  { value: 'xiaohongshu', label: '小红书', icon: BookOpen, color: 'text-red-500', hoverColor: 'hover:bg-red-50' },
  { value: 'bilibili', label: 'B站', icon: Tv, color: 'text-blue-500', hoverColor: 'hover:bg-blue-50' },
  ];

  const taskTypeOptions = [
    { value: 'like', label: '点赞', icon: Heart },
    { value: 'share', label: '分享', icon: Share },
    { value: 'follow', label: '关注', icon: UserPlus },
    { value: 'comment', label: '评论', icon: MessageCircle },
  ];

  const getTaskTypeIcon = (type: TaskType) => {
    const option = taskTypeOptions.find(opt => opt.value === type);
    return option ? option.icon : Heart;
  };

  const getPlatformInfo = (platform: Platform) => {
    return platformOptions.find(opt => opt.value === platform);
  };

  // Helper function to get task requirements (supports both old and new format)
  const getTaskRequirements = (task: Task): TaskRequirement[] => {
    if (task.requirements && task.requirements.length > 0) {
      return task.requirements;
    }
    // Fallback for legacy tasks
    if (task.task_type && task.reward_points && task.total_quota) {
      return [{
        type: task.task_type,
        quota: task.total_quota,
        completed_count: task.completed_count || 0,
        reward_points: task.reward_points
      }];
    }
    return [];
  };

  // Helper function to get total reward points
  const getTotalRewardPoints = (task: Task): number => {
    if (task.total_reward_points) {
      return task.total_reward_points;
    }
    // Fallback for legacy tasks
    return task.reward_points || 0;
  };




const TaskHallPage: React.FC = () => {
  const { user } = useAuthStore();
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(null);
  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null);
  const [isLoadingVideoInfo, setIsLoadingVideoInfo] = useState(false);
  const [selectedRequirements, setSelectedRequirements] = useState<TaskType[]>([]);
  const [showRequirementSelection, setShowRequirementSelection] = useState(false);
  const [executionSession, setExecutionSession] = useState<TaskExecutionSession | null>(null);
  const [executionResult, setExecutionResult] = useState<ExecutionResult | null>(null);
  const [showExecutionDialog, setShowExecutionDialog] = useState(false);
  const [showGuidanceOverlay, setShowGuidanceOverlay] = useState(false);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);

  // 监听任务执行完成事件
  useEffect(() => {
    const handleExecutionComplete = (event: CustomEvent) => {
      const { session, result } = event.detail;
      setExecutionSession(session);
      setExecutionResult(result);
      setShowExecutionDialog(true);
      setShowGuidanceOverlay(false); // 隐藏引导界面
      setActiveSessionId(null);
    };

    window.addEventListener('taskExecutionComplete', handleExecutionComplete as EventListener);

    return () => {
      window.removeEventListener('taskExecutionComplete', handleExecutionComplete as EventListener);
    };
  }, []);

  const { mutate: fetchRandomTask, isLoading, error } = useMutation(
    (platform: Platform) => TaskService.getRandomTask(platform),
    {
      onSuccess: (data) => {
        setCurrentTask(data);
        setSelectedRequirements([]);
        setShowRequirementSelection(false);
        // 获取视频信息
        if (data && data.video_url) {
          fetchVideoInfo(data.video_url);
        }
      },
      onError: (error: any) => {
        console.error('获取随机任务失败:', error);
        setCurrentTask(null); // Clear task on error
        setVideoInfo(null);
      },
    }
  );

  const { mutate: completeTaskMutation, isLoading: isCompleting } = useMutation(
    (taskId: number) => TaskService.completeTask(taskId),
    {
      onSuccess: (data) => {
        toast.success(`任务完成！获得 ${data.task.reward_points} 积分`);
        // 可以在这里更新用户积分显示，或者刷新下一个任务
        handleNextTask();
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.message || '任务完成失败');
      }
    }
  );

  const handlePlatformSelect = (platform: Platform) => {
    setSelectedPlatform(platform);
    fetchRandomTask(platform);
  };

  const fetchVideoInfo = async (videoUrl: string) => {
    try {
      setIsLoadingVideoInfo(true);
      const info = await getVideoInfo(videoUrl);
      setVideoInfo(info);
    } catch (error) {
      console.error('获取视频信息失败:', error);
      setVideoInfo(null);
    } finally {
      setIsLoadingVideoInfo(false);
    }
  };

  const handleNextTask = () => {
    if (selectedPlatform) {
      setVideoInfo(null); // Clear previous video info
      fetchRandomTask(selectedPlatform);
    }
  };

  const handleExecuteTask = async (task: Task) => {
    if (selectedRequirements.length === 0) {
      toast.error("请先选择要完成的任务类型");
      return;
    }

    if (!task.video_url) {
      toast.error("任务链接不存在");
      return;
    }

    try {
      // 使用新的任务执行服务，传递用户信誉度
      const session = await taskExecutionService.startExecution(
        task.id,
        task.video_url,
        selectedRequirements,
        user?.reputation_score
      );

      // 显示引导界面
      setShowGuidanceOverlay(true);
      setActiveSessionId(session.id);

      toast.success("任务窗口已打开，请完成相应操作后关闭窗口");
      console.log("任务执行会话已开始:", session.id);
    } catch (error: any) {
      toast.error(error.message || "启动任务执行失败");
    }
  };

  const renderPlatformSelector = () => (
    <div className="text-center">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">请选择一个平台开始任务</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
        {platformOptions.map(p => {
          const Icon = p.icon;
    return (
            <button
              key={p.value}
              onClick={() => handlePlatformSelect(p.value)}
              className={`flex flex-col items-center justify-center p-6 border-2 border-gray-200 rounded-xl shadow-sm transition-all duration-200 ease-in-out transform hover:-translate-y-1 hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-opacity-50 ${p.hoverColor} ${p.color} focus:ring-current`}
            >
              <Icon className="w-12 h-12 mb-3" />
              <span className="text-lg font-semibold text-gray-700">{p.label}</span>
            </button>
          );
        })}
      </div>
    </div>
  );

  const renderTaskViewer = () => {
    if (isLoading) {
      return <LoadingSpinner size="lg" text="正在获取任务..." />;
    }

    if (error) {
        const errorMessage = (error as any)?.response?.data?.message || '加载任务失败，请稍后再试。';
        return (
            <div className="text-center text-red-500">
                <p>{errorMessage}</p>
                <button onClick={handleNextTask} className="mt-4 px-4 py-2 border rounded-md">重试</button>
      </div>
    );
  }

    if (!currentTask) {
  return (
        <div className="text-center text-gray-600">
          <p>该平台暂时没有可用的任务了。</p>
           <button onClick={() => setSelectedPlatform(null)} className="mt-4 px-4 py-2 border rounded-md">返回选择平台</button>
        </div>
      );
    }

    const requirements = getTaskRequirements(currentTask);
    const totalRewardPoints = getTotalRewardPoints(currentTask);
    const platformInfo = getPlatformInfo(currentTask.platform);
    const PlatformIcon = platformInfo?.icon;

    return (
      <div className="w-full max-w-4xl mx-auto animate-fade-in bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">{currentTask.title}</h2>
            <div className="flex items-center text-yellow-600 font-semibold text-lg">
                <Coins className="w-6 h-6 mr-1" />
                最高 {totalRewardPoints} 积分
        </div>
      </div>
          <div className="flex items-center flex-wrap gap-2 text-sm text-gray-500 mb-4">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${platformInfo?.color}`}>
              {PlatformIcon && <PlatformIcon className="w-4 h-4 mr-1" />} {platformInfo?.label}
            </span>
            <span className="text-xs text-gray-600">需要:</span>
            {requirements.map((req, index) => {
              const TaskTypeIcon = getTaskTypeIcon(req.type);
              const taskTypeLabel = taskTypeOptions.find(opt => opt.value === req.type)?.label;
              return (
                <span key={req.type} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  <TaskTypeIcon className="w-3 h-3 mr-1" />
                  {taskTypeLabel}
                </span>
              );
            })}
          </div>

          {/* 视频信息展示 */}
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
            {isLoadingVideoInfo ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin text-gray-400 mr-2" />
                <span className="text-gray-500">正在获取视频信息...</span>
              </div>
            ) : videoInfo ? (
              <div className="flex space-x-4">
                <div className="flex-shrink-0">
                  <img
                    src={videoInfo.cover}
                    alt={videoInfo.title}
                    className="w-32 h-24 object-cover rounded-lg border"
                    onError={(e) => {
                      const img = e.target as HTMLImageElement;
                      if (!img.src.includes('placeholder-cover.svg')) {
                        console.warn('封面图片加载失败，使用占位符:', videoInfo.cover);
                        img.src = '/placeholder-cover.svg';
                        img.alt = '封面加载失败';
                      }
                    }}
                  />
                </div>
                <div className="flex-grow min-w-0">
                  <h3 className="font-semibold text-gray-800 text-lg mb-2 overflow-hidden" style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical'
                  }}>
                    {videoInfo.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-1">作者: {videoInfo.author}</p>
                  {videoInfo.duration && (
                    <p className="text-sm text-gray-600 mb-2">
                      时长: {Math.floor(videoInfo.duration / 60)}:{(videoInfo.duration % 60).toString().padStart(2, '0')}
                    </p>
                  )}
                  {videoInfo.description && (
                    <p className="text-sm text-gray-500 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {videoInfo.description}
                    </p>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <p>无法获取视频详细信息</p>
              </div>
            )}
          </div>
        </div>

        <div className="px-6 py-4 space-y-4 border-t border-gray-200">
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 className="font-semibold text-gray-800 mb-3">选择要完成的任务类型</h4>
            <p className="text-gray-600 text-sm mb-4">您可以选择完成以下一种或多种任务类型，每完成一种可获得相应积分奖励</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {requirements.map((req) => {
                const TaskTypeIcon = getTaskTypeIcon(req.type);
                const taskTypeLabel = taskTypeOptions.find(opt => opt.value === req.type)?.label;
                const isSelected = selectedRequirements.includes(req.type);

                return (
                  <div
                    key={req.type}
                    onClick={() => {
                      if (isSelected) {
                        setSelectedRequirements(prev => prev.filter(t => t !== req.type));
                      } else {
                        setSelectedRequirements(prev => [...prev, req.type]);
                      }
                    }}
                    className={`flex items-center justify-between p-3 rounded-lg border-2 cursor-pointer transition-all ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center">
                      <TaskTypeIcon className={`w-6 h-6 mr-3 ${isSelected ? 'text-blue-600' : 'text-gray-600'}`} />
                      <div>
                        <p className={`font-medium ${isSelected ? 'text-blue-900' : 'text-gray-800'}`}>
                          {taskTypeLabel}
                        </p>
                        <p className={`text-sm ${isSelected ? 'text-blue-700' : 'text-gray-500'}`}>
                          +{req.reward_points} 积分
                        </p>
                      </div>
                    </div>
                    <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                      isSelected
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300'
                    }`}>
                      {isSelected && <div className="w-2 h-2 bg-white rounded-full" />}
                    </div>
                  </div>
                );
              })}
            </div>

            {selectedRequirements.length > 0 && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800">
                  已选择 {selectedRequirements.length} 种任务类型，预计获得 {' '}
                  {requirements
                    .filter(req => selectedRequirements.includes(req.type))
                    .reduce((sum, req) => sum + req.reward_points, 0)
                  } 积分
                </p>
              </div>
            )}
          </div>
        </div>
        <div className="px-6 py-4 bg-gray-50 flex justify-between items-center">
            <button onClick={() => setSelectedPlatform(null)} className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100">
                返回选择平台
            </button>
          <div className="flex gap-4">
            <button onClick={handleNextTask} disabled={isCompleting} className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 flex items-center disabled:opacity-50">
              <RefreshCw className="w-4 h-4 mr-2" />
              下一个任务
            </button>
                <button
              onClick={() => handleExecuteTask(currentTask)}
              disabled={isCompleting || selectedRequirements.length === 0}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 flex items-center disabled:opacity-50 disabled:bg-blue-400"
                >
              {isCompleting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  {selectedRequirements.length > 0 ? '立即执行' : '请选择任务类型'}
                </>
              )}
                </button>
              </div>
            </div>
      </div>
    );
  };

  return (
    <div className="space-y-6 p-4 sm:p-6 lg:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">任务大厅</h1>
          <p className="text-gray-600 mt-1">
            {selectedPlatform 
                ? `正在执行 ${getPlatformInfo(selectedPlatform)?.label} 平台任务`
                : '选择感兴趣的平台，赚取积分奖励'}
          </p>
        </div>
        {selectedPlatform && (
            <button onClick={() => setSelectedPlatform(null)} className="text-blue-600 hover:underline">切换平台</button>
      )}
      </div>

      <div className="mt-8 flex items-center justify-center">
        {!selectedPlatform ? renderPlatformSelector() : renderTaskViewer()}
      </div>

      {/* 任务执行完成对话框 */}
      {showExecutionDialog && (
        <TaskExecutionDialog
          session={executionSession}
          result={executionResult}
          onClose={() => {
            setShowExecutionDialog(false);
            setExecutionSession(null);
            setExecutionResult(null);
            // 刷新任务列表
            if (selectedPlatform) {
              handleNextTask();
            }
          }}
        />
      )}

      {/* 任务引导界面 */}
      {showGuidanceOverlay && activeSessionId && currentTask && (
        <TaskGuidanceOverlay
          isVisible={showGuidanceOverlay}
          selectedRequirements={selectedRequirements}
          platform={currentTask.platform}
          sessionId={activeSessionId}
          onClose={() => setShowGuidanceOverlay(false)}
        />
      )}

      {/* 引导通知 */}
      <GuidanceNotification />
    </div>
  );
};

export default TaskHallPage;
