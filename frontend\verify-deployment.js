/**
 * 扩展式检测器部署验证脚本
 * 验证所有组件是否正确部署和工作
 */

console.log('🔍 开始验证扩展式检测器部署...');

// 验证结果
const verificationResults = {
    components: {},
    features: {},
    performance: {},
    issues: [],
    recommendations: []
};

// 1. 验证组件文件存在
async function verifyComponents() {
    console.log('🔧 验证组件文件...');
    
    const components = [
        'src/services/browserExtensionDetector.ts',
        'src/services/realCrossDomainDetector.ts',
        'src/services/taskExecutionService.ts',
        'test-extension-detector.html',
        'test-live-detection.html'
    ];
    
    for (const component of components) {
        try {
            const response = await fetch(`/${component}`);
            verificationResults.components[component] = response.ok;
            console.log(`${response.ok ? '✅' : '❌'} ${component}`);
        } catch (error) {
            verificationResults.components[component] = false;
            verificationResults.issues.push(`组件文件不存在: ${component}`);
            console.log(`❌ ${component} - ${error.message}`);
        }
    }
}

// 2. 验证模块导入
async function verifyModuleImports() {
    console.log('📦 验证模块导入...');
    
    try {
        // 尝试动态导入检测器模块
        const browserExtensionModule = await import('./src/services/browserExtensionDetector.js');
        verificationResults.features.browserExtensionDetector = !!browserExtensionModule.browserExtensionDetector;
        console.log('✅ 浏览器扩展检测器模块导入成功');
    } catch (error) {
        verificationResults.features.browserExtensionDetector = false;
        verificationResults.issues.push('浏览器扩展检测器模块导入失败');
        console.log('❌ 浏览器扩展检测器模块导入失败:', error.message);
    }
    
    try {
        const realDetectorModule = await import('./src/services/realCrossDomainDetector.js');
        verificationResults.features.realCrossDomainDetector = !!realDetectorModule.realCrossDomainDetector;
        console.log('✅ 真实跨域检测器模块导入成功');
    } catch (error) {
        verificationResults.features.realCrossDomainDetector = false;
        verificationResults.issues.push('真实跨域检测器模块导入失败');
        console.log('❌ 真实跨域检测器模块导入失败:', error.message);
    }
    
    try {
        const taskExecutionModule = await import('./src/services/taskExecutionService.js');
        verificationResults.features.taskExecutionService = !!taskExecutionModule.taskExecutionService;
        console.log('✅ 任务执行服务模块导入成功');
    } catch (error) {
        verificationResults.features.taskExecutionService = false;
        verificationResults.issues.push('任务执行服务模块导入失败');
        console.log('❌ 任务执行服务模块导入失败:', error.message);
    }
}

// 3. 验证API功能
async function verifyAPIFunctionality() {
    console.log('🌐 验证API功能...');
    
    try {
        // 测试后端API连接
        const healthResponse = await fetch('http://localhost:3000/health');
        verificationResults.features.backendAPI = healthResponse.ok;
        console.log(`${healthResponse.ok ? '✅' : '❌'} 后端API连接`);
    } catch (error) {
        verificationResults.features.backendAPI = false;
        verificationResults.issues.push('后端API连接失败');
        console.log('❌ 后端API连接失败:', error.message);
    }
    
    try {
        // 测试任务API
        const tasksResponse = await fetch('http://localhost:3000/api/tasks/random?platform=bilibili');
        verificationResults.features.tasksAPI = tasksResponse.ok;
        console.log(`${tasksResponse.ok ? '✅' : '❌'} 任务API`);
    } catch (error) {
        verificationResults.features.tasksAPI = false;
        verificationResults.issues.push('任务API连接失败');
        console.log('❌ 任务API连接失败:', error.message);
    }
}

// 4. 验证检测器功能
async function verifyDetectorFunctionality() {
    console.log('🎯 验证检测器功能...');
    
    // 模拟检测器测试
    const testResults = {
        extensionDetection: false,
        realDetection: false,
        hybridDetection: false
    };
    
    try {
        // 模拟扩展式检测测试
        console.log('🔌 测试扩展式检测...');
        
        // 创建测试窗口
        const testWindow = window.open('about:blank', '_blank', 'width=100,height=100');
        if (testWindow) {
            testResults.extensionDetection = true;
            testWindow.close();
            console.log('✅ 扩展式检测基础功能正常');
        }
    } catch (error) {
        verificationResults.issues.push('扩展式检测测试失败');
        console.log('❌ 扩展式检测测试失败:', error.message);
    }
    
    try {
        // 模拟真实检测测试
        console.log('🎯 测试真实检测...');
        
        // 测试窗口监控功能
        const startTime = Date.now();
        setTimeout(() => {
            const duration = Date.now() - startTime;
            if (duration >= 100) {
                testResults.realDetection = true;
                console.log('✅ 真实检测基础功能正常');
            }
        }, 100);
    } catch (error) {
        verificationResults.issues.push('真实检测测试失败');
        console.log('❌ 真实检测测试失败:', error.message);
    }
    
    // 混合检测
    if (testResults.extensionDetection && testResults.realDetection) {
        testResults.hybridDetection = true;
        console.log('✅ 混合检测功能可用');
    }
    
    Object.assign(verificationResults.features, testResults);
}

// 5. 性能测试
async function verifyPerformance() {
    console.log('📊 验证性能指标...');
    
    const performanceTests = {
        responseTime: 0,
        memoryUsage: 0,
        detectionAccuracy: 0
    };
    
    // 测试响应时间
    const startTime = performance.now();
    try {
        await fetch('http://localhost:3000/health');
        performanceTests.responseTime = performance.now() - startTime;
        console.log(`⚡ API响应时间: ${performanceTests.responseTime.toFixed(2)}ms`);
    } catch (error) {
        console.log('❌ 响应时间测试失败');
    }
    
    // 测试内存使用
    if (performance.memory) {
        performanceTests.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
        console.log(`💾 内存使用: ${performanceTests.memoryUsage.toFixed(2)}MB`);
    }
    
    // 估算检测准确率
    const componentCount = Object.values(verificationResults.components).filter(Boolean).length;
    const featureCount = Object.values(verificationResults.features).filter(Boolean).length;
    performanceTests.detectionAccuracy = Math.min((componentCount + featureCount) * 10, 95);
    console.log(`🎯 估算检测准确率: ${performanceTests.detectionAccuracy}%`);
    
    verificationResults.performance = performanceTests;
}

// 6. 生成建议
function generateRecommendations() {
    console.log('💡 生成部署建议...');
    
    const { components, features, performance, issues } = verificationResults;
    
    // 检查组件完整性
    const missingComponents = Object.entries(components)
        .filter(([_, exists]) => !exists)
        .map(([component, _]) => component);
    
    if (missingComponents.length > 0) {
        verificationResults.recommendations.push(`缺少组件文件: ${missingComponents.join(', ')}`);
    }
    
    // 检查功能完整性
    const missingFeatures = Object.entries(features)
        .filter(([_, works]) => !works)
        .map(([feature, _]) => feature);
    
    if (missingFeatures.length > 0) {
        verificationResults.recommendations.push(`功能异常: ${missingFeatures.join(', ')}`);
    }
    
    // 性能建议
    if (performance.responseTime > 500) {
        verificationResults.recommendations.push('API响应时间较长，建议优化');
    }
    
    if (performance.memoryUsage > 100) {
        verificationResults.recommendations.push('内存使用较高，建议优化');
    }
    
    if (performance.detectionAccuracy < 80) {
        verificationResults.recommendations.push('检测准确率偏低，建议检查配置');
    }
    
    // 如果没有问题
    if (issues.length === 0 && verificationResults.recommendations.length === 0) {
        verificationResults.recommendations.push('部署状态良好，可以进行生产测试');
    }
}

// 7. 生成报告
function generateReport() {
    console.log('\n📋 生成部署验证报告...');
    console.log('='.repeat(60));
    console.log('🔌 扩展式检测器部署验证报告');
    console.log('='.repeat(60));
    
    // 组件状态
    console.log('\n🔧 组件状态:');
    Object.entries(verificationResults.components).forEach(([component, exists]) => {
        console.log(`  ${exists ? '✅' : '❌'} ${component}`);
    });
    
    // 功能状态
    console.log('\n⚙️ 功能状态:');
    Object.entries(verificationResults.features).forEach(([feature, works]) => {
        console.log(`  ${works ? '✅' : '❌'} ${feature}`);
    });
    
    // 性能指标
    console.log('\n📊 性能指标:');
    console.log(`  ⚡ 响应时间: ${verificationResults.performance.responseTime?.toFixed(2) || 'N/A'}ms`);
    console.log(`  💾 内存使用: ${verificationResults.performance.memoryUsage?.toFixed(2) || 'N/A'}MB`);
    console.log(`  🎯 检测准确率: ${verificationResults.performance.detectionAccuracy || 'N/A'}%`);
    
    // 问题列表
    console.log('\n🚨 问题列表:');
    if (verificationResults.issues.length === 0) {
        console.log('  ✅ 无问题');
    } else {
        verificationResults.issues.forEach(issue => {
            console.log(`  ❌ ${issue}`);
        });
    }
    
    // 建议
    console.log('\n💡 建议:');
    verificationResults.recommendations.forEach(rec => {
        console.log(`  💡 ${rec}`);
    });
    
    // 总体状态
    const overallScore = calculateOverallScore();
    console.log('\n🎯 总体评分:');
    console.log(`  📊 ${overallScore.toFixed(1)}% (${getScoreLevel(overallScore)})`);
    
    console.log('\n='.repeat(60));
    console.log(`📅 报告生成时间: ${new Date().toLocaleString()}`);
    console.log('='.repeat(60));
}

// 计算总体评分
function calculateOverallScore() {
    const componentScore = Object.values(verificationResults.components).filter(Boolean).length / 
                          Object.keys(verificationResults.components).length;
    
    const featureScore = Object.values(verificationResults.features).filter(Boolean).length / 
                        Object.keys(verificationResults.features).length;
    
    const performanceScore = Math.min(
        (verificationResults.performance.detectionAccuracy || 0) / 100,
        1
    );
    
    return (componentScore + featureScore + performanceScore) / 3 * 100;
}

// 获取评分等级
function getScoreLevel(score) {
    if (score >= 90) return '优秀';
    if (score >= 80) return '良好';
    if (score >= 70) return '一般';
    if (score >= 60) return '及格';
    return '需要改进';
}

// 主验证流程
async function runVerification() {
    try {
        await verifyComponents();
        await verifyModuleImports();
        await verifyAPIFunctionality();
        await verifyDetectorFunctionality();
        await verifyPerformance();
        generateRecommendations();
        generateReport();
        
        console.log('\n🎉 部署验证完成！');
        
        // 返回验证结果
        return verificationResults;
    } catch (error) {
        console.error('❌ 验证过程中发生错误:', error);
        return null;
    }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    // 暴露到全局作用域
    window.verifyDeployment = runVerification;
    window.verificationResults = verificationResults;
    
    // 自动运行验证
    document.addEventListener('DOMContentLoaded', () => {
        console.log('🚀 自动启动部署验证...');
        runVerification();
    });
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runVerification,
        verificationResults
    };
}

console.log('✅ 部署验证脚本已加载');
