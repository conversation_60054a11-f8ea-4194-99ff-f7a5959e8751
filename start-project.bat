@echo off
echo 🚀 启动互助点赞平台项目...
echo.

cd /d "C:\Users\<USER>\Desktop\social-media-automation"

echo 📍 当前目录: %CD%
echo.

echo 🔍 检查Node.js版本...
node -v
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或不在PATH中
    echo 请安装Node.js 18+版本
    pause
    exit /b 1
)

echo 🔍 检查npm版本...
npm -v
if %errorlevel% neq 0 (
    echo ❌ npm 未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo ✅ 环境检查通过
echo.

echo 🚀 启动开发服务器...
npm run dev

pause
