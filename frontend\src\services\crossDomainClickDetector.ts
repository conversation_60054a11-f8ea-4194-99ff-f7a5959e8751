/**
 * 跨域点击检测服务
 * 专门用于检测跨浏览器窗口中的用户点击行为
 */

export interface ClickDetectionResult {
  totalClicks: number;
  likeClicks: number;
  shareClicks: number;
  followClicks: number;
  commentClicks: number;
  clickTimestamps: number[];
  clickPositions: Array<{ x: number; y: number; timestamp: number }>;
  detectedActions: Array<{
    action: string;
    confidence: number;
    timestamp: number;
    evidence: string[];
  }>;
}

export interface WindowActivityMonitor {
  urlChanges: number;
  focusEvents: number;
  windowActiveTime: number;
  lastActivityTime: number;
  clickIndicators: number;
}

class CrossDomainClickDetector {
  private detectionResults = new Map<string, ClickDetectionResult>();
  private windowMonitors = new Map<string, WindowActivityMonitor>();
  private messageHandlers = new Set<(event: MessageEvent) => void>();

  constructor() {
    this.setupGlobalMessageListener();
  }

  /**
   * 开始监控指定窗口的点击行为
   */
  startClickDetection(
    sessionId: string,
    targetWindow: Window,
    platform: string,
    taskTypes: string[]
  ): void {
    console.log(`🎯 开始点击检测 - 会话: ${sessionId}, 平台: ${platform}`);

    // 初始化检测结果
    this.detectionResults.set(sessionId, {
      totalClicks: 0,
      likeClicks: 0,
      shareClicks: 0,
      followClicks: 0,
      commentClicks: 0,
      clickTimestamps: [],
      clickPositions: [],
      detectedActions: []
    });

    // 初始化窗口监控
    this.windowMonitors.set(sessionId, {
      urlChanges: 0,
      focusEvents: 0,
      windowActiveTime: 0,
      lastActivityTime: Date.now(),
      clickIndicators: 0
    });

    // 尝试注入点击检测脚本
    this.injectClickDetectionScript(targetWindow, platform, sessionId, taskTypes);

    // 启动窗口状态监控
    this.startWindowMonitoring(sessionId, targetWindow);

    // 启动智能推断检测
    this.startIntelligentClickDetection(sessionId, platform, taskTypes);
  }

  /**
   * 注入点击检测脚本到目标窗口
   */
  private injectClickDetectionScript(
    targetWindow: Window,
    platform: string,
    sessionId: string,
    taskTypes: string[]
  ): void {
    try {
      // 检查是否可以访问目标窗口
      const testAccess = targetWindow.document;
      
      // 同域场景：直接注入脚本
      this.injectDirectClickScript(targetWindow, platform, sessionId, taskTypes);
      console.log(`✅ 成功注入同域点击检测脚本`);
    } catch (error) {
      // 跨域场景：使用替代方案
      console.log(`🌐 跨域场景，使用替代点击检测方案`);
      this.setupCrossDomainClickDetection(sessionId, targetWindow, platform, taskTypes);
    }
  }

  /**
   * 直接注入点击检测脚本（同域场景）
   */
  private injectDirectClickScript(
    targetWindow: Window,
    platform: string,
    sessionId: string,
    taskTypes: string[]
  ): void {
    const script = this.generateClickDetectionScript(platform, sessionId, taskTypes);
    
    const injectScript = () => {
      const scriptElement = targetWindow.document.createElement('script');
      scriptElement.textContent = script;
      targetWindow.document.head.appendChild(scriptElement);
    };

    if (targetWindow.document.readyState === 'complete') {
      injectScript();
    } else {
      targetWindow.addEventListener('load', injectScript);
    }
  }

  /**
   * 生成平台特定的点击检测脚本
   */
  private generateClickDetectionScript(
    platform: string,
    sessionId: string,
    taskTypes: string[]
  ): string {
    return `
      (function() {
        const clickDetector = {
          sessionId: '${sessionId}',
          platform: '${platform}',
          taskTypes: ${JSON.stringify(taskTypes)},
          clickCount: 0,
          clickPositions: [],
          detectedActions: [],
          
          init() {
            this.setupClickListeners();
            this.setupMutationObserver();
            this.setupPeriodicCheck();
            console.log('🎯 点击检测器已初始化');
          },
          
          setupClickListeners() {
            // 全局点击监听
            document.addEventListener('click', (event) => {
              this.handleClick(event);
            }, true);
            
            // 特定按钮点击监听
            document.addEventListener('click', (event) => {
              this.checkSpecificButtons(event);
            }, false);
          },
          
          handleClick(event) {
            this.clickCount++;
            
            const clickData = {
              x: event.clientX,
              y: event.clientY,
              timestamp: Date.now(),
              target: this.getElementInfo(event.target)
            };
            
            this.clickPositions.push(clickData);
            
            // 报告点击事件
            this.reportClick(clickData);
            
            // 检查是否为任务相关按钮
            this.analyzeClickTarget(event.target, clickData);
          },
          
          checkSpecificButtons(event) {
            const target = event.target;
            
            // 检查点赞按钮
            if (this.isLikeButton(target)) {
              this.handleLikeClick(target);
            }
            
            // 检查分享按钮
            if (this.isShareButton(target)) {
              this.handleShareClick(target);
            }
            
            // 检查关注按钮
            if (this.isFollowButton(target)) {
              this.handleFollowClick(target);
            }
            
            // 检查评论按钮
            if (this.isCommentButton(target)) {
              this.handleCommentClick(target);
            }
          },
          
          isLikeButton(element) {
            if (!element) return false;
            
            const selectors = this.getPlatformSelectors('${platform}').like;
            return selectors.some(selector => {
              return element.matches(selector) || element.closest(selector);
            });
          },
          
          isShareButton(element) {
            if (!element) return false;
            
            const selectors = this.getPlatformSelectors('${platform}').share;
            return selectors.some(selector => {
              return element.matches(selector) || element.closest(selector);
            });
          },
          
          isFollowButton(element) {
            if (!element) return false;
            
            const selectors = this.getPlatformSelectors('${platform}').follow;
            return selectors.some(selector => {
              return element.matches(selector) || element.closest(selector);
            });
          },
          
          isCommentButton(element) {
            if (!element) return false;
            
            const selectors = this.getPlatformSelectors('${platform}').comment;
            return selectors.some(selector => {
              return element.matches(selector) || element.closest(selector);
            });
          },
          
          getPlatformSelectors(platform) {
            const selectors = {
              bilibili: {
                like: [
                  '.video-like',
                  '.like-btn',
                  '[class*="like"]',
                  '.video-toolbar-left .toolbar-left-item-wrap:first-child',
                  '.video-page-tool-bar .like'
                ],
                share: [
                  '.video-share',
                  '.share-btn',
                  '[class*="share"]',
                  '.video-toolbar-left .toolbar-left-item-wrap:nth-child(2)'
                ],
                follow: [
                  '.follow-btn',
                  '.subscribe-btn',
                  '[class*="follow"]',
                  '.up-info .follow'
                ],
                comment: [
                  '.comment-submit',
                  '.reply-btn',
                  '[class*="comment"]'
                ]
              },
              douyin: {
                like: [
                  '[data-e2e="like-icon"]',
                  '[data-e2e="video-like"]',
                  '.like-container',
                  '.digg-btn'
                ],
                share: [
                  '[data-e2e="share-icon"]',
                  '[data-e2e="video-share"]',
                  '.share-container'
                ],
                follow: [
                  '[data-e2e="follow-icon"]',
                  '.follow-btn',
                  '.subscribe-btn'
                ],
                comment: [
                  '[data-e2e="comment-icon"]',
                  '.comment-btn'
                ]
              }
            };
            
            return selectors[platform] || selectors.bilibili;
          },
          
          handleLikeClick(target) {
            setTimeout(() => {
              const isLiked = this.checkLikeStatus(target);
              this.reportAction('like', {
                confidence: isLiked ? 90 : 70,
                evidence: ['button_click', isLiked ? 'state_changed' : 'click_detected'],
                timestamp: Date.now()
              });
            }, 500);
          },
          
          handleShareClick(target) {
            setTimeout(() => {
              const shareModal = this.checkShareModal();
              this.reportAction('share', {
                confidence: shareModal ? 85 : 60,
                evidence: ['button_click', shareModal ? 'modal_opened' : 'click_detected'],
                timestamp: Date.now()
              });
            }, 500);
          },
          
          handleFollowClick(target) {
            setTimeout(() => {
              const isFollowed = this.checkFollowStatus(target);
              this.reportAction('follow', {
                confidence: isFollowed ? 90 : 70,
                evidence: ['button_click', isFollowed ? 'state_changed' : 'click_detected'],
                timestamp: Date.now()
              });
            }, 500);
          },
          
          handleCommentClick(target) {
            this.reportAction('comment', {
              confidence: 75,
              evidence: ['button_click', 'comment_interaction'],
              timestamp: Date.now()
            });
          },
          
          checkLikeStatus(button) {
            // 检查按钮状态变化
            const classList = button.classList.toString();
            const ariaPressed = button.getAttribute('aria-pressed');
            
            return classList.includes('liked') || 
                   classList.includes('active') || 
                   ariaPressed === 'true';
          },
          
          checkShareModal() {
            // 检查分享弹窗是否出现
            const shareModals = document.querySelectorAll(
              '.share-modal, .share-dialog, .share-panel, [class*="share-popup"]'
            );
            
            return Array.from(shareModals).some(modal => {
              const style = window.getComputedStyle(modal);
              return style.display !== 'none' && style.visibility !== 'hidden';
            });
          },
          
          checkFollowStatus(button) {
            const text = button.textContent || '';
            const classList = button.classList.toString();
            
            return text.includes('已关注') || 
                   text.includes('已订阅') ||
                   classList.includes('followed') ||
                   classList.includes('subscribed');
          },
          
          getElementInfo(element) {
            return {
              tagName: element.tagName,
              className: element.className,
              id: element.id,
              textContent: element.textContent?.substring(0, 50)
            };
          },
          
          analyzeClickTarget(target, clickData) {
            // 分析点击目标，推断用户意图
            const info = this.getElementInfo(target);
            
            if (info.className.includes('like') || info.textContent?.includes('点赞')) {
              clickData.likelyAction = 'like';
            } else if (info.className.includes('share') || info.textContent?.includes('分享')) {
              clickData.likelyAction = 'share';
            } else if (info.className.includes('follow') || info.textContent?.includes('关注')) {
              clickData.likelyAction = 'follow';
            }
          },
          
          reportClick(clickData) {
            window.parent.postMessage({
              type: 'CLICK_DETECTED',
              sessionId: this.sessionId,
              platform: this.platform,
              data: clickData,
              totalClicks: this.clickCount,
              timestamp: Date.now()
            }, '*');
          },
          
          reportAction(action, data) {
            this.detectedActions.push({ action, ...data });
            
            window.parent.postMessage({
              type: 'ACTION_DETECTED',
              sessionId: this.sessionId,
              platform: this.platform,
              action: action,
              data: data,
              timestamp: Date.now()
            }, '*');
          },
          
          setupMutationObserver() {
            const observer = new MutationObserver((mutations) => {
              mutations.forEach((mutation) => {
                if (mutation.type === 'attributes') {
                  this.checkAttributeChanges(mutation);
                }
              });
            });
            
            observer.observe(document.body, {
              attributes: true,
              subtree: true,
              attributeFilter: ['class', 'aria-pressed', 'data-liked']
            });
          },
          
          checkAttributeChanges(mutation) {
            const target = mutation.target;
            const attributeName = mutation.attributeName;
            
            if (attributeName === 'class' || attributeName === 'aria-pressed') {
              if (this.isLikeButton(target)) {
                this.handleLikeStateChange(target);
              } else if (this.isFollowButton(target)) {
                this.handleFollowStateChange(target);
              }
            }
          },
          
          handleLikeStateChange(target) {
            this.reportAction('like', {
              confidence: 95,
              evidence: ['state_change', 'attribute_mutation'],
              timestamp: Date.now()
            });
          },
          
          handleFollowStateChange(target) {
            this.reportAction('follow', {
              confidence: 95,
              evidence: ['state_change', 'attribute_mutation'],
              timestamp: Date.now()
            });
          },
          
          setupPeriodicCheck() {
            setInterval(() => {
              this.reportStatus();
            }, 3000);
          },
          
          reportStatus() {
            window.parent.postMessage({
              type: 'CLICK_STATUS_UPDATE',
              sessionId: this.sessionId,
              platform: this.platform,
              status: {
                totalClicks: this.clickCount,
                detectedActions: this.detectedActions,
                clickPositions: this.clickPositions.slice(-10) // 只发送最近10次点击
              },
              timestamp: Date.now()
            }, '*');
          }
        };
        
        // 等待页面加载完成后初始化
        if (document.readyState === 'complete') {
          clickDetector.init();
        } else {
          window.addEventListener('load', () => clickDetector.init());
        }
        
        // 暴露到全局作用域
        window.clickDetector = clickDetector;
      })();
    `;
  }

  /**
   * 设置跨域点击检测
   */
  private setupCrossDomainClickDetection(
    sessionId: string,
    targetWindow: Window,
    platform: string,
    taskTypes: string[]
  ): void {
    console.log(`🌐 设置跨域点击检测 - 会话: ${sessionId}`);

    // 启动窗口活动监控
    this.startAdvancedWindowMonitoring(sessionId, targetWindow);

    // 启动智能点击推断
    this.startClickInference(sessionId, platform, taskTypes);
  }

  /**
   * 启动窗口状态监控
   */
  private startWindowMonitoring(sessionId: string, targetWindow: Window): void {
    const monitor = this.windowMonitors.get(sessionId);
    if (!monitor) return;

    const checkInterval = setInterval(() => {
      if (targetWindow.closed) {
        clearInterval(checkInterval);
        return;
      }

      try {
        // 尝试检测URL变化
        const currentUrl = targetWindow.location.href;
        // URL变化可能表明用户进行了操作
        monitor.urlChanges++;
        monitor.clickIndicators++;
      } catch (e) {
        // 跨域场景，无法访问URL
      }

      // 检测窗口焦点
      try {
        if (targetWindow.document.hasFocus()) {
          monitor.windowActiveTime += 1000;
          monitor.lastActivityTime = Date.now();
        }
      } catch (e) {
        // 跨域场景，假设窗口活跃
        monitor.windowActiveTime += 1000;
      }
    }, 1000);
  }

  /**
   * 启动高级窗口监控
   */
  private startAdvancedWindowMonitoring(sessionId: string, targetWindow: Window): void {
    const monitor = this.windowMonitors.get(sessionId);
    if (!monitor) return;

    // 监听窗口焦点事件
    const handleFocus = () => {
      monitor.focusEvents++;
      monitor.clickIndicators++;
      monitor.lastActivityTime = Date.now();
    };

    const handleBlur = () => {
      monitor.focusEvents++;
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    // 清理监听器
    setTimeout(() => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    }, 300000); // 5分钟后清理
  }

  /**
   * 启动智能点击检测
   */
  private startIntelligentClickDetection(
    sessionId: string,
    platform: string,
    taskTypes: string[]
  ): void {
    const checkInterval = setInterval(() => {
      const result = this.detectionResults.get(sessionId);
      const monitor = this.windowMonitors.get(sessionId);
      
      if (!result || !monitor) {
        clearInterval(checkInterval);
        return;
      }

      // 基于窗口活动推断点击行为
      this.inferClickBehavior(sessionId, platform, taskTypes);
    }, 2000);
  }

  /**
   * 启动点击推断
   */
  private startClickInference(sessionId: string, platform: string, taskTypes: string[]): void {
    const inferenceInterval = setInterval(() => {
      this.performClickInference(sessionId, platform, taskTypes);
    }, 3000);

    // 5分钟后停止推断
    setTimeout(() => {
      clearInterval(inferenceInterval);
    }, 300000);
  }

  /**
   * 推断点击行为
   */
  private inferClickBehavior(sessionId: string, platform: string, taskTypes: string[]): void {
    const monitor = this.windowMonitors.get(sessionId);
    const result = this.detectionResults.get(sessionId);
    
    if (!monitor || !result) return;

    // 基于活动指标推断点击次数
    const estimatedClicks = Math.floor(monitor.clickIndicators / 2);
    
    if (estimatedClicks > result.totalClicks) {
      result.totalClicks = estimatedClicks;
      
      // 推断具体操作类型
      taskTypes.forEach(taskType => {
        if (this.shouldInferAction(taskType, monitor, platform)) {
          this.addInferredAction(result, taskType, monitor);
        }
      });
    }
  }

  /**
   * 执行点击推断
   */
  private performClickInference(sessionId: string, platform: string, taskTypes: string[]): void {
    const monitor = this.windowMonitors.get(sessionId);
    const result = this.detectionResults.get(sessionId);
    
    if (!monitor || !result) return;

    const activityScore = this.calculateActivityScore(monitor);
    
    if (activityScore > 50) {
      // 高活动分数，推断用户进行了操作
      taskTypes.forEach(taskType => {
        const confidence = this.calculateActionConfidence(taskType, monitor, activityScore);
        
        if (confidence > 60) {
          result.detectedActions.push({
            action: taskType,
            confidence,
            timestamp: Date.now(),
            evidence: ['window_activity', 'intelligent_inference']
          });
        }
      });
    }
  }

  /**
   * 计算活动分数
   */
  private calculateActivityScore(monitor: WindowActivityMonitor): number {
    let score = 0;
    
    // 基于窗口活跃时间
    if (monitor.windowActiveTime > 5000) score += 20;
    if (monitor.windowActiveTime > 10000) score += 20;
    
    // 基于焦点事件
    score += Math.min(monitor.focusEvents * 10, 30);
    
    // 基于点击指标
    score += Math.min(monitor.clickIndicators * 15, 40);
    
    return Math.min(score, 100);
  }

  /**
   * 计算操作置信度
   */
  private calculateActionConfidence(
    taskType: string,
    monitor: WindowActivityMonitor,
    activityScore: number
  ): number {
    let confidence = activityScore * 0.6;
    
    // 任务类型特定加分
    const taskBonus = {
      like: 15,
      share: 10,
      follow: 12,
      comment: 8
    };
    
    confidence += taskBonus[taskType as keyof typeof taskBonus] || 0;
    
    // 活动时间加分
    if (monitor.windowActiveTime > 8000) confidence += 10;
    
    return Math.min(confidence, 95);
  }

  /**
   * 判断是否应该推断某个操作
   */
  private shouldInferAction(
    taskType: string,
    monitor: WindowActivityMonitor,
    platform: string
  ): boolean {
    const minActivityTime = {
      like: 3000,
      share: 8000,
      follow: 4000,
      comment: 15000
    };
    
    return monitor.windowActiveTime > (minActivityTime[taskType as keyof typeof minActivityTime] || 5000);
  }

  /**
   * 添加推断的操作
   */
  private addInferredAction(
    result: ClickDetectionResult,
    taskType: string,
    monitor: WindowActivityMonitor
  ): void {
    const confidence = this.calculateActionConfidence(taskType, monitor, 70);
    
    result.detectedActions.push({
      action: taskType,
      confidence,
      timestamp: Date.now(),
      evidence: ['time_based_inference', 'window_activity']
    });

    // 更新对应的点击计数
    switch (taskType) {
      case 'like':
        result.likeClicks++;
        break;
      case 'share':
        result.shareClicks++;
        break;
      case 'follow':
        result.followClicks++;
        break;
      case 'comment':
        result.commentClicks++;
        break;
    }
  }

  /**
   * 设置全局消息监听器
   */
  private setupGlobalMessageListener(): void {
    const messageHandler = (event: MessageEvent) => {
      if (event.data.type === 'CLICK_DETECTED') {
        this.handleClickDetected(event.data);
      } else if (event.data.type === 'ACTION_DETECTED') {
        this.handleActionDetected(event.data);
      } else if (event.data.type === 'CLICK_STATUS_UPDATE') {
        this.handleStatusUpdate(event.data);
      }
    };

    window.addEventListener('message', messageHandler);
    this.messageHandlers.add(messageHandler);
  }

  /**
   * 处理检测到的点击
   */
  private handleClickDetected(data: any): void {
    const result = this.detectionResults.get(data.sessionId);
    if (!result) return;

    result.totalClicks = data.totalClicks;
    result.clickTimestamps.push(data.timestamp);
    
    if (data.data.x && data.data.y) {
      result.clickPositions.push({
        x: data.data.x,
        y: data.data.y,
        timestamp: data.timestamp
      });
    }

    console.log(`🖱️ 检测到点击 - 会话: ${data.sessionId}, 总点击: ${data.totalClicks}`);
  }

  /**
   * 处理检测到的操作
   */
  private handleActionDetected(data: any): void {
    const result = this.detectionResults.get(data.sessionId);
    if (!result) return;

    result.detectedActions.push({
      action: data.action,
      confidence: data.data.confidence,
      timestamp: data.timestamp,
      evidence: data.data.evidence
    });

    // 更新对应的点击计数
    switch (data.action) {
      case 'like':
        result.likeClicks++;
        break;
      case 'share':
        result.shareClicks++;
        break;
      case 'follow':
        result.followClicks++;
        break;
      case 'comment':
        result.commentClicks++;
        break;
    }

    console.log(`✅ 检测到操作 - ${data.action}, 置信度: ${data.data.confidence}%`);
  }

  /**
   * 处理状态更新
   */
  private handleStatusUpdate(data: any): void {
    const result = this.detectionResults.get(data.sessionId);
    if (!result) return;

    result.totalClicks = data.status.totalClicks;
    result.detectedActions = data.status.detectedActions;
    result.clickPositions = data.status.clickPositions;
  }

  /**
   * 获取检测结果
   */
  getDetectionResult(sessionId: string): ClickDetectionResult | null {
    return this.detectionResults.get(sessionId) || null;
  }

  /**
   * 获取窗口活动监控数据
   */
  getWindowActivity(sessionId: string): WindowActivityMonitor | null {
    return this.windowMonitors.get(sessionId) || null;
  }

  /**
   * 停止检测
   */
  stopDetection(sessionId: string): void {
    this.detectionResults.delete(sessionId);
    this.windowMonitors.delete(sessionId);
    console.log(`🛑 停止点击检测 - 会话: ${sessionId}`);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.detectionResults.clear();
    this.windowMonitors.clear();
    
    this.messageHandlers.forEach(handler => {
      window.removeEventListener('message', handler);
    });
    this.messageHandlers.clear();
  }
}

export const crossDomainClickDetector = new CrossDomainClickDetector();
