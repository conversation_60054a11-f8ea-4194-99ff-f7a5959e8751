import { TaskExecution, ExecutionStatus } from '@/types';
import { db, getDatabase, USE_MOCK_DB } from '@/config/database';
import { MockDatabase } from '@/services/mockDatabase';

export class TaskExecutionModel {
  private static tableName = 'task_executions';

  /**
   * 创建任务执行记录
   */
  static async create(executionData: {
    task_id: number;
    executor_id: number;
    ip_address: string;
    user_agent?: string;
    execution_proof?: Record<string, any>;
  }): Promise<TaskExecution> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.createTaskExecution({
        ...executionData,
        execution_proof: executionData.execution_proof || {},
        verification_result: {},
        status: 'pending' as ExecutionStatus,
        submitted_at: new Date(),
      });
    }

    const [execution] = await db!(this.tableName)
      .insert({
        ...executionData,
        execution_proof: JSON.stringify(executionData.execution_proof || {}),
        verification_result: JSON.stringify({}),
        status: 'pending',
        submitted_at: new Date(),
      })
      .returning('*');

    return execution;
  }

  /**
   * 根据ID查找执行记录
   */
  static async findById(id: number): Promise<TaskExecution | null> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.findTaskExecutionById(id);
    }

    const execution = await db!(this.tableName).where({ id }).first();
    return execution || null;
  }

  /**
   * 获取用户的任务执行记录
   */
  static async findByExecutor(
    executorId: number,
    page: number = 1,
    limit: number = 20,
    status?: ExecutionStatus
  ): Promise<{
    executions: TaskExecution[];
    total: number;
    totalPages: number;
  }> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.findTaskExecutionsByExecutor(executorId, page, limit, status);
    }

    const offset = (page - 1) * limit;

    let query = db!(this.tableName)
      .select('task_executions.*', 'tasks.title as task_title', 'tasks.reward_points')
      .leftJoin('tasks', 'task_executions.task_id', 'tasks.id')
      .where('task_executions.executor_id', executorId);

    if (status) {
      query = query.where('task_executions.status', status);
    }

    const countResult = await query.clone().count('* as count');
    const total = Number(countResult[0]?.count) || 0;

    const executions = await query
      .orderBy('task_executions.submitted_at', 'desc')
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      executions,
      total,
      totalPages,
    };
  }

  /**
   * 获取任务的执行记录
   */
  static async findByTask(
    taskId: number,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    executions: TaskExecution[];
    total: number;
    totalPages: number;
  }> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.findTaskExecutionsByTask(taskId, page, limit);
    }

    const offset = (page - 1) * limit;

    const countResult = await db!(this.tableName)
      .where({ task_id: taskId })
      .count('* as count');

    const executions = await db!(this.tableName)
      .select('task_executions.*', 'users.username as executor_username')
      .leftJoin('users', 'task_executions.executor_id', 'users.id')
      .where('task_executions.task_id', taskId)
      .orderBy('task_executions.submitted_at', 'desc')
      .limit(limit)
      .offset(offset);

    const total = Number(countResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    return {
      executions,
      total,
      totalPages,
    };
  }

  /**
   * 更新执行状态
   */
  static async updateStatus(
    id: number,
    status: ExecutionStatus,
    verificationResult?: Record<string, any>,
    rejectionReason?: string
  ): Promise<TaskExecution | null> {
    const updateData: any = {
      status,
      updated_at: new Date(),
    };

    if (status === 'verified' || status === 'failed') {
      updateData.verified_at = new Date();
    }

    if (verificationResult) {
      updateData.verification_result = JSON.stringify(verificationResult);
    }

    if (rejectionReason) {
      updateData.rejection_reason = rejectionReason;
    }

    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.updateTaskExecution(id, updateData);
    }

    const [execution] = await db!(this.tableName)
      .where({ id })
      .update(updateData)
      .returning('*');

    return execution || null;
  }

  /**
   * 检查用户是否已执行过任务
   */
  static async hasExecuted(taskId: number, executorId: number): Promise<boolean> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      const execution = await mockDb.findTaskExecution(taskId, executorId);
      return !!execution;
    }

    const execution = await db!(this.tableName)
      .where({ task_id: taskId, executor_id: executorId })
      .first();

    return !!execution;
  }

  /**
   * 获取待验证的执行记录
   */
  static async findPendingVerifications(limit: number = 50): Promise<TaskExecution[]> {
    if (USE_MOCK_DB) {
      // 模拟数据库模式下返回空数组
      return [];
    }

    return db!(this.tableName)
      .select('task_executions.*', 'tasks.platform', 'tasks.task_type', 'tasks.video_url')
      .leftJoin('tasks', 'task_executions.task_id', 'tasks.id')
      .where('task_executions.status', 'pending')
      .where('task_executions.submitted_at', '<', new Date(Date.now() - 60000)) // 1分钟前提交的
      .orderBy('task_executions.submitted_at', 'asc')
      .limit(limit);
  }

  /**
   * 获取用户今日执行统计
   */
  static async getTodayExecutionCount(executorId: number): Promise<number> {
    if (USE_MOCK_DB) {
      // 模拟数据库模式下返回0
      return 0;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const countResult = await db!(this.tableName)
      .where('executor_id', executorId)
      .where('submitted_at', '>=', today)
      .where('submitted_at', '<', tomorrow)
      .count('* as count');

    return Number(countResult[0]?.count) || 0;
  }

  /**
   * 获取IP今日执行统计
   */
  static async getTodayExecutionCountByIP(ipAddress: string): Promise<number> {
    if (USE_MOCK_DB) {
      // 模拟数据库模式下返回0
      return 0;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const countResult = await db!(this.tableName)
      .where('ip_address', ipAddress)
      .where('submitted_at', '>=', today)
      .where('submitted_at', '<', tomorrow)
      .count('* as count');

    return Number(countResult[0]?.count) || 0;
  }

  /**
   * 获取用户执行统计
   */
  static async getExecutionStats(executorId: number): Promise<{
    total: number;
    pending: number;
    completed: number;
    verified: number;
    failed: number;
    rejected: number;
    successRate: number;
  }> {
    if (USE_MOCK_DB) {
      // 模拟数据库模式下返回默认统计
      return {
        total: 0,
        pending: 0,
        completed: 0,
        verified: 0,
        failed: 0,
        rejected: 0,
        successRate: 0,
      };
    }

    const stats = await db!(this.tableName)
      .select('status')
      .count('* as count')
      .where('executor_id', executorId)
      .groupBy('status');

    const result = {
      total: 0,
      pending: 0,
      completed: 0,
      verified: 0,
      failed: 0,
      rejected: 0,
      successRate: 0,
    };

    stats.forEach((stat) => {
      const count = Number(stat.count);
      result.total += count;
      (result as any)[stat.status] = count;
    });

    if (result.total > 0) {
      result.successRate = Math.round((result.verified / result.total) * 100);
    }

    return result;
  }

  /**
   * 批量更新过期的待验证记录
   */
  static async updateExpiredPending(): Promise<number> {
    if (USE_MOCK_DB) {
      // 模拟数据库模式下返回0
      return 0;
    }

    const expiredTime = new Date(Date.now() - 30 * 60 * 1000); // 30分钟前

    const updatedCount = await db!(this.tableName)
      .where('status', 'pending')
      .where('submitted_at', '<', expiredTime)
      .update({
        status: 'failed',
        verification_result: JSON.stringify({
          success: false,
          error_message: '验证超时',
          verified_at: new Date(),
        }),
        verified_at: new Date(),
        updated_at: new Date(),
      });

    return updatedCount;
  }

  /**
   * 删除执行记录
   */
  static async delete(id: number): Promise<boolean> {
    if (USE_MOCK_DB) {
      const mockDb = getDatabase() as typeof MockDatabase;
      return await mockDb.deleteTaskExecution(id);
    }

    const deletedCount = await db!(this.tableName).where({ id }).del();
    return deletedCount > 0;
  }

  /**
   * 获取系统执行统计
   */
  static async getSystemExecutionStats(): Promise<{
    totalExecutions: number;
    todayExecutions: number;
    successRate: number;
    platformStats: Record<string, number>;
  }> {
    if (USE_MOCK_DB) {
      // 模拟数据库模式下返回默认统计
      return {
        totalExecutions: 0,
        todayExecutions: 0,
        successRate: 0,
        platformStats: {},
      };
    }

    const totalResult = await db!(this.tableName)
      .count('* as count');

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayResult = await db!(this.tableName)
      .where('submitted_at', '>=', today)
      .where('submitted_at', '<', tomorrow)
      .count('* as count');

    const verifiedResult = await db!(this.tableName)
      .where('status', 'verified')
      .count('* as count');

    const platformStats = await db!(this.tableName)
      .select('tasks.platform')
      .count('* as count')
      .leftJoin('tasks', 'task_executions.task_id', 'tasks.id')
      .groupBy('tasks.platform');

    const totalExecutions = Number(totalResult[0]?.count) || 0;
    const todayExecutions = Number(todayResult[0]?.count) || 0;
    const verifiedExecutions = Number(verifiedResult[0]?.count) || 0;

    const platformStatsObj: Record<string, number> = {};
    platformStats.forEach((stat) => {
      if (stat.platform) {
        platformStatsObj[stat.platform] = Number(stat.count);
      }
    });

    return {
      totalExecutions,
      todayExecutions,
      successRate: totalExecutions > 0 ? Math.round((verifiedExecutions / totalExecutions) * 100) : 0,
      platformStats: platformStatsObj,
    };
  }
}
