import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { ApiResponse, Task, TaskExecution, User, ExecutionStatus, TaskType } from '@/types';
import { FileDB } from '@/services/fileDatabase';
import { identifyPlatform } from '@/utils/platformIdentifier';

/**
 * 获取我发布的任务
 */
export const getMyPublishedTasks = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;
  if (!userId) {
    res.status(401).json({ success: false, message: '未登录' });
    return;
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;

  const allTasks = await FileDB.find<Task>('tasks', task => task.publisher_id === userId);
  allTasks.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  const total = allTasks.length;
  const offset = (page - 1) * limit;
  const tasks = allTasks.slice(offset, offset + limit);

  res.json({
    success: true,
    message: '获取我发布的任务成功',
    data: {
      data: tasks,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  });
});

/**
 * 获取我参与的任务
 */
export const getMyParticipatedTasks = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;
  if (!userId) {
    res.status(401).json({ success: false, message: '未登录' });
    return;
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;

  const myExecutions = await FileDB.find<TaskExecution>('taskExecutions', e => e.executor_id === userId);
  const taskIds = myExecutions.map(e => e.task_id);
  const allTasks = await FileDB.find<Task>('tasks', t => taskIds.includes(t.id));

  const allTasksWithStatus = allTasks.map(task => {
    const myExecution = myExecutions.find(e => e.task_id === task.id);
    return {
      ...task,
      my_execution_status: myExecution?.status,
      my_execution_id: myExecution?.id,
      my_submitted_at: myExecution?.submitted_at,
      my_completed_at: myExecution?.verified_at,
    };
  });

  allTasksWithStatus.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  const total = allTasksWithStatus.length;
  const offset = (page - 1) * limit;
  const tasks = allTasksWithStatus.slice(offset, offset + limit);

  res.json({
    success: true,
    message: '获取我参与的任务成功',
    data: {
      data: tasks,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  });
});

/**
 * 获取任务大厅列表
 */
export const getTaskHall = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const platform = req.query.platform as string;
    const taskType = req.query.task_type as string;
    const status = (req.query.status as string) || 'active';

    let tasks = await FileDB.find<Task>('tasks', t => t.status === status);

    if (platform) {
        tasks = tasks.filter(t => t.platform === platform);
    }
    if (taskType) {
        tasks = tasks.filter(t => t.task_type === taskType);
    }

    tasks.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    const total = tasks.length;
    const offset = (page - 1) * limit;
    const paginatedTasks = tasks.slice(offset, offset + limit);

    res.json({
        success: true,
        message: '获取任务大厅成功',
        data: {
            tasks: paginatedTasks,
            pagination: { page, limit, total, pages: Math.ceil(total / limit) },
        },
    });
});


/**
 * 获取Dashboard统计数据
 */
export const getDashboardStats = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
    const userId = (req as any).user?.userId;
    if (!userId) {
        res.status(401).json({ success: false, message: '未登录' });
        return;
    }

    const user = await FileDB.getById<User>('users', userId);
    if (!user) {
        res.status(404).json({ success: false, message: '用户不存在' });
        return;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const publishedTasks = await FileDB.find<Task>('tasks', t => t.publisher_id === userId);
    const myExecutions = await FileDB.find<TaskExecution>('taskExecutions', e => e.executor_id === userId);
    const tasksIRun = await FileDB.find<Task>('tasks', t => myExecutions.map(e => e.task_id).includes(t.id));

    const completedExecutions = myExecutions.filter(e => e.status === 'completed');
    
    const todayExecutions = myExecutions.filter(e => new Date(e.created_at) >= today);
    const todayCompletedExecutions = completedExecutions.filter(e => new Date(e.verified_at!) >= today);
    
    const todayEarnedPoints = todayCompletedExecutions.reduce((sum, exec) => {
        const task = tasksIRun.find(t => t.id === exec.task_id);
        return sum + (task?.reward_points || 0);
    }, 0);

    const stats = {
        user: {
            username: user.username,
            points_balance: user.points_balance,
            email_verified: user.email_verified,
        },
        tasks: {
            published_count: publishedTasks.length,
            active_published_count: publishedTasks.filter(t => t.status === 'active').length,
            participated_count: myExecutions.length,
            completed_count: completedExecutions.length,
        },
        today: {
            participated_count: todayExecutions.length,
            earned_points: todayEarnedPoints,
        },
    };

    res.json({ success: true, message: '获取Dashboard统计成功', data: stats });
});

/**
 * 获取单个随机任务
 */
export const getRandomTask = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
    const { platform } = req.query;

    const tasks = await FileDB.find<Task>('tasks', t => t.status === 'active' && t.platform === platform);

    if (tasks.length > 0) {
        const randomIndex = Math.floor(Math.random() * tasks.length);
        res.json({ success: true, message: '获取随机任务成功', data: tasks[randomIndex] });
    } else {
        res.status(404).json({ success: false, message: '没有找到符合条件的任务' });
    }
});

/**
 * 获取任务详情
 */
export const getTaskById = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
    const { id } = req.params;
    const task = await FileDB.getById<Task>('tasks', Number(id));

    if (task) {
        res.json({ success: true, message: '获取任务详情成功', data: task });
    } else {
        res.status(404).json({ success: false, message: '任务不存在' });
    }
});

// 定义一个映射，为不同任务类型设置默认的标题和积分
const taskTypeDetails: Record<TaskType, { title: string; reward_points: number }> = {
    like: { title: '视频点赞', reward_points: 10 },
    share: { title: '分享视频', reward_points: 15 },
    comment: { title: '发表评论', reward_points: 20 },
    follow: { title: '关注账号', reward_points: 25 },
};

interface TaskItem {
    type: TaskType;
    count: number;
}

/**
 * 批量创建新任务
 */
export const createBatchTasks = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<Response | void> => {
    const userId = (req as any).user?.userId;
    const { video_url, tasks } = req.body as { video_url: string; tasks: TaskItem[] };

    if (!video_url || !tasks || !Array.isArray(tasks) || tasks.length === 0) {
        return res.status(400).json({ success: false, message: '请求参数无效' });
    }

    const platform = identifyPlatform(video_url);
    if (platform === 'Unknown') {
        return res.status(400).json({ success: false, message: '不支持的视频链接或格式不正确' });
    }

    const user = await FileDB.getById<User>('users', userId);
    if (!user) {
        return res.status(404).json({ success: false, message: '用户不存在' });
    }

    // 计算总成本
    const totalCost = tasks.reduce((sum, taskItem) => {
        const details = taskTypeDetails[taskItem.type];
        return sum + (details.reward_points * taskItem.count);
    }, 0);

    if (user.points_balance < totalCost) {
        return res.status(400).json({ success: false, message: `积分余额不足, 需要 ${totalCost} 积分` });
    }

    // 更新用户积分
    await FileDB.update<User>('users', userId, { points_balance: user.points_balance - totalCost });
    
    const now = new Date();
    const expires_at = new Date();
    expires_at.setDate(now.getDate() + 7); // 默认7天后过期

    const createdTasks: Task[] = [];

    for (const taskItem of tasks) {
        if(taskItem.count <= 0) continue;

        const details = taskTypeDetails[taskItem.type];
        const newTask: Omit<Task, 'id'> = {
            publisher_id: userId,
            title: details.title,
            description: `为 ${platform} 视频 "${video_url}" 进行 ${details.title}`,
            video_url,
            platform,
            task_type: taskItem.type,
            reward_points: details.reward_points,
            total_quota: taskItem.count,
            completed_count: 0,
            status: 'active',
            verification_rules: {},
            expires_at,
            created_at: now,
            updated_at: now,
        };
        const createdTask = await FileDB.create<Task>('tasks', newTask as any);
        createdTasks.push(createdTask);
    }
    
    res.status(201).json({ success: true, message: '任务批量创建成功', data: createdTasks });
});

/**
 * 创建新任务
 */
export const createTask = asyncHandler(async (
  req: Request, 
  res: Response<ApiResponse>
): Promise<Response | void> => {
    const userId = (req as any).user?.userId;
    const { title, description, video_url, task_type, reward_points, total_quota, expires_in_days } = req.body;

    const platform = identifyPlatform(video_url);
    if (platform === 'Unknown') {
        return res.status(400).json({ success: false, message: '不支持的视频链接或格式不正确' });
    }

    const user = await FileDB.getById<User>('users', userId);
    if (!user) {
        res.status(404).json({ success: false, message: '用户不存在' });
        return;
    }

    const totalCost = reward_points * total_quota;
    if (user.points_balance < totalCost) {
        res.status(400).json({ success: false, message: '积分余额不足' });
        return;
    }

    const expires_at = new Date();
    expires_at.setDate(expires_at.getDate() + expires_in_days);

    const now = new Date();
    const newTask: Omit<Task, 'id'> = {
        publisher_id: userId,
        title,
        description,
        video_url,
        platform,
        task_type,
        reward_points,
        total_quota,
        completed_count: 0,
        status: 'active',
        verification_rules: {},
        expires_at,
        created_at: now,
        updated_at: now,
    };
    
    // Manual transaction
    await FileDB.update<User>('users', userId, { points_balance: user.points_balance - totalCost });
    await FileDB.create<Task>('tasks', { ...newTask, platform } as Omit<Task, 'id'>);

    res.status(201).json({ success: true, message: '任务创建成功', data: { ...newTask, platform } });
});


/**
 * 更新任务信息
 */
export const updateTask = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
    const { id } = req.params;
    const userId = (req as any).user?.userId;
    const { title, description, total_quota, status } = req.body;

    const task = await FileDB.getById<Task>('tasks', Number(id));
    if (!task || task.publisher_id !== userId) {
        res.status(404).json({ success: false, message: '任务不存在或无权修改' });
        return;
    }

    await FileDB.update<Task>('tasks', Number(id), { title, description, total_quota, status, updated_at: new Date() } as Partial<Task>);
    res.json({ success: true, message: '任务更新成功' });
});


/**
 * 删除任务
 */
export const deleteTask = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
    const { id } = req.params;
    const userId = (req as any).user?.userId;

    const task = await FileDB.getById<Task>('tasks', Number(id));
    if (!task || task.publisher_id !== userId) {
        res.status(404).json({ success: false, message: '任务不存在或无权删除' });
        return;
    }

    await FileDB.delete('tasks', Number(id));
    res.json({ success: true, message: '任务删除成功' });
});

/**
 * 获取任务的执行记录
 */
export const getTaskExecutions = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
    const { taskId } = req.params;
    const userId = (req as any).user?.userId;

    const task = await FileDB.getById<Task>('tasks', Number(taskId));
    if (!task || task.publisher_id !== userId) {
        res.status(404).json({ success: false, message: '任务不存在或您不是发布者' });
        return;
    }

    const executions = await FileDB.find<TaskExecution>('taskExecutions', e => e.task_id === Number(taskId));
    res.json({ success: true, message: '获取执行记录成功', data: executions });
});

/**
 * 提交任务成果
 */
export const submitTaskExecution = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
    const { id } = req.params;
    const userId = (req as any).user?.userId;
    const { screenshot } = req.body;

    const task = await FileDB.getById<Task>('tasks', Number(id));
    if (!task || task.status !== 'active') {
        res.status(400).json({ success: false, message: '任务不可用' });
        return;
    }

    const now = new Date();
    const newExecution: Omit<TaskExecution, 'id'> = {
        task_id: Number(id),
        executor_id: userId,
        status: 'pending',
        submitted_at: now,
        execution_proof: { screenshot },
        created_at: now,
        updated_at: now,
        ip_address: req.ip || 'N/A',
        user_agent: req.headers['user-agent'] || 'N/A',
        verification_result: { success: false }
    };
    await FileDB.create<TaskExecution>('taskExecutions', newExecution);
    res.status(201).json({ success: true, message: '任务提交成功' });
});

/**
 * 审核任务执行
 */
export const reviewTaskExecution = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
    const { executionId } = req.params;
    const userId = (req as any).user?.userId;
    const { is_approved, comments } = req.body;

    const execution = await FileDB.getById<TaskExecution>('taskExecutions', Number(executionId));
    if (!execution) {
        res.status(404).json({ success: false, message: '执行记录不存在' });
        return;
    }

    const task = await FileDB.getById<Task>('tasks', execution.task_id);
    if (!task || task.publisher_id !== userId) {
        res.status(403).json({ success: false, message: '您无权审核此任务' });
        return;
    }

    const newStatus: ExecutionStatus = is_approved ? 'completed' : 'rejected';
    
    await FileDB.update<TaskExecution>('taskExecutions', Number(executionId), {
        status: newStatus,
        verified_at: new Date(),
        verification_result: {
            success: is_approved,
            verified_by: userId,
            verified_at: new Date(),
            comments,
        },
        updated_at: new Date(),
    } as Partial<TaskExecution>);

    if (is_approved) {
        const executor = await FileDB.getById<User>('users', execution.executor_id);
        if (executor) {
            await FileDB.update<User>('users', executor.id, { points_balance: executor.points_balance + task.reward_points });
        }
        await FileDB.update<Task>('tasks', task.id, { completed_count: (task.completed_count || 0) + 1 });
    }

    res.json({ success: true, message: '审核完成' });
});
