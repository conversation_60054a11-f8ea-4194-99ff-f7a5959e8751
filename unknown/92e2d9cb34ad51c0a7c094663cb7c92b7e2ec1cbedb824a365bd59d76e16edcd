import { identifyPlatform } from '../utils/platformIdentifier';

describe('Platform Identifier Tests', () => {
  describe('Douyin Platform', () => {
    test('should identify douyin.com video URLs', () => {
      const testUrls = [
        'https://www.douyin.com/video/1234567890',
        'http://douyin.com/video/9876543210',
        'douyin.com/video/1111111111',
        'https://www.douyin.com/note/2222222222',
        'https://douyin.com/share/video/3333333333'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('douyin');
      });
    });

    test('should identify v.douyin.com short URLs', () => {
      const testUrls = [
        'https://v.douyin.com/AbC123/',
        'http://v.douyin.com/XyZ789',
        'v.douyin.com/MnO456'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('douyin');
      });
    });

    test('should identify iesdouyin.com URLs', () => {
      const testUrls = [
        'https://www.iesdouyin.com/share/video/1234567890',
        'http://iesdouyin.com/share/video/9876543210'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('douyin');
      });
    });
  });

  describe('Kuaishou Platform', () => {
    test('should identify kuaishou.com video URLs', () => {
      const testUrls = [
        'https://www.kuaishou.com/short-video/1234567890',
        'http://kuaishou.com/short-video/9876543210',
        'kuaishou.com/short-video/1111111111',
        'https://kuaishou.com/profile/user123/video/2222222222'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('kuaishou');
      });
    });

    test('should identify v.kuaishou.com short URLs', () => {
      const testUrls = [
        'https://v.kuaishou.com/AbC123',
        'http://v.kuaishou.com/XyZ789/',
        'v.kuaishou.com/MnO456'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('kuaishou');
      });
    });
  });

  describe('Xiaohongshu Platform', () => {
    test('should identify xiaohongshu.com URLs', () => {
      const testUrls = [
        'https://www.xiaohongshu.com/explore/abc123def456',
        'http://xiaohongshu.com/explore/xyz789',
        'xiaohongshu.com/explore/mno456',
        'https://xiaohongshu.com/discovery/item/pqr789'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('xiaohongshu');
      });
    });

    test('should identify xhslink.com short URLs', () => {
      const testUrls = [
        'https://xhslink.com/AbC123',
        'http://xhslink.com/XyZ789',
        'xhslink.com/MnO456'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('xiaohongshu');
      });
    });
  });

  describe('Bilibili Platform', () => {
    test('should identify bilibili.com video URLs', () => {
      const testUrls = [
        'https://www.bilibili.com/video/BV1234567890',
        'http://bilibili.com/video/BV9876543210',
        'bilibili.com/video/BVabcdefghij',
        'https://bilibili.com/video/av12345678'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('bilibili');
      });
    });

    test('should identify b23.tv short URLs', () => {
      const testUrls = [
        'https://b23.tv/AbC123',
        'http://b23.tv/XyZ789',
        'b23.tv/MnO456'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('bilibili');
      });
    });
  });

  describe('YouTube Platform', () => {
    test('should identify youtube.com URLs', () => {
      const testUrls = [
        'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        'http://youtube.com/watch?v=abc123def456',
        'youtube.com/watch?v=xyz789',
        'https://www.youtube.com/shorts/AbC123DeF456',
        'https://m.youtube.com/watch?v=mobile123'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('youtube');
      });
    });

    test('should identify youtu.be short URLs', () => {
      const testUrls = [
        'https://youtu.be/dQw4w9WgXcQ',
        'http://youtu.be/abc123def456',
        'youtu.be/xyz789'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('youtube');
      });
    });
  });

  describe('TikTok Platform', () => {
    test('should identify tiktok.com URLs', () => {
      const testUrls = [
        'https://www.tiktok.com/@username/video/1234567890',
        'http://tiktok.com/@user123/video/9876543210',
        'tiktok.com/@testuser/video/1111111111',
        'https://tiktok.com/t/AbC123DeF456',
        'https://m.tiktok.com/@mobile/video/2222222222'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('tiktok');
      });
    });

    test('should identify vm.tiktok.com short URLs', () => {
      const testUrls = [
        'https://vm.tiktok.com/AbC123/',
        'http://vm.tiktok.com/XyZ789',
        'vm.tiktok.com/MnO456'
      ];
      
      testUrls.forEach(url => {
        expect(identifyPlatform(url)).toBe('tiktok');
      });
    });
  });

  describe('Edge Cases', () => {
    test('should handle URLs without protocol', () => {
      const testCases = [
        { url: 'douyin.com/video/123', expected: 'douyin' },
        { url: 'www.youtube.com/watch?v=abc', expected: 'youtube' },
        { url: 'tiktok.com/@user/video/123', expected: 'tiktok' }
      ];
      
      testCases.forEach(({ url, expected }) => {
        expect(identifyPlatform(url)).toBe(expected);
      });
    });

    test('should handle URLs with extra spaces and characters', () => {
      const testCases = [
        { url: '  https://douyin.com/video/123  ', expected: 'douyin' },
        { url: '"https://youtube.com/watch?v=abc"', expected: 'youtube' },
        { url: '<https://tiktok.com/@user/video/123>', expected: 'tiktok' }
      ];
      
      testCases.forEach(({ url, expected }) => {
        expect(identifyPlatform(url)).toBe(expected);
      });
    });

    test('should return Unknown for invalid URLs', () => {
      const invalidUrls = [
        '',
        null,
        undefined,
        'not-a-url',
        'https://unknown-platform.com/video/123',
        'ftp://douyin.com/video/123'
      ];
      
      invalidUrls.forEach(url => {
        expect(identifyPlatform(url as any)).toBe('Unknown');
      });
    });

    test('should handle URLs with query parameters and fragments', () => {
      const testCases = [
        { url: 'https://douyin.com/video/123?param=value#fragment', expected: 'douyin' },
        { url: 'https://youtube.com/watch?v=abc&t=30s', expected: 'youtube' },
        { url: 'https://bilibili.com/video/BV123?p=1&t=120', expected: 'bilibili' }
      ];
      
      testCases.forEach(({ url, expected }) => {
        expect(identifyPlatform(url)).toBe(expected);
      });
    });
  });
});
