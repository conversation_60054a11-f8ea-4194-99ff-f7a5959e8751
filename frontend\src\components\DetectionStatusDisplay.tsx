import React, { useState, useEffect } from 'react';
import { detectionDiagnosticsService, DetectionDiagnostics, DetectionStatus } from '@/services/detectionDiagnostics';

interface DetectionStatusDisplayProps {
  sessionId: string;
  platform: string;
  duration: number;
  extensionData?: any;
  realData?: any;
  legacyData?: any;
  intelligentData?: any;
  onRetry?: () => void;
  onForceValidation?: () => void;
  onUserConfirmation?: (confirmed: boolean) => void;
}

export const DetectionStatusDisplay: React.FC<DetectionStatusDisplayProps> = ({
  sessionId,
  platform,
  duration,
  extensionData,
  realData,
  legacyData,
  intelligentData,
  onRetry,
  onForceValidation,
  onUserConfirmation
}) => {
  const [diagnostics, setDiagnostics] = useState<DetectionDiagnostics | null>(null);
  const [status, setStatus] = useState<DetectionStatus | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const newDiagnostics = detectionDiagnosticsService.diagnoseDetectionSystem(
      sessionId,
      platform,
      duration,
      extensionData,
      realData,
      legacyData,
      intelligentData
    );

    const newStatus = detectionDiagnosticsService.getOverallDetectionStatus(
      extensionData,
      realData,
      legacyData,
      intelligentData
    );

    setDiagnostics(newDiagnostics);
    setStatus(newStatus);
  }, [sessionId, platform, duration, extensionData, realData, legacyData, intelligentData]);

  if (!diagnostics || !status) {
    return <div className="detection-status loading">正在诊断检测系统...</div>;
  }

  const getStatusIcon = (detectorStatus: string) => {
    switch (detectorStatus) {
      case 'working': return '✅';
      case 'failed': return '❌';
      case 'no_data': return '⚪';
      default: return '❓';
    }
  };

  const getStatusColor = (overallStatus: string) => {
    switch (overallStatus) {
      case 'healthy': return '#10b981';
      case 'degraded': return '#f59e0b';
      case 'failed': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const handleAction = () => {
    switch (diagnostics.recommendedAction) {
      case 'retry':
        onRetry?.();
        break;
      case 'force_validation':
        onForceValidation?.();
        break;
      case 'user_confirmation':
        // 显示用户确认对话框
        const confirmed = window.confirm(
          detectionDiagnosticsService.createUserFriendlyMessage(diagnostics, platform) +
          '\n\n您是否已经完成了所需的操作？'
        );
        onUserConfirmation?.(confirmed);
        break;
      case 'manual_review':
        alert('任务将转入人工审核，请提供完成截图作为证明。');
        break;
    }
  };

  return (
    <div className="detection-status-display">
      <div className="status-header">
        <div 
          className="status-indicator"
          style={{ backgroundColor: getStatusColor(status.overallStatus) }}
        >
          <span className="status-text">
            {status.overallStatus === 'healthy' && '检测系统正常'}
            {status.overallStatus === 'degraded' && '检测系统降级'}
            {status.overallStatus === 'failed' && '检测系统失效'}
          </span>
        </div>
        <button 
          className="details-toggle"
          onClick={() => setShowDetails(!showDetails)}
        >
          {showDetails ? '隐藏详情' : '显示详情'}
        </button>
      </div>

      {showDetails && (
        <div className="status-details">
          <div className="detector-status">
            <h4>检测器状态</h4>
            <div className="detector-list">
              <div className="detector-item">
                {getStatusIcon(status.extensionDetector)} 扩展式检测器
              </div>
              <div className="detector-item">
                {getStatusIcon(status.realDetector)} 真实检测器
              </div>
              <div className="detector-item">
                {getStatusIcon(status.legacyDetector)} 传统检测器
              </div>
              <div className="detector-item">
                {getStatusIcon(status.intelligentDetector)} 智能检测器
              </div>
            </div>
          </div>

          {diagnostics.issues.length > 0 && (
            <div className="issues-section">
              <h4>检测到的问题</h4>
              <ul className="issues-list">
                {diagnostics.issues.map((issue, index) => (
                  <li key={index} className="issue-item">❌ {issue}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="solutions-section">
            <h4>建议解决方案</h4>
            <ul className="solutions-list">
              {diagnostics.solutions.map((solution, index) => (
                <li key={index} className="solution-item">💡 {solution}</li>
              ))}
            </ul>
          </div>

          <div className="confidence-section">
            <h4>检测置信度</h4>
            <div className="confidence-bar">
              <div 
                className="confidence-fill"
                style={{ 
                  width: `${diagnostics.confidence}%`,
                  backgroundColor: diagnostics.confidence > 70 ? '#10b981' : 
                                 diagnostics.confidence > 40 ? '#f59e0b' : '#ef4444'
                }}
              />
              <span className="confidence-text">{diagnostics.confidence}%</span>
            </div>
          </div>
        </div>
      )}

      <div className="action-section">
        <p className="user-message">
          {detectionDiagnosticsService.createUserFriendlyMessage(diagnostics, platform)}
        </p>
        
        <button 
          className={`action-button ${diagnostics.recommendedAction}`}
          onClick={handleAction}
        >
          {diagnostics.recommendedAction === 'retry' && '🔄 重试任务'}
          {diagnostics.recommendedAction === 'force_validation' && '🔧 强制验证'}
          {diagnostics.recommendedAction === 'user_confirmation' && '🤔 确认完成'}
          {diagnostics.recommendedAction === 'manual_review' && '👨‍💼 人工审核'}
        </button>
      </div>

      <style jsx>{`
        .detection-status-display {
          background: white;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          margin: 20px 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .status-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }

        .status-indicator {
          padding: 8px 16px;
          border-radius: 20px;
          color: white;
          font-weight: 500;
        }

        .details-toggle {
          background: #f3f4f6;
          border: none;
          padding: 6px 12px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
        }

        .details-toggle:hover {
          background: #e5e7eb;
        }

        .status-details {
          background: #f9fafb;
          border-radius: 6px;
          padding: 15px;
          margin-bottom: 15px;
        }

        .detector-status h4,
        .issues-section h4,
        .solutions-section h4,
        .confidence-section h4 {
          margin: 0 0 10px 0;
          font-size: 14px;
          font-weight: 600;
          color: #374151;
        }

        .detector-list {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;
          margin-bottom: 15px;
        }

        .detector-item {
          font-size: 14px;
          color: #6b7280;
        }

        .issues-list,
        .solutions-list {
          margin: 0;
          padding-left: 0;
          list-style: none;
        }

        .issue-item,
        .solution-item {
          font-size: 14px;
          margin-bottom: 5px;
          color: #6b7280;
        }

        .confidence-section {
          margin-top: 15px;
        }

        .confidence-bar {
          position: relative;
          background: #e5e7eb;
          height: 20px;
          border-radius: 10px;
          overflow: hidden;
        }

        .confidence-fill {
          height: 100%;
          transition: width 0.3s ease;
        }

        .confidence-text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 12px;
          font-weight: 600;
          color: white;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .action-section {
          text-align: center;
        }

        .user-message {
          background: #f0f9ff;
          border: 1px solid #0ea5e9;
          border-radius: 6px;
          padding: 12px;
          margin-bottom: 15px;
          font-size: 14px;
          line-height: 1.5;
          white-space: pre-line;
        }

        .action-button {
          background: #3b82f6;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .action-button:hover {
          background: #2563eb;
        }

        .action-button.retry {
          background: #10b981;
        }

        .action-button.retry:hover {
          background: #059669;
        }

        .action-button.force_validation {
          background: #f59e0b;
        }

        .action-button.force_validation:hover {
          background: #d97706;
        }

        .action-button.user_confirmation {
          background: #8b5cf6;
        }

        .action-button.user_confirmation:hover {
          background: #7c3aed;
        }

        .action-button.manual_review {
          background: #ef4444;
        }

        .action-button.manual_review:hover {
          background: #dc2626;
        }
      `}</style>
    </div>
  );
};
