// Test the regex patterns directly
const testUrl = 'https://www.bilibili.com/video/BV1Hk7TzUEL1/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=3d345170a9f40f6dd7b839003d919f55';

console.log('🧪 Testing Bilibili URL Recognition Fix...\n');
console.log('Testing URL:', testUrl);
console.log('');

// Test the old patterns (should fail)
console.log('🔍 Testing OLD patterns:');
const oldPatterns = [
  /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(BV[A-Za-z0-9]+)/,
  /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(av\d+)/,
];

oldPatterns.forEach((pattern, index) => {
  const match = testUrl.match(pattern);
  console.log(`Old Pattern ${index + 1}: ${pattern}`);
  console.log(`Match result:`, match ? match[1] : 'No match');
});

console.log('\n🔍 Testing NEW patterns:');
const newPatterns = [
  /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(BV[A-Za-z0-9]+)(?:\/|\?|$)/,
  /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(av\d+)(?:\/|\?|$)/,
];

newPatterns.forEach((pattern, index) => {
  const match = testUrl.match(pattern);
  console.log(`New Pattern ${index + 1}: ${pattern}`);
  console.log(`Match result:`, match ? match[1] : 'No match');
});

// Test various Bilibili URL formats
console.log('\n🔍 Testing various Bilibili URL formats:');
const testUrls = [
  'https://www.bilibili.com/video/BV1Hk7TzUEL1',
  'https://www.bilibili.com/video/BV1Hk7TzUEL1/',
  'https://www.bilibili.com/video/BV1Hk7TzUEL1/?param=value',
  'https://bilibili.com/video/BV1234567890',
  'https://www.bilibili.com/video/av12345678',
  'https://www.bilibili.com/video/av12345678/?param=value'
];

const finalPattern = /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(BV[A-Za-z0-9]+)(?:\/|\?|$)/;
const avPattern = /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(av\d+)(?:\/|\?|$)/;

testUrls.forEach((url, index) => {
  const bvMatch = url.match(finalPattern);
  const avMatch = url.match(avPattern);
  const match = bvMatch || avMatch;
  console.log(`${index + 1}. ${url}`);
  console.log(`   Match: ${match ? match[1] : 'No match'}`);
});

console.log('\n✅ Test Complete!');
