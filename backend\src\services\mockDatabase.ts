import { v4 as uuidv4 } from 'uuid';
import { Platform, TaskType, ExecutionStatus } from '@/types';

/**
 * 内存中的模拟数据库
 */
export class MockDatabase {
  private static instance: MockDatabase;
  private data: {
    users: any[];
    tasks: any[];
    taskExecutions: any[];
    pointTransactions: any[];
  };

  private constructor() {
    this.data = {
      users: [],
      tasks: [],
      taskExecutions: [],
      pointTransactions: []
    };
    this.initializeData();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): MockDatabase {
    if (!MockDatabase.instance) {
      MockDatabase.instance = new MockDatabase();
    }
    return MockDatabase.instance;
  }

  /**
   * 初始化模拟数据
   */
  private initializeData(): void {
    // 创建一些测试用户
    this.data.users = [
      {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        password_hash: '$2b$10$9Yt5YKqHkyngXYx5pFXcI.YQglfKHiEG3vVJsGjW5ZUMVNUgy1bFG', // password: admin123
        role: 'admin',
        points_balance: 1000,
        created_at: new Date('2023-01-01'),
        updated_at: new Date('2023-01-01')
      },
      {
        id: '2',
        username: 'user1',
        email: '<EMAIL>',
        password_hash: '$2b$10$9Yt5YKqHkyngXYx5pFXcI.YQglfKHiEG3vVJsGjW5ZUMVNUgy1bFG', // password: admin123
        role: 'user',
        points_balance: 500,
        created_at: new Date('2023-01-02'),
        updated_at: new Date('2023-01-02')
      }
    ];

    // 创建一些测试任务
    this.data.tasks = [
      {
        id: '1',
        publisher_id: '1',
        platform: 'douyin' as Platform,
        task_type: 'like' as TaskType,
        video_url: 'https://v.douyin.com/example1/',
        description: '点赞这个视频',
        quota: 10,
        completed_count: 5,
        reward_points: 10,
        status: 'active',
        created_at: new Date('2023-01-10'),
        updated_at: new Date('2023-01-10')
      },
      {
        id: '2',
        publisher_id: '1',
        platform: 'kuaishou' as Platform,
        task_type: 'share' as TaskType,
        video_url: 'https://v.kuaishou.com/example2/',
        description: '分享这个视频',
        quota: 5,
        completed_count: 2,
        reward_points: 15,
        status: 'active',
        created_at: new Date('2023-01-11'),
        updated_at: new Date('2023-01-11')
      }
    ];

    // 创建一些测试任务执行记录
    this.data.taskExecutions = [
      {
        id: '1',
        task_id: '1',
        user_id: '2',
        status: 'completed' as ExecutionStatus,
        proof_image: 'https://example.com/proof1.jpg',
        comment: '已完成',
        points_earned: 10,
        created_at: new Date('2023-01-12'),
        updated_at: new Date('2023-01-12')
      }
    ];

    // 创建一些测试积分交易记录
    this.data.pointTransactions = [
      {
        id: '1',
        user_id: '2',
        amount: 10,
        type: 'earn',
        description: '完成任务获得积分',
        related_task_id: '1',
        created_at: new Date('2023-01-12')
      },
      {
        id: '2',
        user_id: '1',
        amount: -10,
        type: 'spend',
        description: '发布任务消费积分',
        related_task_id: '1',
        created_at: new Date('2023-01-10')
      }
    ];
  }

  /**
   * 获取表中的所有数据
   */
  public getAll(table: string): any[] {
    return [...this.data[table as keyof typeof this.data]];
  }

  /**
   * 根据ID获取记录
   */
  public getById(table: string, id: string): any | null {
    const items = this.data[table as keyof typeof this.data];
    return items.find((item: any) => item.id === id) || null;
  }

  /**
   * 根据条件查询记录
   */
  public where(table: string, conditions: Record<string, any>): any[] {
    const items = this.data[table as keyof typeof this.data];
    return items.filter((item: any) => {
      return Object.keys(conditions).every(key => {
        if (conditions[key] === undefined) return true;
        return item[key] === conditions[key];
      });
    });
  }

  /**
   * 插入新记录
   */
  public insert(table: string, data: any): any {
    const newItem = {
      ...data,
      id: data.id || uuidv4(),
      created_at: data.created_at || new Date(),
      updated_at: data.updated_at || new Date()
    };
    this.data[table as keyof typeof this.data].push(newItem);
    return newItem;
  }

  /**
   * 更新记录
   */
  public update(table: string, id: string, data: any): any {
    const items = this.data[table as keyof typeof this.data];
    const index = items.findIndex((item: any) => item.id === id);
    if (index === -1) return null;
    
    const updatedItem = {
      ...items[index],
      ...data,
      updated_at: new Date()
    };
    items[index] = updatedItem;
    return updatedItem;
  }

  /**
   * 删除记录
   */
  public delete(table: string, id: string): boolean {
    const items = this.data[table as keyof typeof this.data];
    const index = items.findIndex((item: any) => item.id === id);
    if (index === -1) return false;
    
    items.splice(index, 1);
    return true;
  }

  /**
   * 事务处理（模拟）
   */
  public async transaction(callback: (trx: any) => Promise<any>): Promise<any> {
    try {
      // 创建数据快照
      const snapshot = JSON.stringify(this.data);
      
      // 执行事务回调
      const result = await callback(this);
      
      return result;
    } catch (error) {
      // 发生错误时恢复数据
      this.data = JSON.parse(JSON.stringify(this.data));
      throw error;
    }
  }
} 