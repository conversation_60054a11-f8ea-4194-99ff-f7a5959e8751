import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { useMutation, useQueryClient } from 'react-query';
import { useAuthStore } from '@/store/authStore';
import { AuthService } from '@/services/authService';
import {
  Home,
  List,
  Plus,
  User,
  Coins,
  Menu,
  X,
  LogOut,
  Bell,
  AlertTriangle,
} from 'lucide-react';
import { clsx } from 'clsx';
import { toast } from 'react-hot-toast';

const Layout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const { user, logout, login } = useAuthStore();
  const location = useLocation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  useEffect(() => {
    // 检查本地存储的认证信息
    const storedTokens = localStorage.getItem('auth_tokens');
    if (storedTokens && !user) {
      try {
        const tokens = JSON.parse(storedTokens);
        // 验证token有效性并获取用户信息
        AuthService.getCurrentUser()
          .then(response => {
            if (response.data?.user) {
              login(response.data.user, tokens);
            }
          })
          .catch(() => {
            // Token无效，清除本地存储
            localStorage.removeItem('auth_tokens');
            localStorage.removeItem('user_data');
          });
      } catch (error) {
        console.error('解析存储的认证信息失败:', error);
        localStorage.removeItem('auth_tokens');
        localStorage.removeItem('user_data');
      }
    }
  }, [user, login]);

  const navigation = [
    { name: '首页', href: '/dashboard', icon: Home },
    { name: '任务大厅', href: '/tasks', icon: List },
    { name: '智能发布', href: '/tasks/publish', icon: Plus },
    { name: '我的任务', href: '/my-tasks', icon: User },
    { name: '积分记录', href: '/points', icon: Coins },
  ];

  const isActive = (href: string) => {
    return location.pathname === href;
  };

  // 退出登录mutation
  const logoutMutation = useMutation(
    async () => {
      // 尝试调用后端退出登录API
      try {
        await AuthService.logout();
      } catch (error) {
        // 即使后端API失败，也要继续本地清理
        console.warn('后端退出登录API调用失败，继续本地清理:', error);
      }
    },
    {
      onSuccess: () => {
        // 清除React Query缓存
        queryClient.clear();

        // 清除AuthStore状态
        logout();

        // 清除所有本地存储
        localStorage.clear();
        sessionStorage.clear();

        // 显示成功提示
        toast.success('已安全退出登录');

        // 重定向到登录页面
        navigate('/login', { replace: true });

        // 关闭对话框
        setShowLogoutDialog(false);
      },
      onError: (error: any) => {
        console.error('退出登录失败:', error);

        // 即使出错也要进行本地清理
        queryClient.clear();
        logout();
        localStorage.clear();
        sessionStorage.clear();

        toast.error('退出登录时出现问题，但已清除本地数据');
        navigate('/login', { replace: true });
        setShowLogoutDialog(false);
      },
    }
  );

  const handleLogout = () => {
    setShowLogoutDialog(true);
  };

  const confirmLogout = () => {
    logoutMutation.mutate();
  };

  const cancelLogout = () => {
    setShowLogoutDialog(false);
  };

  const isPublishPage = location.pathname === '/tasks/publish';

  return (
    <div className={clsx("h-screen flex", !isPublishPage && "bg-gray-50")}>
      {/* 移动端侧边栏遮罩 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <div
        className={clsx(
          'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:flex lg:flex-col',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <h1 className="text-xl font-bold text-gray-900">互助点赞</h1>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* 用户信息 */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-primary-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900">
                  {user?.username}
                </p>
                <p className="text-xs text-gray-500">
                  积分: {user?.points_balance || 0}
                </p>
              </div>
            </div>
          </div>

          {/* 导航菜单 */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={clsx(
                    'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    isActive(item.href)
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  )}
                  onClick={() => setSidebarOpen(false)}
                >
                  <Icon className="w-5 h-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* 底部操作 */}
          <div className="p-4 border-t border-gray-200">
            <Link
              to="/profile"
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-colors mb-2"
              onClick={() => setSidebarOpen(false)}
            >
              <User className="w-5 h-5 mr-3" />
              个人设置
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2 text-sm font-medium text-red-600 rounded-lg hover:bg-red-50 transition-colors"
            >
              <LogOut className="w-5 h-5 mr-3" />
              退出登录
            </button>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col lg:pl-0">
        {/* 顶部导航栏 */}
        <header className="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
            >
              <Menu className="w-5 h-5" />
            </button>

            <div className="flex items-center space-x-4">
              {/* 通知按钮 */}
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                <Bell className="w-5 h-5" />
              </button>

              {/* 用户头像 */}
              <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-primary-600" />
              </div>
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <main className="flex-1 overflow-auto">
          {isPublishPage ? (
            <Outlet />
          ) : (
          <div className="p-4 sm:p-6 lg:p-8">
            <Outlet />
          </div>
          )}
        </main>
      </div>

      {/* 退出登录确认对话框 */}
      {showLogoutDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-orange-100 rounded-full">
                <AlertTriangle className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">确认退出登录</h3>
                <p className="text-sm text-gray-500">您确定要退出当前账户吗？</p>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-gray-600 text-sm">
                退出登录后，您需要重新输入用户名和密码才能访问账户。
                所有本地数据将被清除以确保账户安全。
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelLogout}
                disabled={logoutMutation.isLoading}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
              >
                取消
              </button>
              <button
                onClick={confirmLogout}
                disabled={logoutMutation.isLoading}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                {logoutMutation.isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>退出中...</span>
                  </>
                ) : (
                  <>
                    <LogOut className="w-4 h-4" />
                    <span>确认退出</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Layout;
