console.log('🔍 测试Node.js环境...');
console.log('Node.js版本:', process.version);
console.log('当前目录:', process.cwd());
console.log('平台:', process.platform);
console.log('架构:', process.arch);

const fs = require('fs');
const path = require('path');

// 检查项目文件
const projectFiles = [
    'package.json',
    'backend/package.json',
    'frontend/package.json'
];

console.log('\n📁 检查项目文件:');
projectFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`${exists ? '✅' : '❌'} ${file}`);
});

// 检查扩展式检测器文件
const detectorFiles = [
    'frontend/src/services/browserExtensionDetector.ts',
    'frontend/src/services/realCrossDomainDetector.ts',
    'frontend/src/services/taskExecutionService.ts'
];

console.log('\n🔌 检查扩展式检测器文件:');
detectorFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`${exists ? '✅' : '❌'} ${file}`);
});

console.log('\n✅ 环境测试完成');
