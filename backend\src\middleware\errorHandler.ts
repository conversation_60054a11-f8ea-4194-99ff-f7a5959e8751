import { Request, Response, NextFunction } from 'express';
import { ValidationError } from 'express-validator';
import logger from '@/utils/logger';
import { ApiResponse } from '@/types';

/**
 * 自定义错误类
 */
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 异步错误处理包装器
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404错误处理中间件
 */
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (req.originalUrl === '/') {
    res.send('<h1>Welcome to the Mutual-Like Platform API</h1><p>API documentation is available at <a href="/api-docs">/api-docs</a></p>');
    return;
  }
  const error = new AppError(`路径 ${req.originalUrl} 不存在`, 404);
  next(error);
};

/**
 * 全局错误处理中间件
 */
export const globalErrorHandler = (
  error: any,
  req: Request,
  res: Response<ApiResponse>,
  _next: NextFunction
): void => {
  let statusCode = error.statusCode || 500;
  let message = error.message || '服务器内部错误';

  // 记录错误日志
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId,
  });

  // 处理不同类型的错误
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = '数据验证失败';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = '无效的数据格式';
  } else if (error.code === '23505') {
    // PostgreSQL唯一约束违反
    statusCode = 409;
    message = '数据已存在';
  } else if (error.code === '23503') {
    // PostgreSQL外键约束违反
    statusCode = 400;
    message = '关联数据不存在';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = '无效的访问令牌';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = '访问令牌已过期';
  }

  // 开发环境返回详细错误信息
  const response: ApiResponse = {
    success: false,
    error: message,
  };

  if (process.env['NODE_ENV'] === 'development') {
    response.error = error.message;
    (response as any).stack = error.stack;
  }

  res.status(statusCode).json(response);
};

/**
 * 验证错误处理中间件
 */
export const handleValidationErrors = (
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): void => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map((error: ValidationError) => error.msg);
    res.status(400).json({
      success: false,
      error: '数据验证失败',
      message: errorMessages.join(', '),
    });
    return;
  }

  next();
};

/**
 * 数据库错误处理
 */
export const handleDatabaseError = (error: any): AppError => {
  if (error.code === '23505') {
    return new AppError('数据已存在', 409);
  }
  
  if (error.code === '23503') {
    return new AppError('关联数据不存在', 400);
  }
  
  if (error.code === '23502') {
    return new AppError('必填字段不能为空', 400);
  }
  
  return new AppError('数据库操作失败', 500);
};

/**
 * JWT错误处理
 */
export const handleJWTError = (error: any): AppError => {
  if (error.name === 'JsonWebTokenError') {
    return new AppError('无效的访问令牌', 401);
  }
  
  if (error.name === 'TokenExpiredError') {
    return new AppError('访问令牌已过期', 401);
  }
  
  return new AppError('认证失败', 401);
};
