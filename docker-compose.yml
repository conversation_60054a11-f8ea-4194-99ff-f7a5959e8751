version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: mutual-like-db
    environment:
      POSTGRES_DB: mutual_like_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    networks:
      - mutual-like-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: mutual-like-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mutual-like-network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: mutual-like-backend
    environment:
      NODE_ENV: production
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      EMAIL_HOST: smtp.gmail.com
      EMAIL_PORT: 587
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: your-email-password
    ports:
      - "3000:3000"
    depends_on:
      - redis
    volumes:
      - ./backend/uploads:/app/uploads
    networks:
      - mutual-like-network

  # 前端Web应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: mutual-like-frontend
    environment:
      VITE_API_BASE_URL: http://localhost:3000/api
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - mutual-like-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: mutual-like-nginx
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    networks:
      - mutual-like-network

volumes:
  redis_data:

networks:
  mutual-like-network:
    driver: bridge
