import { Router } from 'express';
import {
  getMyPublishedTasks,
  getMyParticipatedTasks,
  getTaskHall,
  getDashboardStats,
  getRandomTask,
  createTask,
  createBatchTasks,
  getTaskById
} from '@/controllers/tasksController';
import { authenticateToken } from '@/middleware/auth';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Task:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 任务ID
 *         title:
 *           type: string
 *           description: 任务标题
 *         description:
 *           type: string
 *           description: 任务描述
 *         platform:
 *           type: string
 *           enum: [douyin, kuaishou, xiaohongshu]
 *           description: 平台类型
 *         task_type:
 *           type: string
 *           enum: [like, comment, share, follow]
 *           description: 任务类型
 *         content_url:
 *           type: string
 *           description: 内容链接
 *         reward_points:
 *           type: integer
 *           description: 奖励积分
 *         total_quota:
 *           type: integer
 *           description: 总配额
 *         completed_count:
 *           type: integer
 *           description: 已完成数量
 *         status:
 *           type: string
 *           enum: [active, completed, expired, cancelled]
 *           description: 任务状态
 *         expires_at:
 *           type: string
 *           format: date-time
 *           description: 过期时间
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 */

/**
 * @swagger
 * /tasks/my/published:
 *   get:
 *     summary: 获取我发布的任务
 *     tags: [任务]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Task'
 *       401:
 *         description: 未登录
 */
router.get('/my/published', authenticateToken, getMyPublishedTasks);

/**
 * @swagger
 * /tasks/my/participated:
 *   get:
 *     summary: 获取我参与的任务
 *     tags: [任务]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Task'
 *       401:
 *         description: 未登录
 */
router.get('/my/participated', authenticateToken, getMyParticipatedTasks);

/**
 * @swagger
 * /tasks/hall:
 *   get:
 *     summary: 获取任务大厅
 *     tags: [任务]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: platform
 *         schema:
 *           type: string
 *           enum: [douyin, kuaishou, xiaohongshu]
 *         description: 平台过滤
 *       - in: query
 *         name: task_type
 *         schema:
 *           type: string
 *           enum: [like, comment, share, follow]
 *         description: 任务类型过滤
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, completed, expired, cancelled]
 *         description: 状态过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     tasks:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Task'
 *                     pagination:
 *                       type: object
 */
router.get('/hall', getTaskHall);

/**
 * @swagger
 * /tasks/dashboard/stats:
 *   get:
 *     summary: 获取Dashboard统计数据
 *     tags: [任务]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       401:
 *         description: 未登录
 */
router.get('/dashboard/stats', authenticateToken, getDashboardStats);

/**
 * @swagger
 * /tasks/random:
 *   get:
 *     summary: 获取单个随机任务
 *     tags: [任务]
 *     parameters:
 *       - in: query
 *         name: platform
 *         required: true
 *         schema:
 *           type: string
 *           enum: [douyin, kuaishou, xiaohongshu, bilibili]
 *         description: 平台
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Task'
 *       400:
 *         description: 缺少平台参数
 *       404:
 *         description: 该平台暂无可用任务
 */
router.get('/random', getRandomTask);

/**
 * @swagger
 * /tasks/batch:
 *   post:
 *     summary: 批量创建新任务
 *     tags: [任务]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               $ref: '#/components/schemas/Task'
 *     responses:
 *       201:
 *         description: 任务批量创建成功
 *       400:
 *         description: 无效的请求数据
 *       401:
 *         description: 未登录
 */
router.post('/batch', authenticateToken, createBatchTasks);

/**
 * @swagger
 * /tasks:
 *   post:
 *     summary: 创建一个新任务
 *     tags: [任务]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Task' # Ideally a CreateTaskDTO
 *     responses:
 *       201:
 *         description: 任务创建成功
 *       400:
 *         description: 无效的请求数据
 *       401:
 *         description: 未登录
 */
router.post('/', authenticateToken, createTask);

/**
 * @swagger
 * /tasks/:id:
 *   get:
 *     summary: 获取任务详情
 *     tags: [任务]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: 任务ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Task'
 *       404:
 *         description: 任务不存在
 */
router.get('/:id', getTaskById);

export default router;
