// Comprehensive test for the specific Bilibili URL
const testUrl = 'https://www.bilibili.com/video/BV1Hk7TzUEL1/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=3d345170a9f40f6dd7b839003d919f55';

console.log('🧪 Comprehensive Bilibili URL Recognition Test');
console.log('=' .repeat(60));
console.log('Testing URL:', testUrl);
console.log('');

// Test 1: SmartTaskForm validation patterns
console.log('📋 Test 1: SmartTaskForm Validation Patterns');
console.log('-'.repeat(40));
const smartTaskFormPatterns = [
  { name: '抖音 video', pattern: /douyin\.com\/video/ },
  { name: '抖音 note', pattern: /douyin\.com\/note/ },
  { name: '抖音 short', pattern: /v\.douyin\.com/ },
  { name: '抖音 ies', pattern: /iesdouyin\.com/ },
  { name: '快手 video', pattern: /kuaishou\.com\/short-video/ },
  { name: '快手 short', pattern: /v\.kuaishou\.com/ },
  { name: '小红书 explore', pattern: /xiaohongshu\.com\/explore/ },
  { name: '小红书 discovery', pattern: /xiaohongshu\.com\/discovery/ },
  { name: '小红书 short', pattern: /xhslink\.com/ },
  { name: 'B站 video', pattern: /bilibili\.com\/video/ },
  { name: 'B站 short', pattern: /b23\.tv/ },
  { name: 'YouTube watch', pattern: /youtube\.com\/watch/ },
  { name: 'YouTube shorts', pattern: /youtube\.com\/shorts/ },
  { name: 'YouTube short', pattern: /youtu\.be/ },
  { name: 'TikTok video', pattern: /tiktok\.com.*\/video/ },
  { name: 'TikTok vm', pattern: /vm\.tiktok\.com/ },
  { name: 'TikTok t', pattern: /tiktok\.com\/t\// }
];

let smartTaskFormMatch = false;
smartTaskFormPatterns.forEach(({ name, pattern }) => {
  const match = pattern.test(testUrl);
  if (match) {
    console.log(`✅ ${name}: MATCH`);
    smartTaskFormMatch = true;
  } else {
    console.log(`❌ ${name}: no match`);
  }
});

console.log(`\nSmartTaskForm Overall Result: ${smartTaskFormMatch ? '✅ VALID' : '❌ INVALID'}`);

// Test 2: Frontend VideoService patterns
console.log('\n📱 Test 2: Frontend VideoService Patterns');
console.log('-'.repeat(40));
const videoServicePatterns = [
  { name: 'Bilibili BV (old)', pattern: /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(BV[A-Za-z0-9]+)/ },
  { name: 'Bilibili BV (new)', pattern: /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(BV[A-Za-z0-9]+)(?:\/|\?|$)/ },
  { name: 'Bilibili AV (old)', pattern: /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(av\d+)/ },
  { name: 'Bilibili AV (new)', pattern: /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(av\d+)(?:\/|\?|$)/ },
  { name: 'Bilibili b23', pattern: /(?:https?:\/\/)?b23\.tv\/([A-Za-z0-9]+)/ }
];

let videoServiceMatch = false;
let extractedVideoId = null;
videoServicePatterns.forEach(({ name, pattern }) => {
  const match = testUrl.match(pattern);
  if (match) {
    console.log(`✅ ${name}: MATCH - Video ID: ${match[1]}`);
    videoServiceMatch = true;
    if (!extractedVideoId) extractedVideoId = match[1];
  } else {
    console.log(`❌ ${name}: no match`);
  }
});

console.log(`\nVideoService Overall Result: ${videoServiceMatch ? '✅ VALID' : '❌ INVALID'}`);
if (extractedVideoId) {
  console.log(`Extracted Video ID: ${extractedVideoId}`);
}

// Test 3: Backend Platform Identifier simulation
console.log('\n🔧 Test 3: Backend Platform Identifier Simulation');
console.log('-'.repeat(40));

// Simulate the backend logic
function simulateBackendIdentifier(url) {
  // Clean URL
  const cleanUrl = url.trim().replace(/[<>"'`]/g, '');
  
  // Extract URL
  const urlRegex = /(https?:\/\/[^\s]+|(?:www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}[^\s]*)/;
  const match = cleanUrl.match(urlRegex);
  
  if (!match) return 'Unknown';
  
  let extractedUrl = match[0];
  if (!extractedUrl.startsWith('http://') && !extractedUrl.startsWith('https://')) {
    extractedUrl = 'https://' + extractedUrl;
  }
  
  try {
    const parsedUrl = new URL(extractedUrl);
    const host = parsedUrl.hostname.toLowerCase().replace(/^www\./, '');
    const fullPath = parsedUrl.pathname + parsedUrl.search + parsedUrl.hash;
    
    console.log(`Host: ${host}`);
    console.log(`Path: ${fullPath}`);
    
    // Check bilibili
    if (host === 'bilibili.com' || host.endsWith('.bilibili.com')) {
      if (fullPath.includes('/video/')) {
        return 'bilibili';
      }
    }
    
    if (host === 'b23.tv') {
      return 'bilibili';
    }
    
    return 'Unknown';
  } catch (error) {
    return 'Unknown';
  }
}

const backendResult = simulateBackendIdentifier(testUrl);
console.log(`Backend Platform Identifier Result: ${backendResult === 'bilibili' ? '✅ bilibili' : '❌ ' + backendResult}`);

// Summary
console.log('\n📊 SUMMARY');
console.log('='.repeat(60));
console.log(`SmartTaskForm Validation: ${smartTaskFormMatch ? '✅ PASS' : '❌ FAIL'}`);
console.log(`VideoService Recognition: ${videoServiceMatch ? '✅ PASS' : '❌ FAIL'}`);
console.log(`Backend Platform ID: ${backendResult === 'bilibili' ? '✅ PASS' : '❌ FAIL'}`);

if (smartTaskFormMatch && videoServiceMatch && backendResult === 'bilibili') {
  console.log('\n🎉 ALL TESTS PASSED! The URL should be recognized correctly.');
  console.log('If the user is still experiencing issues, the problem might be:');
  console.log('1. Browser cache issues');
  console.log('2. Different URL format than tested');
  console.log('3. JavaScript errors in the browser');
  console.log('4. Network connectivity issues');
} else {
  console.log('\n⚠️  SOME TESTS FAILED! This indicates a real issue.');
}
