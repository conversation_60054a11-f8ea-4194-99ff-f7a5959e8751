<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>改进的检测机制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 13px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
        .detection-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 改进的任务检测机制测试</h1>
        <p>测试新的智能检测系统，包括跨域检测、智能推断和用户行为分析。</p>
        
        <div class="detection-stats">
            <div class="stat-card">
                <div class="stat-value" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="successfulDetections">0</div>
                <div class="stat-label">成功检测</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="averageConfidence">0%</div>
                <div class="stat-label">平均置信度</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="crossDomainTests">0</div>
                <div class="stat-label">跨域测试</div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>🧪 检测机制测试</h2>
        
        <div class="test-section">
            <h3>智能推断测试</h3>
            <p>测试基于用户行为模式的智能推断功能</p>
            
            <button onclick="testIntelligentDetection('bilibili', 'like', 15000)">B站点赞检测 (15秒)</button>
            <button onclick="testIntelligentDetection('douyin', 'share', 25000)">抖音分享检测 (25秒)</button>
            <button onclick="testIntelligentDetection('bilibili', 'like', 3000)">快速完成测试 (3秒)</button>
            <button onclick="testIntelligentDetection('bilibili', 'like', 180000)">长时间测试 (3分钟)</button>
        </div>

        <div class="test-section">
            <h3>跨域检测测试</h3>
            <p>测试跨域场景下的检测能力</p>
            
            <button onclick="testCrossDomainDetection('bilibili')">B站跨域检测</button>
            <button onclick="testCrossDomainDetection('douyin')">抖音跨域检测</button>
            <button onclick="testMultiTaskDetection()">多任务检测</button>
            <button onclick="testDetectionFailure()">检测失败场景</button>
        </div>

        <div class="test-section">
            <h3>实时监控测试</h3>
            <p>测试窗口状态监控和用户行为分析</p>
            
            <button onclick="startRealTimeTest()" id="realTimeBtn">开始实时监控测试</button>
            <button onclick="stopRealTimeTest()" disabled id="stopBtn">停止测试</button>
            <button onclick="simulateUserActivity()">模拟用户活动</button>
        </div>

        <div id="testResults"></div>
        <div id="testLogs" class="log"></div>
    </div>

    <script>
        // 测试统计
        let testStats = {
            total: 0,
            successful: 0,
            totalConfidence: 0,
            crossDomain: 0
        };

        let realTimeTestActive = false;
        let realTimeInterval = null;

        // 模拟改进的检测服务
        const mockImprovedDetectionService = {
            // 智能推断检测
            performIntelligentDetection(platform, taskType, timeSpent, userActivity) {
                const confidence = this.calculateIntelligentConfidence(platform, taskType, timeSpent, userActivity);
                
                return {
                    detectedActions: confidence > 50 ? [{
                        action: taskType,
                        platform: platform,
                        timestamp: Date.now(),
                        confidence: confidence,
                        method: 'intelligent_inference'
                    }] : [],
                    confidence: confidence,
                    method: 'intelligent_cross_domain',
                    indicators: {
                        timeSpent: timeSpent,
                        userActivity: userActivity,
                        platformOptimized: true
                    }
                };
            },

            // 计算智能推断置信度
            calculateIntelligentConfidence(platform, taskType, timeSpent, userActivity) {
                let confidence = 0;
                
                // 时间因子
                if (timeSpent > 10000) confidence += 30;
                if (timeSpent > 20000) confidence += 20;
                if (timeSpent < 5000) confidence -= 40;
                if (timeSpent > 300000) confidence -= 30;
                
                // 用户活动因子
                if (userActivity.focusTime > 5000) confidence += 25;
                if (userActivity.interactions > 3) confidence += 20;
                if (userActivity.windowChanges > 0) confidence += 15;
                
                // 平台特定调整
                const platformBonus = {
                    'bilibili': 10,
                    'douyin': 15,
                    'kuaishou': 12,
                    'xiaohongshu': 8
                };
                confidence += platformBonus[platform] || 0;
                
                // 任务类型调整
                const taskBonus = {
                    'like': 15,
                    'share': 10,
                    'follow': 5,
                    'comment': 0
                };
                confidence += taskBonus[taskType] || 0;
                
                return Math.max(0, Math.min(90, confidence));
            },

            // 跨域检测
            performCrossDomainDetection(platform, timeSpent) {
                const baseConfidence = timeSpent > 15000 ? 60 : 30;
                const randomFactor = Math.random() * 20 - 10; // ±10%
                
                return {
                    detectedActions: [{
                        action: 'like',
                        platform: platform,
                        timestamp: Date.now(),
                        confidence: Math.max(0, baseConfidence + randomFactor),
                        method: 'cross_domain_inference'
                    }],
                    confidence: Math.max(0, baseConfidence + randomFactor),
                    method: 'intelligent_cross_domain'
                };
            }
        };

        // 测试函数
        function testIntelligentDetection(platform, taskType, timeSpent) {
            log(`🧠 开始智能推断测试 - 平台: ${platform}, 任务: ${taskType}, 时间: ${timeSpent/1000}秒`);
            
            const userActivity = {
                focusTime: Math.random() * timeSpent * 0.8,
                interactions: Math.floor(Math.random() * 10) + 1,
                windowChanges: Math.floor(Math.random() * 3)
            };
            
            const result = mockImprovedDetectionService.performIntelligentDetection(
                platform, taskType, timeSpent, userActivity
            );
            
            updateStats(result);
            displayResult(`智能推断测试 - ${platform} ${taskType}`, result, {
                timeSpent: timeSpent,
                userActivity: userActivity
            });
        }

        function testCrossDomainDetection(platform) {
            log(`🌐 开始跨域检测测试 - 平台: ${platform}`);
            
            const timeSpent = 15000 + Math.random() * 30000; // 15-45秒
            const result = mockImprovedDetectionService.performCrossDomainDetection(platform, timeSpent);
            
            testStats.crossDomain++;
            updateStats(result);
            displayResult(`跨域检测测试 - ${platform}`, result, { timeSpent: timeSpent });
        }

        function testMultiTaskDetection() {
            log(`🎯 开始多任务检测测试`);
            
            const tasks = ['like', 'share'];
            const results = [];
            
            tasks.forEach(task => {
                const timeSpent = 20000 + Math.random() * 15000;
                const userActivity = {
                    focusTime: timeSpent * 0.7,
                    interactions: 5 + Math.floor(Math.random() * 5),
                    windowChanges: 2
                };
                
                const result = mockImprovedDetectionService.performIntelligentDetection(
                    'bilibili', task, timeSpent, userActivity
                );
                results.push(result);
            });
            
            const combinedResult = {
                detectedActions: results.flatMap(r => r.detectedActions),
                confidence: results.reduce((sum, r) => sum + r.confidence, 0) / results.length,
                method: 'multi_task_intelligent'
            };
            
            updateStats(combinedResult);
            displayResult('多任务检测测试', combinedResult, { tasks: tasks });
        }

        function testDetectionFailure() {
            log(`❌ 开始检测失败场景测试`);
            
            const result = {
                detectedActions: [],
                confidence: 15,
                method: 'intelligent_cross_domain',
                failureReason: '用户活动不足，无法确认操作完成'
            };
            
            updateStats(result);
            displayResult('检测失败场景测试', result, { scenario: 'insufficient_activity' });
        }

        function startRealTimeTest() {
            if (realTimeTestActive) return;
            
            realTimeTestActive = true;
            document.getElementById('realTimeBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            
            log(`🔄 开始实时监控测试`);
            
            let testDuration = 0;
            realTimeInterval = setInterval(() => {
                testDuration += 1000;
                
                if (testDuration % 5000 === 0) {
                    const activity = {
                        focusTime: testDuration * 0.6,
                        interactions: Math.floor(testDuration / 2000),
                        windowChanges: Math.floor(testDuration / 10000)
                    };
                    
                    log(`📊 实时状态 - 时长: ${testDuration/1000}秒, 交互: ${activity.interactions}次`);
                }
                
                if (testDuration >= 30000) {
                    stopRealTimeTest();
                }
            }, 1000);
        }

        function stopRealTimeTest() {
            if (!realTimeTestActive) return;
            
            realTimeTestActive = false;
            document.getElementById('realTimeBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
            if (realTimeInterval) {
                clearInterval(realTimeInterval);
                realTimeInterval = null;
            }
            
            log(`✅ 实时监控测试完成`);
        }

        function simulateUserActivity() {
            log(`👆 模拟用户活动 - 点击、滚动、焦点变化`);
            
            // 模拟检测到的用户活动
            const activity = {
                clicks: Math.floor(Math.random() * 5) + 1,
                scrolls: Math.floor(Math.random() * 10) + 2,
                focusChanges: Math.floor(Math.random() * 3) + 1
            };
            
            log(`   检测到: ${activity.clicks}次点击, ${activity.scrolls}次滚动, ${activity.focusChanges}次焦点变化`);
        }

        function updateStats(result) {
            testStats.total++;
            if (result.confidence > 50) testStats.successful++;
            testStats.totalConfidence += result.confidence;
            
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('successfulDetections').textContent = testStats.successful;
            document.getElementById('averageConfidence').textContent = 
                Math.round(testStats.totalConfidence / testStats.total) + '%';
            document.getElementById('crossDomainTests').textContent = testStats.crossDomain;
        }

        function displayResult(testName, result, context = {}) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            
            let statusClass = 'info';
            let statusIcon = '🔍';
            
            if (result.confidence >= 70) {
                statusClass = 'success';
                statusIcon = '✅';
            } else if (result.confidence >= 50) {
                statusClass = 'warning';
                statusIcon = '⚠️';
            } else {
                statusClass = 'error';
                statusIcon = '❌';
            }
            
            resultDiv.className = `test-result ${statusClass}`;
            
            const detectedActions = result.detectedActions || [];
            const actionsList = detectedActions.length > 0 
                ? detectedActions.map(a => `${a.action} (${a.confidence}%)`).join(', ')
                : '无';
            
            resultDiv.innerHTML = `
                <h4>${statusIcon} ${testName}</h4>
                <p><strong>检测方法:</strong> ${result.method}</p>
                <p><strong>整体置信度:</strong> ${result.confidence}%</p>
                <p><strong>检测到的操作:</strong> ${actionsList}</p>
                <p><strong>检测结果:</strong> ${result.confidence > 50 ? '成功' : '失败'}</p>
                ${result.failureReason ? `<p><strong>失败原因:</strong> ${result.failureReason}</p>` : ''}
                ${context.timeSpent ? `<p><strong>执行时间:</strong> ${Math.round(context.timeSpent/1000)}秒</p>` : ''}
                ${context.userActivity ? `<p><strong>用户活动:</strong> 焦点时间${Math.round(context.userActivity.focusTime/1000)}秒, 交互${context.userActivity.interactions}次</p>` : ''}
            `;
            
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function log(message) {
            const logsDiv = document.getElementById('testLogs');
            const timestamp = new Date().toLocaleTimeString();
            logsDiv.textContent += `[${timestamp}] ${message}\n`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        // 初始化
        log('🚀 改进的检测机制测试页面已加载');
        log('📝 新功能包括: 智能推断、跨域检测、实时监控、用户行为分析');
        log('🎯 点击按钮开始测试各种检测场景');
    </script>
</body>
</html>
