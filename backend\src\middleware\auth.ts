import { Request, Response, NextFunction } from 'express';
import { verifyToken, extractTokenFromHeader } from '@/utils/jwt';
import { UserModel } from '@/models/User';
import { ApiResponse } from '@/types';

/**
 * JWT认证中间件
 */
export const authenticateToken = async (
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      res.status(401).json({
        success: false,
        error: '未提供访问令牌',
      });
      return;
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      res.status(401).json({
        success: false,
        error: '无效的访问令牌',
      });
      return;
    }

    // 验证用户是否存在且处于活跃状态
    const user = await UserModel.findById(decoded.userId);
    if (!user || !user.is_active) {
      res.status(401).json({
        success: false,
        error: '用户不存在或已被禁用',
      });
      return;
    }

    // 将用户信息添加到请求对象
    req.user = decoded;
    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '认证过程中发生错误',
    });
  }
};

/**
 * 可选认证中间件（不强制要求登录）
 */
export const optionalAuth = async (
  req: Request,
  _res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);

    if (token) {
      const decoded = verifyToken(token);
      if (decoded) {
        const user = await UserModel.findById(decoded.userId);
        if (user && user.is_active) {
          req.user = decoded;
        }
      }
    }

    next();
  } catch (error) {
    // 可选认证失败时不阻止请求继续
    next();
  }
};

/**
 * 检查用户是否已验证邮箱
 */
export const requireEmailVerification = async (
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: '需要登录',
      });
      return;
    }

    const user = await UserModel.findById(req.user.userId);
    if (!user || !user.email_verified) {
      res.status(403).json({
        success: false,
        error: '请先验证邮箱',
      });
      return;
    }

    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '验证邮箱状态时发生错误',
    });
  }
};

/**
 * 检查用户权限（管理员）
 */
export const requireAdmin = async (
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: '需要登录',
      });
      return;
    }

    const user = await UserModel.findById(req.user.userId);
    if (!user) {
      res.status(401).json({
        success: false,
        error: '用户不存在',
      });
      return;
    }

    // 这里可以添加管理员权限检查逻辑
    // 例如检查用户角色或特定权限
    // if (user.role !== 'admin') {
    //   res.status(403).json({
    //     success: false,
    //     error: '需要管理员权限',
    //   });
    //   return;
    // }

    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '权限验证时发生错误',
    });
  }
};
