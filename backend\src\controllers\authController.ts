import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { ApiResponse, LoginRequest, RegisterRequest } from '@/types';
import { UserModel } from '@/models/User';
import { generateTokens } from '@/utils/jwt';
// 移除未使用的导入

/**
 * 用户注册
 */
export const register = asyncHandler(async (
  req: Request<{}, ApiResponse, RegisterRequest>,
  res: Response<ApiResponse>
): Promise<void> => {
  const { username, email, password } = req.body;

  // 验证输入
  if (!username || !email || !password) {
    res.status(400).json({
      success: false,
      error: '用户名、邮箱和密码都是必填项'
    });
    return;
  }

  // 检查用户是否已存在
  const existingUserByEmail = await UserModel.findByEmail(email);
  if (existingUserByEmail) {
    res.status(409).json({
      success: false,
      error: '该邮箱已被注册'
    });
    return;
  }

  const existingUserByUsername = await UserModel.findByUsername(username);
  if (existingUserByUsername) {
    res.status(409).json({
      success: false,
      error: '该用户名已被使用'
    });
    return;
  }

  // 创建新用户
  const newUser = await UserModel.create({
    username,
    email,
    password,
    points_balance: 100
  });

  // 生成JWT tokens
  const tokens = generateTokens({ userId: newUser.id, username: newUser.username, email: newUser.email });

  res.status(201).json({
    success: true,
    message: '注册成功',
    data: {
      user: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        points_balance: newUser.points_balance,
        email_verified: newUser.email_verified,
        social_accounts: newUser.social_accounts,
        created_at: newUser.created_at
      },
      tokens
    }
  });
});

/**
 * 用户登录
 */
export const login = asyncHandler(async (
  req: Request<{}, ApiResponse, LoginRequest>,
  res: Response<ApiResponse>
): Promise<void> => {
  const { email, password } = req.body;

  // 验证输入
  if (!email || !password) {
    res.status(400).json({
      success: false,
      error: '邮箱和密码都是必填项'
    });
    return;
  }

  // 查找用户（支持邮箱登录）
  const user = await UserModel.findByEmail(email);

  if (!user) {
    res.status(401).json({
      success: false,
      error: '用户名或密码错误'
    });
    return;
  }

  // 验证密码
  const isPasswordValid = await UserModel.verifyPassword(password, user.password_hash);
  if (!isPasswordValid) {
    res.status(401).json({
      success: false,
      error: '用户名或密码错误'
    });
    return;
  }

  // 检查用户是否激活
  if (!user.is_active) {
    res.status(403).json({
      success: false,
      error: '账户已被禁用，请联系管理员'
    });
    return;
  }

  // 生成JWT tokens
  const tokens = generateTokens({ userId: user.id, username: user.username, email: user.email });

  // 更新最后登录时间
  await UserModel.update(user.id, {
    last_login_at: new Date(),
    last_login_ip: req.ip || ''
  } as any);

  res.json({
    success: true,
    message: '登录成功',
    data: {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        points_balance: user.points_balance,
        email_verified: user.email_verified,
        social_accounts: user.social_accounts,
        role: (user as any).role || 'user',
        created_at: user.created_at
      },
      tokens
    }
  });
});

/**
 * 获取当前用户信息
 */
export const getCurrentUser = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;

  if (!userId) {
    res.status(401).json({
      success: false,
      error: '未授权访问'
    });
    return;
  }

  const user = await UserModel.findById(userId);
  if (!user) {
    res.status(404).json({
      success: false,
      error: '用户不存在'
    });
    return;
  }

  res.json({
    success: true,
    data: {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        points_balance: user.points_balance,
        email_verified: user.email_verified,
        social_accounts: user.social_accounts,
        role: (user as any).role || 'user',
        created_at: user.created_at
      }
    }
  });
});

/**
 * 更新用户信息
 */
export const updateProfile = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式，请访问 /api/demo/user 查看演示数据'
  });
});

/**
 * 请求密码重置
 */
export const requestPasswordReset = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});

/**
 * 重置密码
 */
export const resetPassword = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});

/**
 * 验证邮箱
 */
export const verifyEmail = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});

/**
 * 绑定社交账号
 */
export const bindSocialAccount = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});

/**
 * 解绑社交账号
 */
export const unbindSocialAccount = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});
