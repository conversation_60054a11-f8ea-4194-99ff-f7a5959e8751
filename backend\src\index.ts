import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

// 导入配置和中间件
import { globalErrorHandler, notFoundHandler } from '@/middleware/errorHandler';
import { generalLimiter } from '@/middleware/rateLimiter';
import logger from '@/utils/logger';

// 导入路由
import authRoutes from '@/routes/auth';
import tasksRoutes from '@/routes/tasks';
import pointsRoutes from '@/routes/points';
import utilsRoutes from '@/routes/utils';
import videoRoutes from '@/routes/video';

const app = express();
const PORT = process.env['PORT'] || 3000;
const HOST = process.env['HOST'] || 'localhost';

// Swagger配置
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '互助点赞平台 API',
      version: '1.0.0',
      description: '基于积分的社交媒体互助系统 API 文档',
    },
    servers: [
      {
        url: `http://${HOST}:${PORT}/api`,
        description: '开发服务器',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
  apis: ['./src/routes/*.ts'], // API文档路径
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

// 基础中间件
app.use(helmet());
app.use(compression());
app.use(morgan('dev'));

// CORS配置
const allowedOrigins = [
  'http://localhost:5173',
  'http://localhost:5174',
  ...(process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : [])
];

app.use(cors({
  origin: (origin, callback) => {
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// 解析请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 限流
app.use(generalLimiter);

// 健康检查
app.get('/health', (_req, res) => {
  res.json({
    success: true,
    message: '服务运行正常',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// API文档
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/points', pointsRoutes);
app.use('/api/tasks', tasksRoutes);
app.use('/api/utils', utilsRoutes);
app.use('/api/video', videoRoutes);

// 404处理
app.use(notFoundHandler);

// 全局错误处理
app.use(globalErrorHandler);

// 启动服务器
const startServer = async () => {
  try {
    app.listen(PORT, () => {
      logger.info(`🚀 服务器启动成功`);
      logger.info(`📍 地址: http://${HOST}:${PORT}`);
      logger.info(`📚 API文档: http://${HOST}:${PORT}/api-docs`);
      logger.info(`🏥 健康检查: http://${HOST}:${PORT}/health`);
      logger.info(`🌍 环境: ${process.env['NODE_ENV'] || 'development'}`);
    });
  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
};

startServer();

export default app;
