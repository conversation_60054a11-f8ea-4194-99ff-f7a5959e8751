const bcrypt = require('bcryptjs');
const knex = require('knex');
const config = require('./knexfile');

// 使用开发环境配置
const db = knex(config.development);

async function createAdminUser() {
  try {
    console.log('正在连接数据库...');
    
    // 测试数据库连接
    await db.raw('SELECT 1');
    console.log('数据库连接成功');
    
    // 检查用户是否已存在
    const existingUser = await db('users').where('username', 'lozehq').first();
    
    if (existingUser) {
      console.log('管理员用户 lozehq 已存在');
      console.log('用户信息:', {
        id: existingUser.id,
        username: existingUser.username,
        email: existingUser.email,
        points_balance: existingUser.points_balance,
        is_active: existingUser.is_active,
        email_verified: existingUser.email_verified
      });
      return;
    }

    console.log('正在创建管理员用户...');
    
    // 加密密码
    const hashedPassword = await bcrypt.hash('Hwb123123', 12);
    
    // 创建管理员用户
    const [newUser] = await db('users').insert({
      username: 'lozehq',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      points_balance: 10000,
      social_accounts: JSON.stringify({}),
      is_active: true,
      email_verified: true,
      created_at: new Date(),
      updated_at: new Date(),
    }).returning('*');

    console.log('管理员用户创建成功!');
    console.log('用户信息:', {
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      points_balance: newUser.points_balance
    });
    
  } catch (error) {
    console.error('创建管理员用户失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n数据库连接被拒绝，请确保PostgreSQL数据库正在运行。');
      console.log('你可以尝试以下方法启动数据库:');
      console.log('1. 如果安装了PostgreSQL服务: net start postgresql-x64-16');
      console.log('2. 如果使用Docker: docker run --name postgres -e POSTGRES_PASSWORD=postgres123 -e POSTGRES_DB=mutual_like_platform -p 5432:5432 -d postgres:13');
      console.log('3. 或者手动启动PostgreSQL服务');
    }
  } finally {
    await db.destroy();
  }
}

// 运行脚本
createAdminUser();