import React, { useState, useEffect } from 'react';
import { TaskType } from '@/types';

interface CrossDomainTaskGuideProps {
  sessionId: string;
  platform: string;
  selectedRequirements: TaskType[];
  onUserConfirm: (completed: boolean) => void;
  isVisible: boolean;
}

const CrossDomainTaskGuide: React.FC<CrossDomainTaskGuideProps> = ({
  sessionId,
  platform,
  selectedRequirements,
  onUserConfirm,
  isVisible
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime] = useState(Date.now());
  const [userActions, setUserActions] = useState<Set<TaskType>>(new Set());

  const taskInstructions = {
    like: {
      icon: '👍',
      name: '点赞',
      steps: [
        '找到视频下方的点赞按钮（通常是一个拇指向上的图标）',
        '点击点赞按钮',
        '确认按钮变色或点赞数增加'
      ]
    },
    share: {
      icon: '📤',
      name: '分享',
      steps: [
        '找到视频下方的分享按钮（通常是一个箭头或分享图标）',
        '点击分享按钮',
        '选择分享方式（微信、QQ、微博等）',
        '完成分享操作'
      ]
    },
    comment: {
      icon: '💬',
      name: '评论',
      steps: [
        '滚动到评论区域',
        '点击评论输入框',
        '输入有意义的评论内容',
        '点击发送按钮'
      ]
    },
    follow: {
      icon: '➕',
      name: '关注',
      steps: [
        '找到用户头像或关注按钮',
        '点击关注按钮',
        '确认按钮变为"已关注"状态'
      ]
    }
  };

  const platformGuides = {
    bilibili: {
      name: 'B站',
      tips: [
        '点赞按钮在视频下方工具栏',
        '分享按钮通常在点赞按钮旁边',
        '评论区在视频下方',
        '关注按钮在UP主头像旁边'
      ]
    },
    douyin: {
      name: '抖音',
      tips: [
        '点赞按钮在视频右侧',
        '分享按钮在点赞按钮下方',
        '评论按钮在分享按钮下方',
        '关注按钮在用户头像上方'
      ]
    },
    kuaishou: {
      name: '快手',
      tips: [
        '点赞按钮在视频右下角',
        '分享按钮在点赞按钮旁边',
        '评论区在视频下方',
        '关注按钮在用户名旁边'
      ]
    }
  };

  useEffect(() => {
    if (!isVisible) return;

    const timer = setInterval(() => {
      setElapsedTime(Date.now() - startTime);
    }, 1000);

    return () => clearInterval(timer);
  }, [isVisible, startTime]);

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    return `${Math.floor(seconds / 60)}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  const handleTaskComplete = (taskType: TaskType) => {
    setUserActions(prev => new Set([...prev, taskType]));
  };

  const handleConfirmCompletion = () => {
    const allCompleted = selectedRequirements.every(req => userActions.has(req));
    onUserConfirm(allCompleted);
  };

  const currentTask = selectedRequirements[currentStep];
  const taskInfo = currentTask ? taskInstructions[currentTask] : null;
  const platformInfo = platformGuides[platform as keyof typeof platformGuides];

  if (!isVisible || !taskInfo) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="bg-blue-500 text-white p-4 rounded-t-lg">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">
              🎯 任务执行指南 - {platformInfo?.name || platform}
            </h3>
            <div className="text-sm bg-blue-600 px-2 py-1 rounded">
              {formatTime(elapsedTime)}
            </div>
          </div>
          <div className="mt-2 text-sm opacity-90">
            进度: {currentStep + 1} / {selectedRequirements.length}
          </div>
        </div>

        {/* 当前任务 */}
        <div className="p-4">
          <div className="flex items-center mb-4">
            <span className="text-3xl mr-3">{taskInfo.icon}</span>
            <div>
              <h4 className="text-xl font-semibold text-gray-800">
                {taskInfo.name}任务
              </h4>
              <p className="text-gray-600 text-sm">
                请按照以下步骤完成操作
              </p>
            </div>
          </div>

          {/* 操作步骤 */}
          <div className="space-y-3 mb-4">
            {taskInfo.steps.map((step, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold mr-3 mt-0.5">
                  {index + 1}
                </div>
                <p className="text-gray-700 text-sm leading-relaxed">{step}</p>
              </div>
            ))}
          </div>

          {/* 平台特定提示 */}
          {platformInfo && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
              <h5 className="text-sm font-semibold text-yellow-800 mb-2">
                💡 {platformInfo.name}平台提示
              </h5>
              <ul className="text-xs text-yellow-700 space-y-1">
                {platformInfo.tips.map((tip, index) => (
                  <li key={index}>• {tip}</li>
                ))}
              </ul>
            </div>
          )}

          {/* 任务状态 */}
          <div className="space-y-2 mb-4">
            <h5 className="text-sm font-semibold text-gray-700">任务完成状态:</h5>
            {selectedRequirements.map((req) => (
              <div key={req} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {taskInstructions[req].icon} {taskInstructions[req].name}
                </span>
                <div className="flex items-center">
                  {userActions.has(req) ? (
                    <span className="text-green-600 text-sm">✅ 已完成</span>
                  ) : (
                    <button
                      onClick={() => handleTaskComplete(req)}
                      className="text-blue-600 text-sm hover:text-blue-800"
                    >
                      标记完成
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            {currentStep < selectedRequirements.length - 1 ? (
              <button
                onClick={() => setCurrentStep(currentStep + 1)}
                className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
              >
                下一个任务
              </button>
            ) : (
              <button
                onClick={handleConfirmCompletion}
                className="flex-1 bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600 transition-colors"
              >
                确认完成所有任务
              </button>
            )}
            
            <button
              onClick={() => onUserConfirm(false)}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              取消
            </button>
          </div>

          {/* 底部提示 */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-xs text-gray-600 text-center">
              💡 完成操作后，请确认操作成功（如按钮变色、数字增加等），然后标记为完成
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CrossDomainTaskGuide;
