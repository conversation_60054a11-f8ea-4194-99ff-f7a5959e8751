// 模拟后端视频解析API
import { VideoInfo, VideoPlatform } from './videoService';

/**
 * 模拟抖音API响应
 */
const mockDouyinAPI = async (videoId: string): Promise<VideoInfo> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 800));
  
  return {
    platform: VideoPlatform.DOUYIN,
    videoId,
    originalUrl: `https://www.douyin.com/video/${videoId}`,
    title: '🔥超火抖音视频！这个技巧太实用了 #生活小妙招 #必看',
    cover: 'https://picsum.photos/400/600?random=douyin',
    author: '生活达人小王',
    description: '分享一个超实用的生活小技巧，学会了能省不少事！记得点赞收藏哦～',
    duration: 15
  };
};

/**
 * 模拟快手API响应
 */
const mockKuaishouAPI = async (videoId: string): Promise<VideoInfo> => {
  await new Promise(resolve => setTimeout(resolve, 600));
  
  return {
    platform: VideoPlatform.KUAISHOU,
    videoId,
    originalUrl: `https://www.kuaishou.com/short-video/${videoId}`,
    title: '快手热门：记录美好生活的每一刻 ✨',
    cover: 'https://picsum.photos/400/600?random=kuaishou',
    author: '快手老铁',
    description: '用快手记录生活中的美好瞬间，每一帧都是回忆！',
    duration: 30
  };
};

/**
 * 模拟小红书API响应
 */
const mockXiaohongshuAPI = async (videoId: string): Promise<VideoInfo> => {
  await new Promise(resolve => setTimeout(resolve, 1200));
  
  return {
    platform: VideoPlatform.XIAOHONGSHU,
    videoId,
    originalUrl: `https://www.xiaohongshu.com/explore/${videoId}`,
    title: '小红书种草 | 这个好物真的太好用了！强烈推荐 💕',
    cover: 'https://picsum.photos/400/600?random=xiaohongshu',
    author: '种草博主',
    description: '姐妹们！这个宝藏好物我必须要分享给大家，真的超级好用，性价比超高！',
    duration: 45
  };
};

/**
 * 模拟B站API响应
 */
const mockBilibiliAPI = async (videoId: string): Promise<VideoInfo> => {
  await new Promise(resolve => setTimeout(resolve, 900));
  
  return {
    platform: VideoPlatform.BILIBILI,
    videoId,
    originalUrl: `https://www.bilibili.com/video/${videoId}`,
    title: '【技术分享】前端开发必看！这些技巧让你效率翻倍',
    cover: 'https://picsum.photos/400/600?random=bilibili',
    author: '技术UP主',
    description: '分享一些前端开发的实用技巧和最佳实践，适合初学者和进阶开发者观看。',
    duration: 600
  };
};

/**
 * 根据平台调用对应的模拟API
 */
export const mockVideoParseAPI = async (platform: VideoPlatform, videoId: string): Promise<VideoInfo> => {
  switch (platform) {
    case VideoPlatform.DOUYIN:
      return mockDouyinAPI(videoId);
    case VideoPlatform.KUAISHOU:
      return mockKuaishouAPI(videoId);
    case VideoPlatform.XIAOHONGSHU:
      return mockXiaohongshuAPI(videoId);
    case VideoPlatform.BILIBILI:
      return mockBilibiliAPI(videoId);
    default:
      throw new Error('不支持的平台');
  }
};

/**
 * 模拟任务提交API
 */
export const mockSubmitTaskAPI = async (taskData: any): Promise<{ success: boolean; taskId: string }> => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 模拟成功响应
  return {
    success: true,
    taskId: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  };
};

/**
 * 模拟获取任务列表API
 */
export const mockGetTasksAPI = async (): Promise<any[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return [
    {
      id: 'task_1',
      title: '抖音视频点赞任务',
      platform: VideoPlatform.DOUYIN,
      taskType: 'like',
      status: 'active',
      reward: 10,
      completedCount: 25,
      totalCount: 100,
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 'task_2',
      title: '快手视频分享任务',
      platform: VideoPlatform.KUAISHOU,
      taskType: 'share',
      status: 'completed',
      reward: 15,
      completedCount: 50,
      totalCount: 50,
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 'task_3',
      title: '小红书笔记评论任务',
      platform: VideoPlatform.XIAOHONGSHU,
      taskType: 'comment',
      status: 'active',
      reward: 20,
      completedCount: 8,
      totalCount: 30,
      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
    }
  ];
};

/**
 * 真实API集成示例（需要后端支持）
 */
export const realVideoParseAPI = async (url: string, platform: VideoPlatform, videoId: string): Promise<VideoInfo> => {
  // 这里应该调用真实的后端API
  const response = await fetch('/api/video/parse', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ url, platform, videoId })
  });
  
  if (!response.ok) {
    throw new Error('解析视频信息失败');
  }
  
  return response.json();
};

/**
 * 抖音开放平台API集成示例
 */
export const douyinOpenAPI = async (videoId: string): Promise<VideoInfo> => {
  // 抖音开放平台API调用示例
  // 需要先获取access_token，然后调用视频信息接口
  
  const accessToken = 'your_access_token'; // 需要通过OAuth获取
  
  const response = await fetch(`https://open.douyin.com/video/data/?access_token=${accessToken}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      item_ids: [videoId]
    })
  });
  
  if (!response.ok) {
    throw new Error('调用抖音API失败');
  }
  
  const data = await response.json();
  
  // 转换抖音API响应格式
  const videoData = data.data.list[0];
  
  return {
    platform: VideoPlatform.DOUYIN,
    videoId,
    originalUrl: `https://www.douyin.com/video/${videoId}`,
    title: videoData.title,
    cover: videoData.cover,
    author: videoData.author.nickname,
    description: videoData.desc,
    duration: videoData.duration
  };
};
