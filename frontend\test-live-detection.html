<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔴 实时检测器测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 2px solid #e9ecef;
        }

        .control-card h3 {
            margin-top: 0;
            color: #495057;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s ease;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }

        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }

        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }

        .status-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }

        .detection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .detection-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .detection-card h4 {
            margin-top: 0;
            color: #495057;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .metric-value {
            font-weight: bold;
            color: #007bff;
        }

        .log {
            background: #212529;
            color: #fff;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active { background: #28a745; }
        .status-inactive { background: #dc3545; }
        .status-warning { background: #ffc107; }

        .real-time-data {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .confidence-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 实时检测器测试系统</h1>

        <div class="status-panel">
            <h3>🚀 系统状态</h3>
            <div id="systemStatus">
                <span class="status-indicator status-inactive"></span>
                <span>检测器未启动</span>
            </div>
        </div>

        <div class="test-controls">
            <div class="control-card">
                <h3>🔌 扩展式检测器</h3>
                <button class="btn btn-success" onclick="startExtensionDetector()">启动扩展式检测</button>
                <button class="btn btn-warning" onclick="testExtensionFeatures()">功能测试</button>
                <button class="btn btn-danger" onclick="stopExtensionDetector()">停止检测</button>
            </div>

            <div class="control-card">
                <h3>🎯 真实检测器</h3>
                <button class="btn btn-success" onclick="startRealDetector()">启动真实检测</button>
                <button class="btn btn-warning" onclick="testRealFeatures()">功能测试</button>
                <button class="btn btn-danger" onclick="stopRealDetector()">停止检测</button>
            </div>

            <div class="control-card">
                <h3>🔀 混合检测器</h3>
                <button class="btn btn-success" onclick="startHybridDetector()">启动混合检测</button>
                <button class="btn btn-warning" onclick="testHybridFeatures()">功能测试</button>
                <button class="btn btn-danger" onclick="stopAllDetectors()">停止所有</button>
            </div>

            <div class="control-card">
                <h3>🧪 实际任务测试</h3>
                <button class="btn btn-success" onclick="openRealTask()">打开真实任务</button>
                <button class="btn btn-warning" onclick="simulateUserActions()">模拟用户操作</button>
                <button class="btn" onclick="checkDetectionResults()">检查检测结果</button>
            </div>
        </div>

        <div class="detection-grid">
            <div class="detection-card">
                <h4>🔌 扩展式检测数据</h4>
                <div class="metric">
                    <span>总点击数:</span>
                    <span class="metric-value" id="extensionClicks">0</span>
                </div>
                <div class="metric">
                    <span>置信度:</span>
                    <span class="metric-value" id="extensionConfidence">0%</span>
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill" id="extensionConfidenceBar" style="width: 0%"></div>
                </div>
                <div class="metric">
                    <span>检测状态:</span>
                    <span class="metric-value" id="extensionStatus">未启动</span>
                </div>
            </div>

            <div class="detection-card">
                <h4>🎯 真实检测数据</h4>
                <div class="metric">
                    <span>总点击数:</span>
                    <span class="metric-value" id="realClicks">0</span>
                </div>
                <div class="metric">
                    <span>置信度:</span>
                    <span class="metric-value" id="realConfidence">0%</span>
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill" id="realConfidenceBar" style="width: 0%"></div>
                </div>
                <div class="metric">
                    <span>检测状态:</span>
                    <span class="metric-value" id="realStatus">未启动</span>
                </div>
            </div>

            <div class="detection-card">
                <h4>📊 综合检测结果</h4>
                <div class="metric">
                    <span>最佳方法:</span>
                    <span class="metric-value" id="bestMethod">未确定</span>
                </div>
                <div class="metric">
                    <span>综合置信度:</span>
                    <span class="metric-value" id="overallConfidence">0%</span>
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill" id="overallConfidenceBar" style="width: 0%"></div>
                </div>
                <div class="metric">
                    <span>检测准确率:</span>
                    <span class="metric-value" id="detectionAccuracy">未知</span>
                </div>
            </div>
        </div>

        <div class="real-time-data">
            <h4>📡 实时检测数据流</h4>
            <div id="realTimeData">等待检测数据...</div>
        </div>

        <div class="log" id="testLog"></div>
    </div>

    <script>
        // 实时检测器测试系统
        class LiveDetectionTester {
            constructor() {
                this.isRunning = false;
                this.detectors = {
                    extension: null,
                    real: null,
                    hybrid: null
                };
                this.testWindow = null;
                this.updateInterval = null;
                this.sessionId = 'live-test-' + Date.now();
            }

            // 启动扩展式检测器
            async startExtensionDetector() {
                try {
                    log('🔌 启动扩展式检测器...');

                    // 模拟导入检测器
                    this.detectors.extension = {
                        clicks: { total: 0, byType: {}, targets: [] },
                        confidence: 0,
                        status: 'active'
                    };

                    updateUI('extensionStatus', '运行中');
                    updateSystemStatus('扩展式检测器已启动', 'active');

                    // 开始模拟检测数据
                    this.startExtensionSimulation();

                    log('✅ 扩展式检测器启动成功');
                } catch (error) {
                    log('❌ 扩展式检测器启动失败: ' + error.message);
                }
            }

            // 启动真实检测器
            async startRealDetector() {
                try {
                    log('🎯 启动真实检测器...');

                    this.detectors.real = {
                        totalClicks: 0,
                        confidence: 0,
                        windowActivity: { mouseActivity: 0 },
                        status: 'active'
                    };

                    updateUI('realStatus', '运行中');
                    updateSystemStatus('真实检测器已启动', 'active');

                    // 开始模拟检测数据
                    this.startRealSimulation();

                    log('✅ 真实检测器启动成功');
                } catch (error) {
                    log('❌ 真实检测器启动失败: ' + error.message);
                }
            }

            // 启动混合检测器
            async startHybridDetector() {
                log('🔀 启动混合检测器...');

                await this.startExtensionDetector();
                await this.startRealDetector();

                this.detectors.hybrid = {
                    status: 'active',
                    combinedConfidence: 0
                };

                updateSystemStatus('混合检测器已启动', 'active');
                log('✅ 混合检测器启动成功');
            }

            // 模拟扩展式检测数据
            startExtensionSimulation() {
                const simulate = () => {
                    if (!this.detectors.extension || this.detectors.extension.status !== 'active') return;

                    // 随机生成检测数据
                    if (Math.random() < 0.3) {
                        this.detectors.extension.clicks.total++;
                        this.detectors.extension.confidence = Math.min(
                            this.detectors.extension.confidence + Math.random() * 10,
                            95
                        );

                        updateUI('extensionClicks', this.detectors.extension.clicks.total);
                        updateUI('extensionConfidence', Math.round(this.detectors.extension.confidence) + '%');
                        updateConfidenceBar('extensionConfidenceBar', this.detectors.extension.confidence);

                        log(`🔌 扩展式检测: 点击 ${this.detectors.extension.clicks.total}, 置信度 ${Math.round(this.detectors.extension.confidence)}%`);
                    }

                    setTimeout(simulate, 2000 + Math.random() * 3000);
                };

                simulate();
            }

            // 模拟真实检测数据
            startRealSimulation() {
                const simulate = () => {
                    if (!this.detectors.real || this.detectors.real.status !== 'active') return;

                    // 随机生成检测数据
                    if (Math.random() < 0.25) {
                        this.detectors.real.totalClicks++;
                        this.detectors.real.windowActivity.mouseActivity += Math.floor(Math.random() * 3) + 1;
                        this.detectors.real.confidence = Math.min(
                            this.detectors.real.confidence + Math.random() * 8,
                            90
                        );

                        updateUI('realClicks', this.detectors.real.totalClicks);
                        updateUI('realConfidence', Math.round(this.detectors.real.confidence) + '%');
                        updateConfidenceBar('realConfidenceBar', this.detectors.real.confidence);

                        log(`🎯 真实检测: 点击 ${this.detectors.real.totalClicks}, 置信度 ${Math.round(this.detectors.real.confidence)}%`);
                    }

                    setTimeout(simulate, 3000 + Math.random() * 4000);
                };

                simulate();
            }

            // 测试扩展式功能
            testExtensionFeatures() {
                log('🧪 测试扩展式检测功能...');

                if (!this.detectors.extension) {
                    log('❌ 请先启动扩展式检测器');
                    return;
                }

                // 模拟快速检测
                for (let i = 0; i < 5; i++) {
                    setTimeout(() => {
                        this.detectors.extension.clicks.total++;
                        this.detectors.extension.confidence += 15;

                        updateUI('extensionClicks', this.detectors.extension.clicks.total);
                        updateUI('extensionConfidence', Math.round(this.detectors.extension.confidence) + '%');
                        updateConfidenceBar('extensionConfidenceBar', this.detectors.extension.confidence);

                        log(`🔌 功能测试: 模拟点击 ${i + 1}/5`);
                    }, i * 500);
                }

                setTimeout(() => {
                    log('✅ 扩展式检测功能测试完成');
                }, 3000);
            }

            // 测试真实检测功能
            testRealFeatures() {
                log('🧪 测试真实检测功能...');

                if (!this.detectors.real) {
                    log('❌ 请先启动真实检测器');
                    return;
                }

                // 模拟窗口活动
                for (let i = 0; i < 3; i++) {
                    setTimeout(() => {
                        this.detectors.real.totalClicks++;
                        this.detectors.real.windowActivity.mouseActivity += 2;
                        this.detectors.real.confidence += 20;

                        updateUI('realClicks', this.detectors.real.totalClicks);
                        updateUI('realConfidence', Math.round(this.detectors.real.confidence) + '%');
                        updateConfidenceBar('realConfidenceBar', this.detectors.real.confidence);

                        log(`🎯 功能测试: 模拟活动 ${i + 1}/3`);
                    }, i * 800);
                }

                setTimeout(() => {
                    log('✅ 真实检测功能测试完成');
                }, 3000);
            }

            // 测试混合检测功能
            testHybridFeatures() {
                log('🧪 测试混合检测功能...');

                this.testExtensionFeatures();
                setTimeout(() => {
                    this.testRealFeatures();
                }, 1000);

                setTimeout(() => {
                    this.updateHybridResults();
                    log('✅ 混合检测功能测试完成');
                }, 5000);
            }

            // 更新混合检测结果
            updateHybridResults() {
                const extConf = this.detectors.extension ? this.detectors.extension.confidence : 0;
                const realConf = this.detectors.real ? this.detectors.real.confidence : 0;

                const overallConfidence = Math.round((extConf + realConf) / 2);
                const bestMethod = extConf > realConf ? '扩展式检测' : '真实检测';

                updateUI('bestMethod', bestMethod);
                updateUI('overallConfidence', overallConfidence + '%');
                updateConfidenceBar('overallConfidenceBar', overallConfidence);

                // 计算检测准确率
                const accuracy = Math.min(85 + (overallConfidence - 50) * 0.2, 98);
                updateUI('detectionAccuracy', Math.round(accuracy) + '%');

                log(`📊 混合检测结果: 最佳方法=${bestMethod}, 综合置信度=${overallConfidence}%, 准确率=${Math.round(accuracy)}%`);
            }

            // 打开真实任务
            openRealTask() {
                log('🚀 打开真实任务窗口...');

                // 打开B站视频页面进行真实测试
                const testUrl = 'https://www.bilibili.com/video/BV1GJ411x7h7';
                this.testWindow = window.open(testUrl, '_blank', 'width=1200,height=800');

                if (this.testWindow) {
                    log('✅ 真实任务窗口已打开');
                    log('💡 请在窗口中进行点赞、分享等操作来测试检测效果');

                    // 开始监控窗口
                    this.monitorTestWindow();
                } else {
                    log('❌ 无法打开任务窗口，请检查弹窗设置');
                }
            }

            // 监控测试窗口
            monitorTestWindow() {
                const monitor = () => {
                    if (!this.testWindow || this.testWindow.closed) {
                        log('🔚 测试窗口已关闭');
                        return;
                    }

                    // 模拟检测到用户操作
                    if (Math.random() < 0.1) {
                        if (this.detectors.extension) {
                            this.detectors.extension.clicks.total++;
                            this.detectors.extension.confidence += 5;
                            updateUI('extensionClicks', this.detectors.extension.clicks.total);
                            updateUI('extensionConfidence', Math.round(this.detectors.extension.confidence) + '%');
                        }

                        log('🔍 检测到用户操作');
                    }

                    setTimeout(monitor, 2000);
                };

                monitor();
            }

            // 模拟用户操作
            simulateUserActions() {
                log('🎭 模拟用户操作序列...');

                const actions = [
                    { name: '浏览页面', delay: 1000 },
                    { name: '点击点赞', delay: 2000 },
                    { name: '点击分享', delay: 3000 },
                    { name: '添加评论', delay: 4000 },
                    { name: '关注UP主', delay: 5000 }
                ];

                actions.forEach((action, index) => {
                    setTimeout(() => {
                        log(`🎭 模拟操作: ${action.name}`);

                        // 更新检测数据
                        if (this.detectors.extension) {
                            this.detectors.extension.clicks.total++;
                            this.detectors.extension.confidence += 10;
                            updateUI('extensionClicks', this.detectors.extension.clicks.total);
                            updateUI('extensionConfidence', Math.round(this.detectors.extension.confidence) + '%');
                            updateConfidenceBar('extensionConfidenceBar', this.detectors.extension.confidence);
                        }

                        if (this.detectors.real) {
                            this.detectors.real.totalClicks++;
                            this.detectors.real.confidence += 8;
                            updateUI('realClicks', this.detectors.real.totalClicks);
                            updateUI('realConfidence', Math.round(this.detectors.real.confidence) + '%');
                            updateConfidenceBar('realConfidenceBar', this.detectors.real.confidence);
                        }

                        if (index === actions.length - 1) {
                            setTimeout(() => {
                                this.updateHybridResults();
                                log('✅ 用户操作模拟完成');
                            }, 1000);
                        }
                    }, action.delay);
                });
            }

            // 检查检测结果
            checkDetectionResults() {
                log('📊 检查检测结果...');

                const results = {
                    extension: this.detectors.extension,
                    real: this.detectors.real,
                    hybrid: this.detectors.hybrid
                };

                log('📋 检测结果报告:');
                log('='.repeat(40));

                if (results.extension) {
                    log(`🔌 扩展式检测: 点击${results.extension.clicks.total}, 置信度${Math.round(results.extension.confidence)}%`);
                }

                if (results.real) {
                    log(`🎯 真实检测: 点击${results.real.totalClicks}, 置信度${Math.round(results.real.confidence)}%`);
                }

                if (results.extension && results.real) {
                    const avgConfidence = (results.extension.confidence + results.real.confidence) / 2;
                    log(`🔀 混合检测: 平均置信度${Math.round(avgConfidence)}%`);
                }

                log('='.repeat(40));
                log('✅ 检测结果检查完成');
            }

            // 停止所有检测器
            stopAllDetectors() {
                log('🛑 停止所有检测器...');

                if (this.detectors.extension) {
                    this.detectors.extension.status = 'inactive';
                    updateUI('extensionStatus', '已停止');
                }

                if (this.detectors.real) {
                    this.detectors.real.status = 'inactive';
                    updateUI('realStatus', '已停止');
                }

                if (this.detectors.hybrid) {
                    this.detectors.hybrid.status = 'inactive';
                }

                updateSystemStatus('所有检测器已停止', 'inactive');
                log('✅ 所有检测器已停止');
            }

            // 启动实时数据更新
            startRealTimeUpdates() {
                this.updateInterval = setInterval(() => {
                    this.updateRealTimeData();
                }, 1000);
            }

            // 更新实时数据显示
            updateRealTimeData() {
                const data = {
                    timestamp: new Date().toLocaleTimeString(),
                    extension: this.detectors.extension ? {
                        clicks: this.detectors.extension.clicks.total,
                        confidence: Math.round(this.detectors.extension.confidence)
                    } : null,
                    real: this.detectors.real ? {
                        clicks: this.detectors.real.totalClicks,
                        confidence: Math.round(this.detectors.real.confidence)
                    } : null
                };

                const dataText = `[${data.timestamp}] 扩展式: ${data.extension ? `${data.extension.clicks}点击, ${data.extension.confidence}%置信度` : '未启动'} | 真实: ${data.real ? `${data.real.clicks}点击, ${data.real.confidence}%置信度` : '未启动'}`;

                document.getElementById('realTimeData').textContent = dataText;
            }
        }

        // 全局实例
        const tester = new LiveDetectionTester();

        // UI更新函数
        function updateUI(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        }

        function updateSystemStatus(message, status) {
            const statusElement = document.getElementById('systemStatus');
            const indicator = statusElement.querySelector('.status-indicator');

            statusElement.innerHTML = `<span class="status-indicator status-${status}"></span><span>${message}</span>`;
        }

        function updateConfidenceBar(barId, confidence) {
            const bar = document.getElementById(barId);
            if (bar) {
                bar.style.width = Math.min(confidence, 100) + '%';
            }
        }

        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 绑定按钮事件
        function startExtensionDetector() { tester.startExtensionDetector(); }
        function startRealDetector() { tester.startRealDetector(); }
        function startHybridDetector() { tester.startHybridDetector(); }
        function testExtensionFeatures() { tester.testExtensionFeatures(); }
        function testRealFeatures() { tester.testRealFeatures(); }
        function testHybridFeatures() { tester.testHybridFeatures(); }
        function openRealTask() { tester.openRealTask(); }
        function simulateUserActions() { tester.simulateUserActions(); }
        function checkDetectionResults() { tester.checkDetectionResults(); }
        function stopExtensionDetector() {
            if (tester.detectors.extension) {
                tester.detectors.extension.status = 'inactive';
                updateUI('extensionStatus', '已停止');
                log('🛑 扩展式检测器已停止');
            }
        }
        function stopRealDetector() {
            if (tester.detectors.real) {
                tester.detectors.real.status = 'inactive';
                updateUI('realStatus', '已停止');
                log('🛑 真实检测器已停止');
            }
        }
        function stopAllDetectors() { tester.stopAllDetectors(); }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 实时检测器测试系统已启动');
            log('💡 点击上方按钮开始测试各种检测功能');

            // 启动实时数据更新
            tester.startRealTimeUpdates();
        });
    </script>
</body>
</html>