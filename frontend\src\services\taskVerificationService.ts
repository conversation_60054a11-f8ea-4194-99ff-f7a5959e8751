import { TaskType } from '@/types';
import api from './api';

// 验证结果类型
export interface VerificationResult {
  success: boolean;
  confidence: number; // 0-100 置信度
  method: VerificationMethod;
  evidence?: VerificationEvidence;
  needsManualReview?: boolean;
  failureReason?: string;
  timestamp: number;
}

// 验证方法类型
export type VerificationMethod = 
  | 'api_verification'
  | 'dom_state_detection'
  | 'screenshot_analysis'
  | 'behavior_analysis'
  | 'time_based'
  | 'hybrid';

// 验证证据类型
export interface VerificationEvidence {
  screenshots?: File[];
  domChanges?: DOMChangeRecord[];
  behaviorMetrics?: BehaviorMetrics;
  apiResponse?: any;
  userInteractions?: UserInteraction[];
}

// DOM变化记录
export interface DOMChangeRecord {
  element: string;
  property: string;
  beforeValue: string;
  afterValue: string;
  timestamp: number;
}

// 行为指标
export interface BehaviorMetrics {
  totalTime: number;
  activeTime: number;
  clickCount: number;
  scrollDistance: number;
  mouseMovements: number;
  keystrokes: number;
  focusEvents: number;
}

// 用户交互记录
export interface UserInteraction {
  type: 'click' | 'scroll' | 'keypress' | 'focus' | 'blur';
  target: string;
  timestamp: number;
  coordinates?: { x: number; y: number };
  value?: string;
}

// 平台特定验证配置
export interface PlatformVerificationConfig {
  platform: string;
  supportedMethods: VerificationMethod[];
  apiEndpoints?: {
    checkLike?: string;
    checkShare?: string;
    checkComment?: string;
    checkFollow?: string;
  };
  domSelectors?: {
    likeButton?: string;
    shareButton?: string;
    commentBox?: string;
    followButton?: string;
  };
  behaviorThresholds?: {
    minActiveTime?: number;
    minClicks?: number;
    maxCompletionTime?: number;
  };
}

// 任务验证服务
export class TaskVerificationService {
  private platformConfigs: Map<string, PlatformVerificationConfig> = new Map();
  private verificationHistory: Map<string, VerificationResult[]> = new Map();

  constructor() {
    this.initializePlatformConfigs();
  }

  /**
   * 初始化平台验证配置
   */
  private initializePlatformConfigs(): void {
    // B站配置
    this.platformConfigs.set('bilibili', {
      platform: 'bilibili',
      supportedMethods: ['dom_state_detection', 'behavior_analysis', 'screenshot_analysis'],
      domSelectors: {
        likeButton: '.video-like, .like-btn, [data-v-*] .like, .video-toolbar-left .like, .bpx-player-ctrl-like',
        shareButton: '.video-share, .share-btn, .video-toolbar-right .share, .bpx-player-ctrl-share, .video-share-info, [class*="share"], .video-toolbar .share-container',
        shareModal: '.share-container, .share-panel, .share-dialog, .bili-dialog-m, [class*="share-modal"], .video-share-popover',
        commentBox: '.reply-box-textarea, .comment-input, .reply-box-send, .bili-comment-box',
        followButton: '.follow-btn, .subscribe-btn, .video-up-info .follow, .up-info-right .follow'
      },
      behaviorThresholds: {
        minActiveTime: 2000, // 2秒最小活跃时间
        minClicks: 1,
        maxCompletionTime: 60000 // 1分钟最大完成时间
      }
    });

    // 抖音配置
    this.platformConfigs.set('douyin', {
      platform: 'douyin',
      supportedMethods: ['dom_state_detection', 'behavior_analysis', 'screenshot_analysis'],
      domSelectors: {
        likeButton: '.like-btn, .digg-btn, [data-e2e*="like"], .video-info-detail .like, .interaction-btn[data-type="like"]',
        shareButton: '.share-btn, [data-e2e*="share"], .video-info-detail .share, .interaction-btn[data-type="share"], [class*="share"]',
        shareModal: '.share-modal, .share-panel, [data-e2e*="share-panel"], .share-container, .share-dialog, [class*="share-popup"]',
        commentBox: '.comment-input, [data-e2e*="comment"], .comment-textarea, .input-container textarea',
        followButton: '.follow-btn, [data-e2e*="follow"], .user-info .follow, .author-card .follow'
      },
      behaviorThresholds: {
        minActiveTime: 2000,
        minClicks: 1,
        maxCompletionTime: 60000
      }
    });

    // 快手配置
    this.platformConfigs.set('kuaishou', {
      platform: 'kuaishou',
      supportedMethods: ['dom_state_detection', 'behavior_analysis', 'screenshot_analysis'],
      domSelectors: {
        likeButton: '.like-btn, .praise-btn, .video-info .like, [class*="like"], .interaction-like',
        shareButton: '.share-btn, .video-info .share, [class*="share"], .interaction-share, .share-container',
        shareModal: '.share-modal, .share-panel, .share-dialog, .share-popup, [class*="share-modal"]',
        commentBox: '.comment-input, .comment-textarea, .input-box textarea',
        followButton: '.follow-btn, .user-info .follow, .author-info .follow, [class*="follow"]'
      },
      behaviorThresholds: {
        minActiveTime: 2000,
        minClicks: 1,
        maxCompletionTime: 60000
      }
    });

    // 小红书配置
    this.platformConfigs.set('xiaohongshu', {
      platform: 'xiaohongshu',
      supportedMethods: ['dom_state_detection', 'behavior_analysis', 'screenshot_analysis'],
      domSelectors: {
        likeButton: '.like-btn, .heart-btn',
        shareButton: '.share-btn',
        commentBox: '.comment-input',
        followButton: '.follow-btn'
      },
      behaviorThresholds: {
        minActiveTime: 2000,
        minClicks: 1,
        maxCompletionTime: 60000
      }
    });
  }

  /**
   * 验证任务完成情况
   */
  async verifyTaskCompletion(
    platform: string,
    taskTypes: TaskType[],
    evidence: VerificationEvidence,
    sessionId: string
  ): Promise<Map<TaskType, VerificationResult>> {
    const config = this.platformConfigs.get(platform);
    if (!config) {
      throw new Error(`不支持的平台: ${platform}`);
    }

    const results = new Map<TaskType, VerificationResult>();

    for (const taskType of taskTypes) {
      try {
        const result = await this.verifySpecificTask(config, taskType, evidence, sessionId);
        results.set(taskType, result);
      } catch (error) {
        console.error(`验证任务 ${taskType} 失败:`, error);
        results.set(taskType, {
          success: false,
          confidence: 0,
          method: 'hybrid',
          failureReason: `验证过程出错: ${error.message}`,
          timestamp: Date.now(),
          needsManualReview: true
        });
      }
    }

    // 保存验证历史
    this.verificationHistory.set(sessionId, Array.from(results.values()));

    return results;
  }

  /**
   * 验证特定任务类型
   */
  private async verifySpecificTask(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    evidence: VerificationEvidence,
    sessionId: string
  ): Promise<VerificationResult> {
    const verificationMethods: VerificationResult[] = [];

    // 1. 行为分析验证
    if (evidence.behaviorMetrics && config.supportedMethods.includes('behavior_analysis')) {
      const behaviorResult = this.verifyBehaviorPattern(config, taskType, evidence.behaviorMetrics);
      verificationMethods.push(behaviorResult);
    }

    // 2. DOM状态检测验证
    if (evidence.domChanges && config.supportedMethods.includes('dom_state_detection')) {
      const domResult = this.verifyDOMChanges(config, taskType, evidence.domChanges);
      verificationMethods.push(domResult);
    }

    // 3. 截图分析验证
    if (evidence.screenshots && config.supportedMethods.includes('screenshot_analysis')) {
      const screenshotResult = await this.verifyScreenshots(config, taskType, evidence.screenshots);
      verificationMethods.push(screenshotResult);
    }

    // 4. 用户交互验证
    if (evidence.userInteractions) {
      const interactionResult = this.verifyUserInteractions(config, taskType, evidence.userInteractions);
      verificationMethods.push(interactionResult);
    }

    // 综合评估所有验证结果
    return this.combineVerificationResults(verificationMethods, taskType);
  }

  /**
   * 验证行为模式（增强版）
   */
  private verifyBehaviorPattern(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    metrics: BehaviorMetrics
  ): VerificationResult {
    const thresholds = config.behaviorThresholds || {};
    let confidence = 0;
    const issues: string[] = [];

    // 基础时间验证
    if (thresholds.minActiveTime && metrics.activeTime < thresholds.minActiveTime) {
      issues.push(`活跃时间过短 (${metrics.activeTime}ms < ${thresholds.minActiveTime}ms)`);
    } else {
      confidence += 20;
    }

    // 严格的点击验证 - 必须有真实点击
    if (metrics.clickCount === 0) {
      issues.push('未检测到任何点击操作');
      confidence = 0; // 没有点击直接失败
    } else if (thresholds.minClicks && metrics.clickCount < thresholds.minClicks) {
      issues.push(`点击次数不足 (${metrics.clickCount} < ${thresholds.minClicks})`);
    } else {
      confidence += 40; // 点击是最重要的指标
    }

    // 检查总时间合理性
    if (thresholds.maxCompletionTime && metrics.totalTime > thresholds.maxCompletionTime) {
      issues.push(`完成时间过长 (${metrics.totalTime}ms > ${thresholds.maxCompletionTime}ms)`);
    } else if (metrics.totalTime < 2000) {
      issues.push('完成时间过短，可能是脚本操作');
      confidence -= 30;
    } else {
      confidence += 15;
    }

    // 鼠标移动验证 - 真实用户必须有鼠标移动
    if (metrics.mouseMovements === 0) {
      issues.push('未检测到鼠标移动，可能是脚本操作');
      confidence -= 20;
    } else if (metrics.mouseMovements < 5) {
      issues.push('鼠标移动过少，行为可疑');
      confidence -= 10;
    } else {
      confidence += 15;
    }

    // 特定任务类型验证
    if (taskType === 'comment' && metrics.keystrokes === 0) {
      issues.push('评论任务未检测到键盘输入');
      confidence -= 30;
    } else if (taskType === 'comment' && metrics.keystrokes > 0) {
      confidence += 20;
    }

    // 行为一致性检查
    const activeRatio = metrics.totalTime > 0 ? metrics.activeTime / metrics.totalTime : 0;
    if (activeRatio < 0.3) {
      issues.push('活跃时间比例过低，可能未真实操作');
      confidence -= 15;
    }

    // 最终验证：必须有基本的用户交互
    const hasBasicInteraction = metrics.clickCount > 0 && metrics.mouseMovements > 0;
    if (!hasBasicInteraction) {
      confidence = 0;
      issues.push('缺少基本用户交互，验证失败');
    }

    return {
      success: confidence >= 70 && issues.length === 0 && hasBasicInteraction,
      confidence: Math.max(0, Math.min(confidence, 100)),
      method: 'behavior_analysis',
      failureReason: issues.length > 0 ? issues.join('; ') : undefined,
      timestamp: Date.now(),
      needsManualReview: confidence >= 40 && confidence < 70
    };
  }

  /**
   * 验证DOM变化（增强版 - 严格检测实际操作）
   */
  private verifyDOMChanges(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    changes: DOMChangeRecord[]
  ): VerificationResult {
    const selectors = config.domSelectors || {};
    let confidence = 0;
    let relevantChanges = 0;
    let criticalChanges = 0;
    const detectedChanges: string[] = [];

    for (const change of changes) {
      // 根据任务类型检查相关的DOM变化
      switch (taskType) {
        case 'like':
          // 检查点赞按钮的关键状态变化
          if (this.isLikeButtonChange(change)) {
            relevantChanges++;
            if (this.isCriticalLikeChange(change)) {
              criticalChanges++;
              confidence += 60;
              detectedChanges.push('点赞按钮状态变化');
            } else {
              confidence += 20;
              detectedChanges.push('点赞相关DOM变化');
            }
          }
          break;
        case 'share':
          if (this.isShareButtonChange(change) || this.isShareModalChange(change)) {
            relevantChanges++;
            if (this.isCriticalShareChange(change) || this.isShareModalAppeared(change)) {
              criticalChanges++;
              confidence += 70; // 提高分享检测的置信度
              detectedChanges.push('分享操作确认');
            } else {
              confidence += 25;
              detectedChanges.push('分享相关DOM变化');
            }
          }
          break;
        case 'comment':
          if (this.isCommentChange(change)) {
            relevantChanges++;
            if (this.isCriticalCommentChange(change)) {
              criticalChanges++;
              confidence += 60;
              detectedChanges.push('评论提交确认');
            } else {
              confidence += 20;
              detectedChanges.push('评论相关DOM变化');
            }
          }
          break;
        case 'follow':
          if (this.isFollowButtonChange(change)) {
            relevantChanges++;
            if (this.isCriticalFollowChange(change)) {
              criticalChanges++;
              confidence += 60;
              detectedChanges.push('关注状态变化');
            } else {
              confidence += 20;
              detectedChanges.push('关注相关DOM变化');
            }
          }
          break;
      }
    }

    // 严格验证：必须有关键状态变化才算成功
    const hasRequiredChanges = criticalChanges > 0;
    const finalConfidence = hasRequiredChanges ? confidence : Math.min(confidence, 30);

    return {
      success: hasRequiredChanges && finalConfidence >= 60,
      confidence: Math.min(finalConfidence, 100),
      method: 'dom_state_detection',
      evidence: { domChanges: changes },
      failureReason: !hasRequiredChanges
        ? `未检测到${taskType}的关键状态变化，可能未真实完成操作`
        : relevantChanges === 0
        ? `未检测到${taskType}相关的DOM变化`
        : undefined,
      timestamp: Date.now(),
      needsManualReview: relevantChanges > 0 && !hasRequiredChanges
    };
  }

  /**
   * 检查是否为点赞按钮变化
   */
  private isLikeButtonChange(change: DOMChangeRecord): boolean {
    const element = change.element.toLowerCase();
    const property = change.property.toLowerCase();

    return (
      element.includes('like') ||
      element.includes('digg') ||
      element.includes('praise') ||
      element.includes('heart') ||
      (property === 'class' && (change.afterValue.includes('liked') || change.afterValue.includes('active'))) ||
      (property === 'aria-pressed' && change.afterValue === 'true')
    );
  }

  /**
   * 检查是否为关键的点赞状态变化
   */
  private isCriticalLikeChange(change: DOMChangeRecord): boolean {
    const property = change.property.toLowerCase();
    const beforeValue = change.beforeValue.toLowerCase();
    const afterValue = change.afterValue.toLowerCase();

    // 检查关键状态变化
    return (
      (property === 'class' && !beforeValue.includes('liked') && afterValue.includes('liked')) ||
      (property === 'class' && !beforeValue.includes('active') && afterValue.includes('active')) ||
      (property === 'aria-pressed' && beforeValue === 'false' && afterValue === 'true') ||
      (property === 'data-liked' && beforeValue === 'false' && afterValue === 'true')
    );
  }

  /**
   * 检查是否为分享按钮变化
   */
  private isShareButtonChange(change: DOMChangeRecord): boolean {
    const element = change.element.toLowerCase();
    return element.includes('share') ||
           element.includes('forward') ||
           element.includes('repost') ||
           element.includes('share-btn') ||
           element.includes('share-container') ||
           element.includes('data-e2e') && element.includes('share');
  }

  /**
   * 检查是否为关键的分享状态变化
   */
  private isCriticalShareChange(change: DOMChangeRecord): boolean {
    const property = change.property.toLowerCase();
    const afterValue = change.afterValue.toLowerCase();
    const beforeValue = change.beforeValue?.toLowerCase() || '';

    return (
      (property === 'class' && afterValue.includes('shared') && !beforeValue.includes('shared')) ||
      (property === 'style' && afterValue.includes('display: block')) || // 分享弹窗显示
      (property === 'aria-pressed' && afterValue === 'true' && beforeValue === 'false') ||
      change.element.includes('share-dialog') ||
      change.element.includes('share-modal') ||
      change.element.includes('share-success') ||
      change.element.includes('share-completed')
    );
  }

  /**
   * 检查是否为分享弹窗变化
   */
  private isShareModalChange(change: DOMChangeRecord): boolean {
    const element = change.element.toLowerCase();
    return element.includes('share-modal') ||
           element.includes('share-panel') ||
           element.includes('share-dialog') ||
           element.includes('share-popup') ||
           element.includes('share-container') ||
           element.includes('bili-dialog-m');
  }

  /**
   * 检查分享弹窗是否出现
   */
  private isShareModalAppeared(change: DOMChangeRecord): boolean {
    const property = change.property.toLowerCase();
    const afterValue = change.afterValue.toLowerCase();
    const beforeValue = change.beforeValue?.toLowerCase() || '';
    const element = change.element.toLowerCase();

    // 检查分享相关元素的显示状态变化
    if (element.includes('share')) {
      if (property.includes('display')) {
        return afterValue.includes('block') && beforeValue.includes('none');
      }
      if (property.includes('visibility')) {
        return afterValue.includes('visible') && beforeValue.includes('hidden');
      }
      if (property.includes('opacity')) {
        const afterOpacity = parseFloat(afterValue) || 0;
        const beforeOpacity = parseFloat(beforeValue) || 0;
        return afterOpacity > 0 && beforeOpacity === 0;
      }
    }

    return false;
  }

  /**
   * 检查是否为评论变化
   */
  private isCommentChange(change: DOMChangeRecord): boolean {
    const element = change.element.toLowerCase();
    return element.includes('comment') || element.includes('reply') || element.includes('message');
  }

  /**
   * 检查是否为关键的评论状态变化
   */
  private isCriticalCommentChange(change: DOMChangeRecord): boolean {
    const property = change.property.toLowerCase();
    const afterValue = change.afterValue.toLowerCase();

    return (
      (property === 'childlist' && change.element.includes('comment-list')) ||
      (property === 'value' && afterValue.length > 0) || // 评论内容输入
      change.element.includes('comment-success') || change.element.includes('comment-submitted')
    );
  }

  /**
   * 检查是否为关注按钮变化
   */
  private isFollowButtonChange(change: DOMChangeRecord): boolean {
    const element = change.element.toLowerCase();
    return element.includes('follow') || element.includes('subscribe') || element.includes('attention');
  }

  /**
   * 检查是否为关键的关注状态变化
   */
  private isCriticalFollowChange(change: DOMChangeRecord): boolean {
    const property = change.property.toLowerCase();
    const beforeValue = change.beforeValue.toLowerCase();
    const afterValue = change.afterValue.toLowerCase();

    return (
      (property === 'class' && !beforeValue.includes('followed') && afterValue.includes('followed')) ||
      (property === 'textcontent' && beforeValue.includes('关注') && afterValue.includes('已关注')) ||
      (property === 'textcontent' && beforeValue.includes('follow') && afterValue.includes('following'))
    );
  }

  /**
   * 验证截图
   */
  private async verifyScreenshots(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    screenshots: File[]
  ): Promise<VerificationResult> {
    try {
      // 这里应该调用图像识别API来分析截图
      // 目前先实现基础的文件验证
      if (screenshots.length === 0) {
        return {
          success: false,
          confidence: 0,
          method: 'screenshot_analysis',
          failureReason: '未提供截图证明',
          timestamp: Date.now()
        };
      }

      // 基础验证：检查文件类型和大小
      const validScreenshots = screenshots.filter(file => 
        file.type.startsWith('image/') && file.size > 1024 && file.size < 10 * 1024 * 1024
      );

      if (validScreenshots.length === 0) {
        return {
          success: false,
          confidence: 0,
          method: 'screenshot_analysis',
          failureReason: '截图文件格式或大小不符合要求',
          timestamp: Date.now()
        };
      }

      // TODO: 实现真正的图像识别验证
      // 目前给予基础分数，需要人工审核
      return {
        success: true,
        confidence: 50, // 中等置信度，需要人工审核
        method: 'screenshot_analysis',
        evidence: { screenshots },
        needsManualReview: true,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        success: false,
        confidence: 0,
        method: 'screenshot_analysis',
        failureReason: `截图验证失败: ${error.message}`,
        timestamp: Date.now()
      };
    }
  }

  /**
   * 验证用户交互
   */
  private verifyUserInteractions(
    config: PlatformVerificationConfig,
    taskType: TaskType,
    interactions: UserInteraction[]
  ): VerificationResult {
    let confidence = 0;
    let relevantInteractions = 0;

    // 分析交互模式
    const clickInteractions = interactions.filter(i => i.type === 'click');
    const scrollInteractions = interactions.filter(i => i.type === 'scroll');
    const keypressInteractions = interactions.filter(i => i.type === 'keypress');

    // 检查是否有相关的点击操作
    if (clickInteractions.length > 0) {
      relevantInteractions++;
      confidence += 30;
    }

    // 检查滚动行为（表明用户在浏览内容）
    if (scrollInteractions.length > 0) {
      confidence += 20;
    }

    // 对于评论任务，检查键盘输入
    if (taskType === 'comment' && keypressInteractions.length > 0) {
      relevantInteractions++;
      confidence += 30;
    }

    // 检查交互的时间分布（防止脚本化行为）
    const timeSpans = interactions.map((interaction, index) => 
      index > 0 ? interaction.timestamp - interactions[index - 1].timestamp : 0
    ).filter(span => span > 0);

    const avgTimeSpan = timeSpans.length > 0 ? timeSpans.reduce((a, b) => a + b, 0) / timeSpans.length : 0;
    
    // 如果交互间隔太规律，可能是脚本
    if (avgTimeSpan > 100 && avgTimeSpan < 5000) {
      confidence += 20;
    } else if (avgTimeSpan < 50) {
      confidence -= 30; // 可能是脚本行为
    }

    return {
      success: relevantInteractions > 0 && confidence >= 50,
      confidence: Math.max(0, Math.min(confidence, 100)),
      method: 'behavior_analysis',
      evidence: { userInteractions: interactions },
      failureReason: relevantInteractions === 0 ? '未检测到相关的用户交互' : undefined,
      timestamp: Date.now(),
      needsManualReview: confidence < 50 && relevantInteractions > 0
    };
  }

  /**
   * 综合多种验证结果
   */
  private combineVerificationResults(
    results: VerificationResult[],
    taskType: TaskType
  ): VerificationResult {
    if (results.length === 0) {
      return {
        success: false,
        confidence: 0,
        method: 'hybrid',
        failureReason: '无可用的验证方法',
        timestamp: Date.now(),
        needsManualReview: true
      };
    }

    // 计算加权平均置信度
    const totalConfidence = results.reduce((sum, result) => sum + result.confidence, 0);
    const avgConfidence = totalConfidence / results.length;

    // 检查是否有任何方法明确成功
    const hasSuccessfulMethod = results.some(result => result.success && result.confidence >= 70);
    
    // 检查是否需要人工审核
    const needsManualReview = results.some(result => result.needsManualReview) || avgConfidence < 60;

    // 收集失败原因
    const failureReasons = results
      .filter(result => !result.success && result.failureReason)
      .map(result => result.failureReason)
      .join('; ');

    return {
      success: hasSuccessfulMethod || avgConfidence >= 70,
      confidence: Math.round(avgConfidence),
      method: 'hybrid',
      evidence: {
        // 合并所有证据
        screenshots: results.flatMap(r => r.evidence?.screenshots || []),
        domChanges: results.flatMap(r => r.evidence?.domChanges || []),
        userInteractions: results.flatMap(r => r.evidence?.userInteractions || [])
      },
      needsManualReview,
      failureReason: !hasSuccessfulMethod && avgConfidence < 70 ? failureReasons : undefined,
      timestamp: Date.now()
    };
  }

  /**
   * 获取验证历史
   */
  getVerificationHistory(sessionId: string): VerificationResult[] | undefined {
    return this.verificationHistory.get(sessionId);
  }

  /**
   * 提交人工审核请求
   */
  async submitManualReview(
    sessionId: string,
    taskType: TaskType,
    evidence: VerificationEvidence,
    userNote?: string
  ): Promise<void> {
    try {
      await api.post('/verification/manual-review', {
        session_id: sessionId,
        task_type: taskType,
        evidence,
        user_note: userNote,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('提交人工审核失败:', error);
      throw new Error('提交人工审核失败，请稍后重试');
    }
  }
}

// 自动化检测服务
export class AutomatedDetectionService {
  private detectionScripts: Map<string, string> = new Map();
  private communicationChannels: Map<string, MessageChannel> = new Map();
  private detectionResults: Map<string, any> = new Map();
  private currentSessionId: string | null = null;
  private lastWindowSize: { width: number; height: number } | null = null;

  constructor() {
    this.initializeDetectionScripts();
    this.setupMessageHandlers();
  }

  /**
   * 初始化各平台的检测脚本
   */
  private initializeDetectionScripts(): void {
    // B站检测脚本
    this.detectionScripts.set('bilibili', `
      (function() {
        const detector = {
          platform: 'bilibili',
          detectedActions: new Set(),
          observers: [],

          init() {
            this.setupMutationObserver();
            this.setupClickListeners();
            this.setupPeriodicCheck();
            this.reportStatus('initialized');
          },

          setupMutationObserver() {
            const observer = new MutationObserver((mutations) => {
              mutations.forEach((mutation) => {
                this.checkForActionChanges(mutation);
              });
            });

            observer.observe(document.body, {
              childList: true,
              subtree: true,
              attributes: true,
              attributeFilter: ['class', 'aria-pressed', 'data-liked', 'data-followed']
            });

            this.observers.push(observer);
          },

          setupClickListeners() {
            document.addEventListener('click', (event) => {
              const target = event.target;
              if (this.isLikeButton(target)) {
                setTimeout(() => this.checkLikeStatus(), 500);
              } else if (this.isShareButton(target)) {
                setTimeout(() => this.checkShareStatus(), 500);
              } else if (this.isFollowButton(target)) {
                setTimeout(() => this.checkFollowStatus(), 500);
              }
            }, true);
          },

          setupPeriodicCheck() {
            setInterval(() => {
              this.performPeriodicCheck();
            }, 2000);
          },

          isLikeButton(element) {
            if (!element) return false;
            const classes = element.className || '';
            const text = element.textContent || '';
            return classes.includes('like') || classes.includes('digg') ||
                   text.includes('点赞') || element.closest('.like-btn, .video-like');
          },

          isShareButton(element) {
            if (!element) return false;
            const classes = element.className || '';
            const text = element.textContent || '';
            return classes.includes('share') || text.includes('分享') ||
                   element.closest('.share-btn, .video-share');
          },

          isFollowButton(element) {
            if (!element) return false;
            const classes = element.className || '';
            const text = element.textContent || '';
            return classes.includes('follow') || classes.includes('subscribe') ||
                   text.includes('关注') || text.includes('订阅');
          },

          checkForActionChanges(mutation) {
            if (mutation.type === 'attributes') {
              const target = mutation.target;
              if (this.isLikeButton(target)) {
                this.checkLikeStatus();
              } else if (this.isFollowButton(target)) {
                this.checkFollowStatus();
              }
            }
          },

          checkLikeStatus() {
            const likeButtons = document.querySelectorAll('.like-btn, .video-like, [class*="like"]');
            for (const button of likeButtons) {
              if (this.isButtonActive(button)) {
                this.detectedActions.add('like');
                this.reportAction('like', { element: button.className, timestamp: Date.now() });
                break;
              }
            }
          },

          checkShareStatus() {
            // 检查分享弹窗是否出现
            const shareModals = document.querySelectorAll('.share-modal, .share-dialog, [class*="share-panel"], .share-container, .bili-dialog-m, .video-share-popover');
            for (const modal of shareModals) {
              const style = window.getComputedStyle(modal);
              if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
                this.detectedActions.add('share');
                this.reportAction('share', {
                  element: modal.className,
                  method: 'modal_detection',
                  timestamp: Date.now()
                });
                return;
              }
            }

            // 检查分享按钮状态
            const shareButtons = document.querySelectorAll('.video-share, .share-btn, .video-toolbar-right .share, .bpx-player-ctrl-share, [class*="share"]');
            for (const button of shareButtons) {
              if (this.isButtonActive(button) || button.getAttribute('aria-pressed') === 'true') {
                this.detectedActions.add('share');
                this.reportAction('share', {
                  element: button.className,
                  method: 'button_state',
                  timestamp: Date.now()
                });
                return;
              }
            }
          },

          checkFollowStatus() {
            const followButtons = document.querySelectorAll('.follow-btn, .subscribe-btn, [class*="follow"]');
            for (const button of followButtons) {
              if (this.isButtonActive(button) || button.textContent.includes('已关注')) {
                this.detectedActions.add('follow');
                this.reportAction('follow', { element: button.className, timestamp: Date.now() });
                break;
              }
            }
          },

          isButtonActive(button) {
            const classes = button.className || '';
            const ariaPressed = button.getAttribute('aria-pressed');
            return classes.includes('active') || classes.includes('liked') ||
                   classes.includes('followed') || ariaPressed === 'true';
          },

          performPeriodicCheck() {
            this.checkLikeStatus();
            this.checkFollowStatus();
            this.reportStatus('checking');
          },

          reportAction(action, data) {
            window.parent.postMessage({
              type: 'TASK_ACTION_DETECTED',
              platform: 'bilibili',
              action: action,
              data: data,
              timestamp: Date.now()
            }, '*');
          },

          reportStatus(status) {
            window.parent.postMessage({
              type: 'TASK_DETECTION_STATUS',
              platform: 'bilibili',
              status: status,
              detectedActions: Array.from(this.detectedActions),
              timestamp: Date.now()
            }, '*');
          },

          getDetectedActions() {
            return Array.from(this.detectedActions);
          }
        };

        detector.init();
        window.taskDetector = detector;
      })();
    `);

    // 抖音检测脚本
    this.detectionScripts.set('douyin', `
      (function() {
        const detector = {
          platform: 'douyin',
          detectedActions: new Set(),
          observers: [],

          init() {
            this.setupMutationObserver();
            this.setupClickListeners();
            this.setupPeriodicCheck();
            this.reportStatus('initialized');
          },

          setupMutationObserver() {
            const observer = new MutationObserver((mutations) => {
              mutations.forEach((mutation) => {
                this.checkForActionChanges(mutation);
              });
            });

            observer.observe(document.body, {
              childList: true,
              subtree: true,
              attributes: true,
              attributeFilter: ['class', 'aria-pressed', 'data-e2e']
            });

            this.observers.push(observer);
          },

          setupClickListeners() {
            document.addEventListener('click', (event) => {
              const target = event.target;
              if (this.isLikeButton(target)) {
                setTimeout(() => this.checkLikeStatus(), 500);
              } else if (this.isShareButton(target)) {
                setTimeout(() => this.checkShareStatus(), 500);
              } else if (this.isFollowButton(target)) {
                setTimeout(() => this.checkFollowStatus(), 500);
              }
            }, true);
          },

          setupPeriodicCheck() {
            setInterval(() => {
              this.performPeriodicCheck();
            }, 2000);
          },

          isLikeButton(element) {
            if (!element) return false;
            const dataE2e = element.getAttribute('data-e2e') || '';
            const classes = element.className || '';
            return dataE2e.includes('like') || classes.includes('like') ||
                   classes.includes('digg') || element.closest('[data-e2e*="like"]');
          },

          isShareButton(element) {
            if (!element) return false;
            const dataE2e = element.getAttribute('data-e2e') || '';
            const classes = element.className || '';
            return dataE2e.includes('share') || classes.includes('share') ||
                   element.closest('[data-e2e*="share"]');
          },

          isFollowButton(element) {
            if (!element) return false;
            const dataE2e = element.getAttribute('data-e2e') || '';
            const classes = element.className || '';
            return dataE2e.includes('follow') || classes.includes('follow') ||
                   element.closest('[data-e2e*="follow"]');
          },

          checkForActionChanges(mutation) {
            if (mutation.type === 'attributes') {
              const target = mutation.target;
              if (this.isLikeButton(target)) {
                this.checkLikeStatus();
              } else if (this.isFollowButton(target)) {
                this.checkFollowStatus();
              }
            }
          },

          checkLikeStatus() {
            const likeButtons = document.querySelectorAll('[data-e2e*="like"], .like-btn, [class*="like"]');
            for (const button of likeButtons) {
              if (this.isButtonActive(button)) {
                this.detectedActions.add('like');
                this.reportAction('like', { element: button.className, timestamp: Date.now() });
                break;
              }
            }
          },

          checkShareStatus() {
            // 检查分享弹窗是否出现
            const shareModals = document.querySelectorAll('.share-modal, [data-e2e*="share-panel"], .share-container, .share-dialog, [class*="share-popup"]');
            for (const modal of shareModals) {
              const style = window.getComputedStyle(modal);
              if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
                this.detectedActions.add('share');
                this.reportAction('share', {
                  element: modal.className,
                  method: 'modal_detection',
                  timestamp: Date.now()
                });
                return;
              }
            }

            // 检查分享按钮状态
            const shareButtons = document.querySelectorAll('.share-btn, [data-e2e*="share"], .video-info-detail .share, .interaction-btn[data-type="share"], [class*="share"]');
            for (const button of shareButtons) {
              if (this.isButtonActive(button) || button.getAttribute('aria-pressed') === 'true') {
                this.detectedActions.add('share');
                this.reportAction('share', {
                  element: button.className,
                  method: 'button_state',
                  timestamp: Date.now()
                });
                return;
              }
            }
          },

          checkFollowStatus() {
            const followButtons = document.querySelectorAll('[data-e2e*="follow"], .follow-btn');
            for (const button of followButtons) {
              if (this.isButtonActive(button)) {
                this.detectedActions.add('follow');
                this.reportAction('follow', { element: button.className, timestamp: Date.now() });
                break;
              }
            }
          },

          isButtonActive(button) {
            const classes = button.className || '';
            const style = window.getComputedStyle(button);
            return classes.includes('active') || classes.includes('liked') ||
                   style.color === 'rgb(254, 44, 85)' || // 抖音红色
                   button.getAttribute('aria-pressed') === 'true';
          },

          performPeriodicCheck() {
            this.checkLikeStatus();
            this.checkFollowStatus();
            this.reportStatus('checking');
          },

          reportAction(action, data) {
            window.parent.postMessage({
              type: 'TASK_ACTION_DETECTED',
              platform: 'douyin',
              action: action,
              data: data,
              timestamp: Date.now()
            }, '*');
          },

          reportStatus(status) {
            window.parent.postMessage({
              type: 'TASK_DETECTION_STATUS',
              platform: 'douyin',
              status: status,
              detectedActions: Array.from(this.detectedActions),
              timestamp: Date.now()
            }, '*');
          }
        };

        detector.init();
        window.taskDetector = detector;
      })();
    `);
  }

  /**
   * 设置消息处理器
   */
  private setupMessageHandlers(): void {
    window.addEventListener('message', (event) => {
      if (event.data.type === 'TASK_ACTION_DETECTED') {
        this.handleActionDetected(event.data);
      } else if (event.data.type === 'TASK_DETECTION_STATUS') {
        this.handleDetectionStatus(event.data);
      }
    });
  }

  /**
   * 处理检测到的操作
   */
  private handleActionDetected(data: any): void {
    // 从消息数据中获取会话ID，或使用当前会话ID
    const sessionId = data.sessionId || this.getCurrentSessionId();
    if (sessionId) {
      const existing = this.detectionResults.get(sessionId) || { detectedActions: [], platform: data.platform };
      const detectionData = {
        action: data.action,
        platform: data.platform,
        timestamp: data.timestamp,
        data: data.data,
        confidence: 95 // 直接检测的置信度很高
      };

      existing.detectedActions.push(detectionData);
      this.detectionResults.set(sessionId, existing);

      console.log(`✅ 检测到操作: ${data.action} 在 ${data.platform} 平台 (会话: ${sessionId})`);

      // 通知引导服务
      try {
        const { taskGuidanceService } = require('./taskGuidanceService');
        taskGuidanceService.reportDetection({
          action: data.action,
          detected: true,
          confidence: 95,
          method: data.method || 'direct_detection',
          timestamp: data.timestamp
        });
      } catch (error) {
        console.warn('通知引导服务失败:', error);
      }
    }
  }

  /**
   * 处理检测状态更新
   */
  private handleDetectionStatus(data: any): void {
    console.log(`检测状态更新: ${data.status} - ${data.platform}`);
  }

  /**
   * 注入检测脚本到任务窗口
   */
  injectDetectionScript(taskWindow: Window, platform: string, sessionId: string): void {
    // 设置当前会话ID
    this.currentSessionId = sessionId;

    const script = this.detectionScripts.get(platform);
    if (!script) {
      console.warn(`未找到平台 ${platform} 的检测脚本`);
      return;
    }

    console.log(`🚀 开始注入检测脚本 - 平台: ${platform}, 会话: ${sessionId}`);

    try {
      // 尝试直接注入（同域场景）
      this.tryDirectInjection(taskWindow, script, platform, sessionId);
    } catch (error) {
      console.log('直接注入失败，尝试替代方案:', error.message);
      // 跨域场景：使用替代检测方案
      this.setupCrossDomainDetection(taskWindow, platform, sessionId);
    }
  }

  /**
   * 尝试直接注入脚本（同域场景）
   */
  private tryDirectInjection(taskWindow: Window, script: string, platform: string, sessionId: string): void {
    // 测试是否可以访问窗口的document
    const testAccess = taskWindow.document;

    const injectScript = () => {
      try {
        const scriptElement = taskWindow.document.createElement('script');
        scriptElement.textContent = script;
        taskWindow.document.head.appendChild(scriptElement);
        console.log(`✅ 已成功注入检测脚本到 ${platform} 任务窗口`);
      } catch (err) {
        console.error('脚本注入失败:', err);
      }
    };

    if (taskWindow.document.readyState === 'complete') {
      injectScript();
    } else {
      taskWindow.addEventListener('load', injectScript);
    }
  }

  /**
   * 设置跨域检测方案
   */
  private setupCrossDomainDetection(taskWindow: Window, platform: string, sessionId: string): void {
    console.log(`🔄 设置跨域检测方案 - 平台: ${platform}, 会话: ${sessionId}`);

    // 监控窗口状态变化
    this.monitorWindowChanges(taskWindow, platform, sessionId);

    // 设置智能推断检测
    this.setupIntelligentDetection(taskWindow, platform, sessionId);

    // 启动用户引导
    this.startUserGuidance(platform, sessionId);
  }

  /**
   * 监控窗口状态变化
   */
  private monitorWindowChanges(taskWindow: Window, platform: string, sessionId: string): void {
    const startTime = Date.now();
    let lastUrl = '';
    let urlChangeCount = 0;
    let focusChangeCount = 0;
    let lastFocusTime = 0;
    let totalFocusTime = 0;
    let userInteractionCount = 0;

    const monitor = setInterval(() => {
      try {
        // 检查窗口是否仍然存在
        if (taskWindow.closed) {
          clearInterval(monitor);
          this.handleWindowClosed(platform, sessionId, Date.now() - startTime);
          return;
        }

        // 尝试检测URL变化（可能表示用户导航）
        try {
          const currentUrl = taskWindow.location.href;
          if (currentUrl !== lastUrl) {
            lastUrl = currentUrl;
            urlChangeCount++;
            userInteractionCount++; // URL变化表示用户交互
            console.log(`🔄 检测到URL变化 (${urlChangeCount}次) - ${platform}`);
          }
        } catch (e) {
          // 跨域限制，无法访问URL
        }

        // 检测窗口焦点变化和活跃时间
        const currentTime = Date.now();
        try {
          if (taskWindow.document && taskWindow.document.hasFocus && taskWindow.document.hasFocus()) {
            if (lastFocusTime === 0) {
              lastFocusTime = currentTime;
            }
            focusChangeCount++;
          } else {
            if (lastFocusTime > 0) {
              totalFocusTime += currentTime - lastFocusTime;
              lastFocusTime = 0;
            }
          }
        } catch (e) {
          // 跨域限制
        }

        // 检测窗口大小变化（可能表示用户交互）
        try {
          const currentWidth = taskWindow.innerWidth;
          const currentHeight = taskWindow.innerHeight;
          if (this.lastWindowSize &&
              (this.lastWindowSize.width !== currentWidth || this.lastWindowSize.height !== currentHeight)) {
            userInteractionCount++;
          }
          this.lastWindowSize = { width: currentWidth, height: currentHeight };
        } catch (e) {
          // 跨域限制
        }

      } catch (error) {
        // 窗口可能已关闭或无法访问
        clearInterval(monitor);
      }
    }, 1000);

    // 存储监控数据
    const monitorData = {
      startTime,
      urlChangeCount: () => urlChangeCount,
      focusChangeCount: () => focusChangeCount,
      totalFocusTime: () => totalFocusTime + (lastFocusTime > 0 ? Date.now() - lastFocusTime : 0),
      userInteractionCount: () => userInteractionCount,
      monitor
    };

    this.detectionResults.set(sessionId + '_monitor', monitorData);
  }

  /**
   * 设置智能推断检测
   */
  private setupIntelligentDetection(taskWindow: Window, platform: string, sessionId: string): void {
    const detectionData = {
      platform,
      sessionId,
      startTime: Date.now(),
      detectedActions: [],
      confidence: 0,
      indicators: {
        windowFocusTime: 0,
        minimumTimeSpent: false,
        userInteractionLikely: false,
        windowStateChanges: 0,
        urlChanges: 0,
        focusEvents: 0,
        timeSpentActive: 0
      }
    };

    // 定期评估用户行为指标
    const evaluationInterval = setInterval(() => {
      if (taskWindow.closed) {
        clearInterval(evaluationInterval);
        this.finalizeIntelligentDetection(detectionData);
        return;
      }

      this.updateDetectionIndicators(taskWindow, detectionData);
      this.evaluateTaskCompletion(detectionData, platform);
    }, 2000);

    this.detectionResults.set(sessionId + '_intelligent', { detectionData, evaluationInterval });
  }

  /**
   * 更新检测指标
   */
  private updateDetectionIndicators(taskWindow: Window, detectionData: any): void {
    const currentTime = Date.now();
    const timeSpent = currentTime - detectionData.startTime;

    // 更新时间指标
    if (timeSpent > 10000) { // 10秒以上
      detectionData.indicators.minimumTimeSpent = true;
      detectionData.confidence += 20;
    }

    // 检测窗口焦点（表示用户正在与页面交互）
    try {
      if (taskWindow.document && taskWindow.document.hasFocus && taskWindow.document.hasFocus()) {
        detectionData.indicators.windowFocusTime += 2000;
        if (detectionData.indicators.windowFocusTime > 5000) {
          detectionData.indicators.userInteractionLikely = true;
          detectionData.confidence += 30;
        }
      }
    } catch (e) {
      // 跨域限制
    }

    // 基于时间和行为模式推断可能的操作
    if (timeSpent > 15000 && detectionData.indicators.userInteractionLikely) {
      // 用户在页面停留足够长时间且有交互迹象
      this.inferPossibleActions(detectionData, timeSpent);
    }
  }

  /**
   * 推断可能的用户操作
   */
  private inferPossibleActions(detectionData: any, timeSpent: number): void {
    const platform = detectionData.platform;

    // 基于平台和时间推断可能完成的操作
    if (timeSpent > 15000 && timeSpent < 120000) { // 15秒到2分钟
      // 合理的操作时间范围
      const possibleActions = this.getPlatformTypicalActions(platform);

      possibleActions.forEach(action => {
        if (!detectionData.detectedActions.find(a => a.action === action)) {
          detectionData.detectedActions.push({
            action,
            platform,
            timestamp: Date.now(),
            confidence: this.calculateActionConfidence(action, timeSpent, detectionData.indicators),
            method: 'intelligent_inference'
          });
        }
      });
    }
  }

  /**
   * 获取平台典型操作
   */
  private getPlatformTypicalActions(platform: string): string[] {
    const platformActions = {
      'bilibili': ['like', 'share'],
      'douyin': ['like', 'share', 'follow'],
      'kuaishou': ['like', 'share', 'follow'],
      'xiaohongshu': ['like', 'follow']
    };

    return platformActions[platform] || ['like'];
  }

  /**
   * 计算操作置信度
   */
  private calculateActionConfidence(action: string, timeSpent: number, indicators: any): number {
    let confidence = 0;

    // 基础时间分数
    if (timeSpent > 10000) confidence += 30;
    if (timeSpent > 20000) confidence += 20;

    // 用户交互分数
    if (indicators.userInteractionLikely) confidence += 30;
    if (indicators.windowFocusTime > 10000) confidence += 20;

    // 操作类型调整
    const actionWeights = { 'like': 1.0, 'share': 0.8, 'follow': 0.6, 'comment': 0.4 };
    confidence *= (actionWeights[action] || 0.5);

    return Math.min(confidence, 85); // 最高85%置信度（为智能推断）
  }

  /**
   * 启动用户引导
   */
  private startUserGuidance(platform: string, sessionId: string): void {
    // 显示用户引导信息
    console.log(`🎯 启动用户引导 - 平台: ${platform}`);

    // 可以在这里添加用户界面提示
    const guidance = {
      platform,
      message: this.getGuidanceMessage(platform),
      timestamp: Date.now()
    };

    this.detectionResults.set(sessionId + '_guidance', guidance);
  }

  /**
   * 获取引导消息
   */
  private getGuidanceMessage(platform: string): string {
    const messages = {
      'bilibili': '请在B站视频页面完成点赞或分享操作，完成后关闭窗口即可。',
      'douyin': '请在抖音视频页面完成点赞、分享或关注操作，完成后关闭窗口即可。',
      'kuaishou': '请在快手视频页面完成相应操作，完成后关闭窗口即可。',
      'xiaohongshu': '请在小红书页面完成相应操作，完成后关闭窗口即可。'
    };

    return messages[platform] || '请在视频页面完成相应操作，完成后关闭窗口即可。';
  }

  /**
   * 处理窗口关闭
   */
  private handleWindowClosed(platform: string, sessionId: string, totalTime: number): void {
    console.log(`🔚 窗口已关闭 - 平台: ${platform}, 总时间: ${Math.round(totalTime/1000)}秒`);

    // 整合所有检测结果
    this.finalizeDetectionResults(sessionId, totalTime);
  }

  /**
   * 完成智能检测
   */
  private finalizeIntelligentDetection(detectionData: any): void {
    const totalTime = Date.now() - detectionData.startTime;
    console.log(`🧠 智能检测完成 - 平台: ${detectionData.platform}, 检测到 ${detectionData.detectedActions.length} 个可能操作`);

    // 更新最终置信度
    detectionData.finalConfidence = this.calculateFinalConfidence(detectionData, totalTime);
  }

  /**
   * 计算最终置信度
   */
  private calculateFinalConfidence(detectionData: any, totalTime: number): number {
    let confidence = detectionData.confidence;

    // 时间因子
    if (totalTime < 5000) confidence *= 0.3; // 太快
    else if (totalTime < 10000) confidence *= 0.6; // 稍快
    else if (totalTime > 300000) confidence *= 0.4; // 太慢

    return Math.min(confidence, 90);
  }

  /**
   * 整合检测结果
   */
  private finalizeDetectionResults(sessionId: string, totalTime: number): void {
    const intelligentData = this.detectionResults.get(sessionId + '_intelligent');
    const monitorData = this.detectionResults.get(sessionId + '_monitor');

    if (intelligentData) {
      const finalResult = {
        detectedActions: intelligentData.detectionData.detectedActions,
        totalTime,
        confidence: intelligentData.detectionData.finalConfidence || 0,
        method: 'intelligent_cross_domain',
        indicators: intelligentData.detectionData.indicators
      };

      this.detectionResults.set(sessionId, finalResult);
      console.log(`✅ 检测结果已整合:`, finalResult);
    }
  }

  /**
   * 获取检测结果
   */
  getDetectionResults(sessionId: string): any {
    return this.detectionResults.get(sessionId);
  }

  /**
   * 清除检测结果
   */
  clearDetectionResults(sessionId: string): void {
    this.detectionResults.delete(sessionId);
    this.detectionResults.delete(sessionId + '_monitor');
    this.detectionResults.delete(sessionId + '_intelligent');
    this.detectionResults.delete(sessionId + '_guidance');
  }

  /**
   * 获取当前会话ID
   */
  private getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }

  /**
   * 设置当前会话ID
   */
  setCurrentSessionId(sessionId: string): void {
    this.currentSessionId = sessionId;
  }
}

export const automatedDetectionService = new AutomatedDetectionService();
export const taskVerificationService = new TaskVerificationService();
