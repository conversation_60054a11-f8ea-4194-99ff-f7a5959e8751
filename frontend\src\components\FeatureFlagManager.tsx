import React, { useState } from 'react';
import { Settings, Eye, EyeOff, RotateCcw } from 'lucide-react';
import { 
  getFeatureFlags, 
  isFeatureEnabled, 
  debugFeatureFlags,
  type FeatureFlags 
} from '@/config/featureFlags';

interface FeatureFlagManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

const FeatureFlagManager: React.FC<FeatureFlagManagerProps> = ({ isOpen, onClose }) => {
  const [flags, setFlags] = useState<FeatureFlags>(getFeatureFlags());
  const [searchTerm, setSearchTerm] = useState('');

  if (!isOpen) return null;

  const handleFlagToggle = (flagName: keyof FeatureFlags) => {
    const newFlags = {
      ...flags,
      [flagName]: !flags[flagName]
    };
    setFlags(newFlags);
    
    // 在开发环境中，可以将更改保存到localStorage
    if (import.meta.env.DEV) {
      localStorage.setItem('feature-flags-override', JSON.stringify(newFlags));
      console.log(`🚩 Feature flag ${flagName} toggled to:`, newFlags[flagName]);
    }
  };

  const resetFlags = () => {
    const defaultFlags = getFeatureFlags();
    setFlags(defaultFlags);
    localStorage.removeItem('feature-flags-override');
    console.log('🚩 Feature flags reset to default');
  };

  const filteredFlags = Object.entries(flags).filter(([flagName]) =>
    flagName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getFlagCategory = (flagName: string): string => {
    if (flagName.includes('AUTH')) return '认证';
    if (flagName.includes('TASK')) return '任务';
    if (flagName.includes('POINTS')) return '积分';
    if (flagName.includes('SOCIAL')) return '社交';
    if (flagName.includes('CONSOLE') || flagName.includes('DEBUG') || flagName.includes('PERFORMANCE')) return '调试';
    if (flagName.includes('UI') || flagName.includes('DARK') || flagName.includes('ANIMATION')) return 'UI';
    if (flagName.includes('NEW') || flagName.includes('ADVANCED') || flagName.includes('AI')) return '实验性';
    return '其他';
  };

  const getFlagDescription = (flagName: string): string => {
    const descriptions: Record<string, string> = {
      ENABLE_AUTH_PROTECTION: '启用认证保护，未登录用户将被重定向到登录页',
      ENABLE_DEBUG_LOGIN: '显示调试登录按钮，用于开发测试',
      ENABLE_AUTO_LOGIN: '自动登录功能，跳过认证检查',
      ENABLE_TASK_CREATION: '允许用户创建新任务',
      ENABLE_POINTS_SYSTEM: '启用积分系统功能',
      ENABLE_SOCIAL_BINDING: '允许绑定社交媒体账号',
      ENABLE_NOTIFICATIONS: '启用通知功能',
      ENABLE_CONSOLE_LOGS: '在控制台输出调试日志',
      ENABLE_PERFORMANCE_MONITORING: '启用性能监控',
      ENABLE_ERROR_REPORTING: '启用错误报告',
      ENABLE_DARK_MODE: '启用深色模式',
      ENABLE_ANIMATIONS: '启用动画效果',
      ENABLE_RESPONSIVE_DESIGN: '启用响应式设计',
      ENABLE_NEW_DASHBOARD: '启用新版仪表板',
      ENABLE_ADVANCED_ANALYTICS: '启用高级分析功能',
      ENABLE_AI_RECOMMENDATIONS: '启用AI推荐功能',
    };
    return descriptions[flagName] || '暂无描述';
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* 背景遮罩 */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* 弹窗内容 */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* 头部 */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Settings className="h-6 w-6 text-gray-400 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">
                  功能标志管理
                </h3>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={resetFlags}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  重置
                </button>
                <button
                  onClick={debugFeatureFlags}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  打印状态
                </button>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
            </div>

            {/* 搜索框 */}
            <div className="mb-4">
              <input
                type="text"
                placeholder="搜索功能标志..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* 功能标志列表 */}
            <div className="max-h-96 overflow-y-auto">
              <div className="space-y-3">
                {filteredFlags.map(([flagName, enabled]) => (
                  <div
                    key={flagName}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                          {getFlagCategory(flagName)}
                        </span>
                        <h4 className="text-sm font-medium text-gray-900">
                          {flagName}
                        </h4>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {getFlagDescription(flagName)}
                      </p>
                    </div>
                    
                    <button
                      onClick={() => handleFlagToggle(flagName as keyof FeatureFlags)}
                      className={`flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                        enabled
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                      }`}
                    >
                      {enabled ? (
                        <>
                          <Eye className="h-3 w-3 mr-1" />
                          启用
                        </>
                      ) : (
                        <>
                          <EyeOff className="h-3 w-3 mr-1" />
                          禁用
                        </>
                      )}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 底部 */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeatureFlagManager;
