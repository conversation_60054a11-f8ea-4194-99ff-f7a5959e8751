/**
 * 检测诊断和修复工具
 * 专门用于诊断和解决跨域检测问题
 */

export interface DetectionDiagnostics {
  issues: string[];
  solutions: string[];
  confidence: number;
  recommendedAction: 'retry' | 'force_validation' | 'manual_review' | 'user_confirmation';
}

export interface DetectionStatus {
  extensionDetector: 'working' | 'failed' | 'no_data';
  realDetector: 'working' | 'failed' | 'no_data';
  legacyDetector: 'working' | 'failed' | 'no_data';
  intelligentDetector: 'working' | 'failed' | 'no_data';
  overallStatus: 'healthy' | 'degraded' | 'failed';
}

class DetectionDiagnosticsService {
  /**
   * 诊断检测系统状态
   */
  diagnoseDetectionSystem(
    sessionId: string,
    platform: string,
    duration: number,
    extensionData?: any,
    realData?: any,
    legacyData?: any,
    intelligentData?: any
  ): DetectionDiagnostics {
    console.log(`🔍 开始诊断检测系统 - 会话: ${sessionId}`);

    const issues: string[] = [];
    const solutions: string[] = [];
    let confidence = 100;

    // 检查扩展式检测器
    const extensionStatus = this.checkExtensionDetector(extensionData);
    if (extensionStatus !== 'working') {
      issues.push('扩展式检测器失效');
      solutions.push('尝试重新注入检测脚本');
      confidence -= 25;
    }

    // 检查真实检测器
    const realStatus = this.checkRealDetector(realData);
    if (realStatus !== 'working') {
      issues.push('真实检测器失效');
      solutions.push('启用备用检测方法');
      confidence -= 20;
    }

    // 检查传统检测器
    const legacyStatus = this.checkLegacyDetector(legacyData);
    if (legacyStatus !== 'working') {
      issues.push('传统检测器失效');
      solutions.push('使用智能推断验证');
      confidence -= 15;
    }

    // 检查智能检测器
    const intelligentStatus = this.checkIntelligentDetector(intelligentData);
    if (intelligentStatus !== 'working') {
      issues.push('智能检测器失效');
      solutions.push('使用时间模式验证');
      confidence -= 10;
    }

    // 基于问题数量确定推荐操作
    let recommendedAction: DetectionDiagnostics['recommendedAction'];
    
    if (issues.length === 0) {
      recommendedAction = 'retry';
    } else if (issues.length <= 2 && duration >= 8000) {
      recommendedAction = 'force_validation';
    } else if (duration >= 15000) {
      recommendedAction = 'user_confirmation';
    } else {
      recommendedAction = 'manual_review';
    }

    // 添加通用解决方案
    if (issues.length > 0) {
      solutions.push('要求用户提供完成截图');
      solutions.push('启用手动审核流程');
    }

    return {
      issues,
      solutions,
      confidence: Math.max(confidence, 0),
      recommendedAction
    };
  }

  /**
   * 检查扩展式检测器状态
   */
  private checkExtensionDetector(data?: any): DetectionStatus['extensionDetector'] {
    if (!data) return 'no_data';
    
    if (data.clicks && data.clicks.total > 0) return 'working';
    if (data.confidence && data.confidence > 0) return 'working';
    if (data.performance && data.performance.userInteractions > 0) return 'working';
    
    return 'failed';
  }

  /**
   * 检查真实检测器状态
   */
  private checkRealDetector(data?: any): DetectionStatus['realDetector'] {
    if (!data) return 'no_data';
    
    if (data.totalClicks > 0) return 'working';
    if (data.confidence && data.confidence > 0) return 'working';
    if (data.windowActivity && data.windowActivity.mouseActivity > 0) return 'working';
    
    return 'failed';
  }

  /**
   * 检查传统检测器状态
   */
  private checkLegacyDetector(data?: any): DetectionStatus['legacyDetector'] {
    if (!data) return 'no_data';
    
    if (data.totalClicks > 0) return 'working';
    if (data.detectedActions && data.detectedActions.length > 0) return 'working';
    
    return 'failed';
  }

  /**
   * 检查智能检测器状态
   */
  private checkIntelligentDetector(data?: any): DetectionStatus['intelligentDetector'] {
    if (!data) return 'no_data';
    
    if (data.detectedActions && data.detectedActions.length > 0) return 'working';
    if (data.confidence && data.confidence > 30) return 'working';
    
    return 'failed';
  }

  /**
   * 获取检测系统整体状态
   */
  getOverallDetectionStatus(
    extensionData?: any,
    realData?: any,
    legacyData?: any,
    intelligentData?: any
  ): DetectionStatus {
    const extensionDetector = this.checkExtensionDetector(extensionData);
    const realDetector = this.checkRealDetector(realData);
    const legacyDetector = this.checkLegacyDetector(legacyData);
    const intelligentDetector = this.checkIntelligentDetector(intelligentData);

    const workingCount = [extensionDetector, realDetector, legacyDetector, intelligentDetector]
      .filter(status => status === 'working').length;

    let overallStatus: DetectionStatus['overallStatus'];
    if (workingCount >= 2) {
      overallStatus = 'healthy';
    } else if (workingCount >= 1) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'failed';
    }

    return {
      extensionDetector,
      realDetector,
      legacyDetector,
      intelligentDetector,
      overallStatus
    };
  }

  /**
   * 生成修复建议
   */
  generateFixSuggestions(diagnostics: DetectionDiagnostics, platform: string): string[] {
    const suggestions: string[] = [];

    switch (diagnostics.recommendedAction) {
      case 'retry':
        suggestions.push('检测系统正常，建议重试任务');
        break;

      case 'force_validation':
        suggestions.push('启用强制验证模式');
        suggestions.push('基于时间和平台特征进行验证');
        break;

      case 'user_confirmation':
        suggestions.push('要求用户确认任务完成');
        suggestions.push('提供任务完成指导');
        break;

      case 'manual_review':
        suggestions.push('转入人工审核流程');
        suggestions.push('要求提供完成截图');
        break;
    }

    // 平台特定建议
    if (platform === 'bilibili') {
      suggestions.push('B站用户可以检查点赞按钮是否变色');
      suggestions.push('确认关注按钮状态变化');
    } else if (platform === 'douyin') {
      suggestions.push('抖音用户可以检查红心点赞效果');
      suggestions.push('确认分享面板是否弹出');
    }

    return suggestions;
  }

  /**
   * 创建用户友好的错误消息
   */
  createUserFriendlyMessage(diagnostics: DetectionDiagnostics, platform: string): string {
    const platformNames = {
      'bilibili': 'B站',
      'douyin': '抖音',
      'kuaishou': '快手',
      'xiaohongshu': '小红书'
    };

    const platformName = platformNames[platform as keyof typeof platformNames] || platform;

    if (diagnostics.issues.length === 0) {
      return `✅ 检测系统正常工作，请继续完成${platformName}任务。`;
    }

    let message = `⚠️ 在${platformName}平台检测到以下问题：\n`;
    message += diagnostics.issues.map(issue => `• ${issue}`).join('\n');
    message += '\n\n💡 建议解决方案：\n';
    message += diagnostics.solutions.slice(0, 3).map(solution => `• ${solution}`).join('\n');

    if (diagnostics.recommendedAction === 'user_confirmation') {
      message += '\n\n🤔 请确认您是否已经完成了所需的操作？';
    }

    return message;
  }
}

export const detectionDiagnosticsService = new DetectionDiagnosticsService();
