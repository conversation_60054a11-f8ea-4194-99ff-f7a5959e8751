import { BehaviorMetrics, UserInteraction, DOMChangeRecord } from './taskVerificationService';

/**
 * 行为跟踪服务
 * 用于监控用户在任务窗口中的行为模式
 */
export class BehaviorTrackingService {
  private isTracking = false;
  private startTime = 0;
  private lastActiveTime = 0;
  private interactions: UserInteraction[] = [];
  private domChanges: DOMChangeRecord[] = [];
  private metrics: BehaviorMetrics = {
    totalTime: 0,
    activeTime: 0,
    clickCount: 0,
    scrollDistance: 0,
    mouseMovements: 0,
    keystrokes: 0,
    focusEvents: 0
  };

  private observers: MutationObserver[] = [];
  private eventListeners: Array<{ element: EventTarget; event: string; handler: EventListener }> = [];

  /**
   * 开始跟踪用户行为
   */
  startTracking(targetWindow?: Window): void {
    if (this.isTracking) {
      this.stopTracking();
    }

    this.isTracking = true;
    this.startTime = Date.now();
    this.lastActiveTime = this.startTime;
    this.resetMetrics();

    const windowToTrack = targetWindow || window;
    const documentToTrack = windowToTrack.document;

    try {
      // 监听鼠标事件
      this.addEventListeners(windowToTrack, documentToTrack);
      
      // 监听DOM变化
      this.setupDOMObserver(documentToTrack);
      
      // 定期更新活跃时间
      this.startActiveTimeTracking();
    } catch (error) {
      console.warn('行为跟踪初始化失败:', error);
    }
  }

  /**
   * 停止跟踪
   */
  stopTracking(): BehaviorMetrics {
    if (!this.isTracking) {
      return this.metrics;
    }

    this.isTracking = false;
    this.metrics.totalTime = Date.now() - this.startTime;

    // 清理事件监听器
    this.eventListeners.forEach(({ element, event, handler }) => {
      try {
        element.removeEventListener(event, handler);
      } catch (error) {
        console.warn('移除事件监听器失败:', error);
      }
    });
    this.eventListeners = [];

    // 清理DOM观察器
    this.observers.forEach(observer => {
      try {
        observer.disconnect();
      } catch (error) {
        console.warn('断开DOM观察器失败:', error);
      }
    });
    this.observers = [];

    return { ...this.metrics };
  }

  /**
   * 获取当前行为数据
   */
  getCurrentData(): {
    metrics: BehaviorMetrics;
    interactions: UserInteraction[];
    domChanges: DOMChangeRecord[];
  } {
    if (this.isTracking) {
      this.metrics.totalTime = Date.now() - this.startTime;
    }

    return {
      metrics: { ...this.metrics },
      interactions: [...this.interactions],
      domChanges: [...this.domChanges]
    };
  }

  /**
   * 重置指标
   */
  private resetMetrics(): void {
    this.interactions = [];
    this.domChanges = [];
    this.metrics = {
      totalTime: 0,
      activeTime: 0,
      clickCount: 0,
      scrollDistance: 0,
      mouseMovements: 0,
      keystrokes: 0,
      focusEvents: 0
    };
  }

  /**
   * 添加事件监听器
   */
  private addEventListeners(targetWindow: Window, targetDocument: Document): void {
    // 鼠标点击事件
    const clickHandler = (event: MouseEvent) => {
      this.recordInteraction({
        type: 'click',
        target: this.getElementSelector(event.target as Element),
        timestamp: Date.now(),
        coordinates: { x: event.clientX, y: event.clientY }
      });
      this.metrics.clickCount++;
      this.updateActiveTime();
    };
    this.addEventListener(targetDocument, 'click', clickHandler);

    // 鼠标移动事件（节流处理）
    let mouseMoveThrottle = 0;
    const mouseMoveHandler = (event: MouseEvent) => {
      const now = Date.now();
      if (now - mouseMoveThrottle > 100) { // 100ms节流
        this.metrics.mouseMovements++;
        this.updateActiveTime();
        mouseMoveThrottle = now;
      }
    };
    this.addEventListener(targetDocument, 'mousemove', mouseMoveHandler);

    // 滚动事件
    let lastScrollY = targetWindow.scrollY;
    const scrollHandler = () => {
      const currentScrollY = targetWindow.scrollY;
      const scrollDelta = Math.abs(currentScrollY - lastScrollY);
      this.metrics.scrollDistance += scrollDelta;
      lastScrollY = currentScrollY;

      this.recordInteraction({
        type: 'scroll',
        target: 'window',
        timestamp: Date.now()
      });
      this.updateActiveTime();
    };
    this.addEventListener(targetWindow, 'scroll', scrollHandler);

    // 键盘事件
    const keyHandler = (event: KeyboardEvent) => {
      this.recordInteraction({
        type: 'keypress',
        target: this.getElementSelector(event.target as Element),
        timestamp: Date.now(),
        value: event.key
      });
      this.metrics.keystrokes++;
      this.updateActiveTime();
    };
    this.addEventListener(targetDocument, 'keydown', keyHandler);

    // 焦点事件
    const focusHandler = (event: FocusEvent) => {
      this.recordInteraction({
        type: 'focus',
        target: this.getElementSelector(event.target as Element),
        timestamp: Date.now()
      });
      this.metrics.focusEvents++;
      this.updateActiveTime();
    };
    this.addEventListener(targetDocument, 'focus', focusHandler, true);

    const blurHandler = (event: FocusEvent) => {
      this.recordInteraction({
        type: 'blur',
        target: this.getElementSelector(event.target as Element),
        timestamp: Date.now()
      });
    };
    this.addEventListener(targetDocument, 'blur', blurHandler, true);

    // 窗口可见性变化
    const visibilityHandler = () => {
      if (!targetDocument.hidden) {
        this.updateActiveTime();
      }
    };
    this.addEventListener(targetDocument, 'visibilitychange', visibilityHandler);
  }

  /**
   * 添加事件监听器并记录
   */
  private addEventListener(
    element: EventTarget,
    event: string,
    handler: EventListener,
    useCapture = false
  ): void {
    try {
      element.addEventListener(event, handler, useCapture);
      this.eventListeners.push({ element, event, handler });
    } catch (error) {
      console.warn(`添加事件监听器失败 (${event}):`, error);
    }
  }

  /**
   * 设置DOM观察器
   */
  private setupDOMObserver(targetDocument: Document): void {
    try {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes') {
            const element = mutation.target as Element;
            const attributeName = mutation.attributeName;
            
            if (attributeName && this.isRelevantAttribute(attributeName)) {
              this.recordDOMChange({
                element: this.getElementSelector(element),
                property: attributeName,
                beforeValue: mutation.oldValue || '',
                afterValue: element.getAttribute(attributeName) || '',
                timestamp: Date.now()
              });
            }
          } else if (mutation.type === 'childList') {
            // 记录子元素变化
            if (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0) {
              this.recordDOMChange({
                element: this.getElementSelector(mutation.target as Element),
                property: 'childList',
                beforeValue: `${mutation.removedNodes.length} removed`,
                afterValue: `${mutation.addedNodes.length} added`,
                timestamp: Date.now()
              });
            }
          }
        });
      });

      observer.observe(targetDocument.body, {
        attributes: true,
        attributeOldValue: true,
        childList: true,
        subtree: true,
        attributeFilter: ['class', 'style', 'data-liked', 'data-followed', 'aria-pressed']
      });

      this.observers.push(observer);
    } catch (error) {
      console.warn('设置DOM观察器失败:', error);
    }
  }

  /**
   * 开始活跃时间跟踪
   */
  private startActiveTimeTracking(): void {
    const updateInterval = setInterval(() => {
      if (!this.isTracking) {
        clearInterval(updateInterval);
        return;
      }

      const now = Date.now();
      // 如果最近5秒内有活动，计入活跃时间
      if (now - this.lastActiveTime < 5000) {
        this.metrics.activeTime += 1000; // 每秒更新
      }
    }, 1000);
  }

  /**
   * 更新活跃时间
   */
  private updateActiveTime(): void {
    this.lastActiveTime = Date.now();
  }

  /**
   * 记录用户交互
   */
  private recordInteraction(interaction: UserInteraction): void {
    this.interactions.push(interaction);
    
    // 限制交互记录数量，避免内存溢出
    if (this.interactions.length > 1000) {
      this.interactions = this.interactions.slice(-500);
    }
  }

  /**
   * 记录DOM变化
   */
  private recordDOMChange(change: DOMChangeRecord): void {
    this.domChanges.push(change);
    
    // 限制DOM变化记录数量
    if (this.domChanges.length > 500) {
      this.domChanges = this.domChanges.slice(-250);
    }
  }

  /**
   * 获取元素选择器
   */
  private getElementSelector(element: Element | null): string {
    if (!element) return 'unknown';

    try {
      // 尝试获取有意义的选择器
      if (element.id) {
        return `#${element.id}`;
      }
      
      if (element.className && typeof element.className === 'string') {
        const classes = element.className.split(' ').filter(c => c.length > 0);
        if (classes.length > 0) {
          return `.${classes[0]}`;
        }
      }

      // 使用标签名和属性
      let selector = element.tagName.toLowerCase();
      
      // 添加一些常见的识别属性
      const identifyingAttrs = ['data-testid', 'data-id', 'role', 'type'];
      for (const attr of identifyingAttrs) {
        const value = element.getAttribute(attr);
        if (value) {
          selector += `[${attr}="${value}"]`;
          break;
        }
      }

      return selector;
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * 检查是否为相关属性
   */
  private isRelevantAttribute(attributeName: string): boolean {
    const relevantAttributes = [
      'class', 'style', 'aria-pressed', 'aria-selected',
      'data-liked', 'data-followed', 'data-shared',
      'disabled', 'checked', 'selected'
    ];
    return relevantAttributes.includes(attributeName);
  }

  /**
   * 检测可疑行为模式
   */
  detectSuspiciousPatterns(): {
    isSuspicious: boolean;
    reasons: string[];
    confidence: number;
  } {
    const reasons: string[] = [];
    let suspiciousScore = 0;

    // 检查点击频率
    if (this.metrics.clickCount > 0 && this.metrics.totalTime > 0) {
      const clickRate = this.metrics.clickCount / (this.metrics.totalTime / 1000);
      if (clickRate > 5) { // 每秒超过5次点击
        reasons.push('点击频率异常高');
        suspiciousScore += 30;
      }
    }

    // 检查鼠标移动
    if (this.metrics.mouseMovements === 0 && this.metrics.clickCount > 0) {
      reasons.push('有点击但无鼠标移动');
      suspiciousScore += 40;
    }

    // 检查活跃时间比例
    if (this.metrics.totalTime > 0) {
      const activeRatio = this.metrics.activeTime / this.metrics.totalTime;
      if (activeRatio < 0.1) { // 活跃时间少于10%
        reasons.push('活跃时间比例过低');
        suspiciousScore += 20;
      }
    }

    // 检查交互时间间隔
    if (this.interactions.length > 1) {
      const intervals = [];
      for (let i = 1; i < this.interactions.length; i++) {
        intervals.push(this.interactions[i].timestamp - this.interactions[i - 1].timestamp);
      }
      
      // 检查是否有过于规律的间隔
      const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
      const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
      
      if (variance < 100 && avgInterval < 200) { // 间隔过于规律且很短
        reasons.push('交互间隔过于规律');
        suspiciousScore += 35;
      }
    }

    return {
      isSuspicious: suspiciousScore >= 50,
      reasons,
      confidence: Math.min(suspiciousScore, 100)
    };
  }
}

export const behaviorTrackingService = new BehaviorTrackingService();
