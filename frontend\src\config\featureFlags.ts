/**
 * 功能标志配置
 * 用于控制应用中各种功能的开启和关闭
 */

export interface FeatureFlags {
  // 认证相关
  ENABLE_AUTH_PROTECTION: boolean;
  ENABLE_DEBUG_LOGIN: boolean;
  ENABLE_AUTO_LOGIN: boolean;
  
  // 功能模块
  ENABLE_TASK_CREATION: boolean;
  ENABLE_POINTS_SYSTEM: boolean;
  ENABLE_SOCIAL_BINDING: boolean;
  ENABLE_NOTIFICATIONS: boolean;
  
  // 开发调试
  ENABLE_CONSOLE_LOGS: boolean;
  ENABLE_PERFORMANCE_MONITORING: boolean;
  ENABLE_ERROR_REPORTING: boolean;
  
  // UI功能
  ENABLE_DARK_MODE: boolean;
  ENABLE_ANIMATIONS: boolean;
  ENABLE_RESPONSIVE_DESIGN: boolean;
  
  // 实验性功能
  ENABLE_NEW_DASHBOARD: boolean;
  ENABLE_ADVANCED_ANALYTICS: boolean;
  ENABLE_AI_RECOMMENDATIONS: boolean;
}

/**
 * 默认功能标志配置
 * 生产环境建议的安全配置
 */
const defaultFlags: FeatureFlags = {
  // 认证相关 - 生产环境应该启用认证保护
  ENABLE_AUTH_PROTECTION: true,
  ENABLE_DEBUG_LOGIN: true,
  ENABLE_AUTO_LOGIN: true,
  
  // 功能模块 - 核心功能
  ENABLE_TASK_CREATION: true,
  ENABLE_POINTS_SYSTEM: true,
  ENABLE_SOCIAL_BINDING: true,
  ENABLE_NOTIFICATIONS: true,
  
  // 开发调试 - 生产环境建议关闭
  ENABLE_CONSOLE_LOGS: true,
  ENABLE_PERFORMANCE_MONITORING: true,
  ENABLE_ERROR_REPORTING: true,
  
  // UI功能
  ENABLE_DARK_MODE: true,
  ENABLE_ANIMATIONS: true,
  ENABLE_RESPONSIVE_DESIGN: true,
  
  // 实验性功能 - 默认关闭
  ENABLE_NEW_DASHBOARD: true,
  ENABLE_ADVANCED_ANALYTICS: true,
  ENABLE_AI_RECOMMENDATIONS: true,
};

/**
 * 环境特定的功能标志覆盖
 */
const environmentOverrides: Partial<Record<string, Partial<FeatureFlags>>> = {
  development: {
    ENABLE_AUTH_PROTECTION: true,
    ENABLE_DEBUG_LOGIN: true,
    ENABLE_AUTO_LOGIN: true,
    ENABLE_CONSOLE_LOGS: true,
    ENABLE_PERFORMANCE_MONITORING: true,
  },
  
  staging: {
    ENABLE_AUTH_PROTECTION: true,
    ENABLE_DEBUG_LOGIN: true,
    ENABLE_AUTO_LOGIN: true,
    ENABLE_CONSOLE_LOGS: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_ERROR_REPORTING: true,
  },
  
  production: {
    ENABLE_AUTH_PROTECTION: true,
    ENABLE_DEBUG_LOGIN: true,
    ENABLE_AUTO_LOGIN: true,
    ENABLE_CONSOLE_LOGS: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_ERROR_REPORTING: true,
  },
};

/**
 * 获取当前环境
 */
function getCurrentEnvironment(): string {
  return import.meta.env.MODE || 'development';
}

/**
 * 获取最终的功能标志配置
 */
export function getFeatureFlags(): FeatureFlags {
  const environment = getCurrentEnvironment();
  const overrides = environmentOverrides[environment] || {};
  
  return {
    ...defaultFlags,
    ...overrides,
  };
}

/**
 * 检查特定功能是否启用
 */
export function isFeatureEnabled(flag: keyof FeatureFlags): boolean {
  const flags = getFeatureFlags();
  return flags[flag];
}

/**
 * 功能标志Hook (React)
 */
export function useFeatureFlag(flag: keyof FeatureFlags): boolean {
  return isFeatureEnabled(flag);
}

/**
 * 批量检查功能标志
 */
export function areAllFeaturesEnabled(flags: (keyof FeatureFlags)[]): boolean {
  return flags.every(flag => isFeatureEnabled(flag));
}

/**
 * 获取所有启用的功能标志
 */
export function getEnabledFeatures(): (keyof FeatureFlags)[] {
  const flags = getFeatureFlags();
  return Object.entries(flags)
    .filter(([, enabled]) => enabled)
    .map(([flag]) => flag as keyof FeatureFlags);
}

/**
 * 开发工具：打印当前功能标志状态
 */
export function debugFeatureFlags(): void {
  if (isFeatureEnabled('ENABLE_CONSOLE_LOGS')) {
    console.group('🚩 Feature Flags Status');
    const flags = getFeatureFlags();
    Object.entries(flags).forEach(([flag, enabled]) => {
      console.log(`${enabled ? '✅' : '❌'} ${flag}: ${enabled}`);
    });
    console.groupEnd();
  }
}
