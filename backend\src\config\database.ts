import knex, { Knex } from 'knex';
import path from 'path';
import fs from 'fs';
import { logger } from './logger';

// 确定是否使用模拟数据库
export const USE_MOCK_DB = process.env.USE_MOCK_DB === 'true';

// 数据库连接实例
export let db: Knex | null = null;

/**
 * 初始化数据库连接
 */
export const initDatabase = async (): Promise<void> => {
  if (USE_MOCK_DB) {
    logger.info('使用模拟数据库');
    return;
  }

  try {
    const knexfilePath = path.resolve(__dirname, '../../knexfile.js');
    
    if (!fs.existsSync(knexfilePath)) {
      throw new Error(`找不到knexfile.js: ${knexfilePath}`);
    }
    
    const knexConfig = require(knexfilePath);
    const environment = process.env.NODE_ENV || 'development';
    const config = knexConfig[environment];
    
    if (!config) {
      throw new Error(`在knexfile.js中找不到环境配置: ${environment}`);
    }

    db = knex(config);
    
    // 测试连接
    await db.raw('SELECT 1');
    logger.info(`数据库连接成功 (${environment})`);
  } catch (error) {
    logger.error('数据库连接失败:', error);
    throw error;
  }
};

/**
 * 获取数据库实例
 */
export const getDatabase = (): Knex => {
  if (!db && !USE_MOCK_DB) {
    throw new Error('数据库未初始化');
  }
  return db as Knex;
};

/**
 * 关闭数据库连接
 */
export const closeDatabase = async (): Promise<void> => {
  if (db) {
    await db.destroy();
    db = null;
    logger.info('数据库连接已关闭');
  }
}; 