/**
 * 安全修复验证测试脚本
 * 
 * 用于验证任务检测系统的安全漏洞是否已修复
 * 运行方式：在浏览器控制台中执行此脚本
 */

console.log('🔒 开始验证任务检测系统安全修复...\n');

// 模拟修复后的验证逻辑
const SecurityFixedTaskExecutionService = {
  
  // 严格的智能推断置信度计算
  calculateIntelligentConfidence(session, duration) {
    let confidence = 0;
    
    // 严格的基础时间分数
    if (duration > 15000) confidence += 15;  // 15秒以上才给分
    if (duration > 30000) confidence += 20;  // 30秒以上
    if (duration > 60000) confidence += 15;  // 1分钟以上
    
    // 严厉的时间惩罚
    if (duration < 10000) confidence -= 60;  // 10秒以下严重减分
    if (duration < 15000) confidence -= 30;  // 15秒以下减分
    
    // 严格限制：最高只能到60%
    return Math.max(0, Math.min(60, confidence));
  },
  
  // 严格的时间模式分析
  analyzeTimePattern(duration, requirements) {
    const durationSeconds = duration / 1000;
    let confidence = 0;
    
    if (durationSeconds < 10) {
      confidence = 0;
    } else if (durationSeconds < 15) {
      confidence = 10;
    } else if (durationSeconds < 30) {
      confidence = 25;
    } else if (durationSeconds < 60) {
      confidence = 40;
    } else if (durationSeconds < 120) {
      confidence = 50;
    } else {
      confidence = 20;
    }
    
    return Math.max(0, Math.min(confidence, 50));
  },
  
  // 真实用户交互验证
  validateRealUserInteraction(session, detectionResults, behaviorData) {
    // 检查高置信度操作
    if (detectionResults && detectionResults.detectedActions) {
      const highConfidenceActions = detectionResults.detectedActions.filter(
        action => action.confidence >= 80
      );
      if (highConfidenceActions.length > 0) return true;
    }
    
    // 检查用户交互行为
    if (behaviorData && behaviorData.interactions) {
      const realInteractions = behaviorData.interactions.filter(interaction => 
        interaction.type === 'click' || interaction.type === 'input'
      );
      if (realInteractions.length >= 2) return true;
    }
    
    // 检查长时间执行
    const duration = Date.now() - session.startTime;
    if (duration >= 30000) {
      if (detectionResults && detectionResults.indicators) {
        const indicators = detectionResults.indicators;
        if (indicators.windowFocusTime > 15000 || indicators.urlChanges > 0) {
          return true;
        }
      }
    }
    
    return false;
  },
  
  // 综合验证方法
  verifyTask(session, duration, detectionResults = null, behaviorData = null) {
    const durationSeconds = Math.round(duration / 1000);
    
    console.log(`🔍 验证任务 - 时长: ${durationSeconds}秒`);
    
    // 1. 立即拒绝过短时间
    if (durationSeconds < 15) {
      return {
        success: false,
        reason: '时间过短',
        message: `任务执行时间过短（${durationSeconds}秒），无法验证操作真实性`
      };
    }
    
    // 2. 验证真实用户交互
    const hasRealInteraction = this.validateRealUserInteraction(session, detectionResults, behaviorData);
    if (!hasRealInteraction) {
      return {
        success: false,
        reason: '无真实交互',
        message: '未检测到真实的用户操作行为'
      };
    }
    
    // 3. 智能推断验证
    const confidence = this.calculateIntelligentConfidence(session, duration);
    if (confidence < 60) {
      return {
        success: false,
        reason: '置信度不足',
        message: `智能推断置信度不足（${confidence}%，需要60%+）`
      };
    }
    
    // 4. 时间模式验证
    const timeConfidence = this.analyzeTimePattern(duration, session.selectedRequirements);
    if (timeConfidence < 25) {
      return {
        success: false,
        reason: '时间模式异常',
        message: `时间模式分析未通过（${timeConfidence}%）`
      };
    }
    
    return {
      success: true,
      confidence: Math.min(confidence, 75),
      message: `验证通过（置信度：${confidence}%）`
    };
  }
};

// 测试用例
const testCases = [
  {
    name: '恶意快速完成（4秒）',
    session: {
      startTime: Date.now() - 4000,
      selectedRequirements: ['like'],
      platform: 'bilibili'
    },
    duration: 4000,
    detectionResults: null,
    behaviorData: null,
    expectedResult: false,
    expectedReason: '时间过短'
  },
  {
    name: '正常完成但无交互证据（20秒）',
    session: {
      startTime: Date.now() - 20000,
      selectedRequirements: ['like'],
      platform: 'bilibili'
    },
    duration: 20000,
    detectionResults: null,
    behaviorData: null,
    expectedResult: false,
    expectedReason: '无真实交互'
  },
  {
    name: '有交互但时间过短（12秒）',
    session: {
      startTime: Date.now() - 12000,
      selectedRequirements: ['like'],
      platform: 'bilibili'
    },
    duration: 12000,
    detectionResults: null,
    behaviorData: {
      interactions: [
        { type: 'click', timestamp: Date.now() - 10000 },
        { type: 'click', timestamp: Date.now() - 8000 }
      ]
    },
    expectedResult: false,
    expectedReason: '时间过短'
  },
  {
    name: '正常完成有交互（30秒）',
    session: {
      startTime: Date.now() - 30000,
      selectedRequirements: ['like'],
      platform: 'bilibili'
    },
    duration: 30000,
    detectionResults: {
      detectedActions: [
        { action: 'like', confidence: 85 }
      ],
      indicators: {
        windowFocusTime: 20000,
        urlChanges: 1
      }
    },
    behaviorData: {
      interactions: [
        { type: 'click', timestamp: Date.now() - 25000 },
        { type: 'click', timestamp: Date.now() - 20000 }
      ]
    },
    expectedResult: true,
    expectedReason: '验证通过'
  },
  {
    name: '长时间但无高质量交互（45秒）',
    session: {
      startTime: Date.now() - 45000,
      selectedRequirements: ['share'],
      platform: 'douyin'
    },
    duration: 45000,
    detectionResults: {
      indicators: {
        windowFocusTime: 10000,
        urlChanges: 0
      }
    },
    behaviorData: {
      interactions: [
        { type: 'scroll', timestamp: Date.now() - 40000 }
      ]
    },
    expectedResult: false,
    expectedReason: '无真实交互'
  }
];

// 运行测试
let passedTests = 0;
let totalTests = testCases.length;

console.log(`📋 运行 ${totalTests} 个安全测试用例...\n`);

testCases.forEach((testCase, index) => {
  console.log(`🧪 测试 ${index + 1}: ${testCase.name}`);
  
  const result = SecurityFixedTaskExecutionService.verifyTask(
    testCase.session,
    testCase.duration,
    testCase.detectionResults,
    testCase.behaviorData
  );
  
  const passed = result.success === testCase.expectedResult;
  
  if (passed) {
    console.log(`   ✅ 通过 - ${result.message}`);
    passedTests++;
  } else {
    console.log(`   ❌ 失败 - 预期: ${testCase.expectedResult ? '成功' : '失败'}, 实际: ${result.success ? '成功' : '失败'}`);
    console.log(`   📝 结果: ${result.message}`);
  }
  
  console.log('');
});

// 输出测试结果
const passRate = Math.round((passedTests / totalTests) * 100);
console.log('📊 安全修复验证结果:');
console.log(`总测试数: ${totalTests}`);
console.log(`通过数: ${passedTests}`);
console.log(`失败数: ${totalTests - passedTests}`);
console.log(`通过率: ${passRate}%`);

if (passRate === 100) {
  console.log('🎉 安全漏洞修复验证成功！系统现在能够正确拒绝恶意请求。');
} else {
  console.log('⚠️  仍有安全问题需要解决。');
}

console.log('\n🔒 安全修复要点总结:');
console.log('1. ✅ 提高了时间验证阈值（最少15秒）');
console.log('2. ✅ 增加了真实用户交互验证');
console.log('3. ✅ 严格限制了智能推断的置信度');
console.log('4. ✅ 要求提供操作证明或人工审核');
console.log('5. ✅ 移除了容易被滥用的加分机制');
