Write-Host "🚀 启动互助点赞平台项目..." -ForegroundColor Green
Write-Host ""

# 切换到项目目录
Set-Location "C:\Users\<USER>\Desktop\social-media-automation"

Write-Host "📍 当前目录: $(Get-Location)" -ForegroundColor Yellow
Write-Host ""

# 检查Node.js
Write-Host "🔍 检查Node.js版本..." -ForegroundColor Cyan
try {
    $nodeVersion = node -v
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安装或不在PATH中" -ForegroundColor Red
    Write-Host "请安装Node.js 18+版本" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

# 检查npm
Write-Host "🔍 检查npm版本..." -ForegroundColor Cyan
try {
    $npmVersion = npm -v
    Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm 未安装或不在PATH中" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host ""
Write-Host "✅ 环境检查通过" -ForegroundColor Green
Write-Host ""

# 检查依赖是否安装
if (!(Test-Path "node_modules")) {
    Write-Host "📦 安装根目录依赖..." -ForegroundColor Yellow
    npm install
}

if (!(Test-Path "backend/node_modules")) {
    Write-Host "📦 安装后端依赖..." -ForegroundColor Yellow
    Set-Location backend
    npm install
    Set-Location ..
}

if (!(Test-Path "frontend/node_modules")) {
    Write-Host "📦 安装前端依赖..." -ForegroundColor Yellow
    Set-Location frontend
    npm install
    Set-Location ..
}

Write-Host ""
Write-Host "🚀 启动开发服务器..." -ForegroundColor Green
Write-Host "前端: http://localhost:5173" -ForegroundColor Cyan
Write-Host "后端: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""

# 启动项目
npm run dev
