#!/bin/bash

# 互助点赞平台 - 项目初始化脚本

set -e

echo "🚀 开始初始化互助点赞平台..."

# 检查Node.js版本
check_node_version() {
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo "❌ Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        exit 1
    fi
    
    echo "✅ Node.js 版本检查通过: $(node -v)"
}

# 检查PostgreSQL
check_postgresql() {
    if ! command -v psql &> /dev/null; then
        echo "⚠️  PostgreSQL 未安装，请确保数据库服务可用"
        echo "   可以使用 Docker: docker run --name postgres -e POSTGRES_PASSWORD=postgres123 -p 5432:5432 -d postgres:15"
    else
        echo "✅ PostgreSQL 已安装"
    fi
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    # 安装根目录依赖
    npm install
    
    # 安装后端依赖
    echo "📦 安装后端依赖..."
    cd backend && npm install && cd ..
    
    # 安装前端依赖
    echo "📦 安装前端依赖..."
    cd frontend && npm install && cd ..
    
    echo "✅ 依赖安装完成"
}

# 配置环境变量
setup_environment() {
    echo "⚙️  配置环境变量..."
    
    # 后端环境变量
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        echo "✅ 后端环境变量文件已创建: backend/.env"
        echo "   请根据需要修改数据库连接等配置"
    else
        echo "✅ 后端环境变量文件已存在"
    fi
    
    # 前端环境变量
    if [ ! -f "frontend/.env" ]; then
        cp frontend/.env.example frontend/.env
        echo "✅ 前端环境变量文件已创建: frontend/.env"
    else
        echo "✅ 前端环境变量文件已存在"
    fi
}

# 初始化数据库
setup_database() {
    echo "🗄️  初始化数据库..."
    
    cd backend
    
    # 等待数据库连接
    echo "等待数据库连接..."
    sleep 3
    
    # 运行数据库迁移
    if npm run db:migrate; then
        echo "✅ 数据库迁移完成"
    else
        echo "⚠️  数据库迁移失败，请检查数据库连接配置"
        cd ..
        return 1
    fi
    
    # 插入种子数据
    if npm run db:seed; then
        echo "✅ 种子数据插入完成"
    else
        echo "⚠️  种子数据插入失败"
    fi
    
    cd ..
}

# 构建项目
build_project() {
    echo "🔨 构建项目..."
    
    # 构建后端
    cd backend && npm run build && cd ..
    echo "✅ 后端构建完成"
    
    # 构建前端
    cd frontend && npm run build && cd ..
    echo "✅ 前端构建完成"
}

# 显示启动信息
show_startup_info() {
    echo ""
    echo "🎉 项目初始化完成！"
    echo ""
    echo "📋 接下来的步骤："
    echo "1. 检查并修改环境变量配置："
    echo "   - backend/.env (数据库连接、JWT密钥等)"
    echo "   - frontend/.env (API地址等)"
    echo ""
    echo "2. 启动开发服务器："
    echo "   npm run dev"
    echo ""
    echo "3. 访问应用："
    echo "   - 前端: http://localhost:5173"
    echo "   - 后端API: http://localhost:3000"
    echo "   - API文档: http://localhost:3000/api-docs"
    echo ""
    echo "4. 测试账号："
    echo "   - 邮箱: <EMAIL>"
    echo "   - 密码: Password123"
    echo ""
    echo "📚 更多信息请查看 README.md 和 docs/ 目录"
}

# 主函数
main() {
    echo "互助点赞平台 - 项目初始化"
    echo "================================"
    
    check_node_version
    check_postgresql
    install_dependencies
    setup_environment
    
    # 询问是否初始化数据库
    read -p "是否初始化数据库？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_database
    else
        echo "⏭️  跳过数据库初始化"
    fi
    
    # 询问是否构建项目
    read -p "是否构建项目？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        build_project
    else
        echo "⏭️  跳过项目构建"
    fi
    
    show_startup_info
}

# 运行主函数
main "$@"
