import { Router } from 'express';
import { parseVideoInfo, proxyImage } from '@/controllers/videoController';

const router = Router();

/**
 * @swagger
 * /video/parse:
 *   post:
 *     summary: 解析视频信息
 *     tags: [视频]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - url
 *               - platform
 *               - videoId
 *             properties:
 *               url:
 *                 type: string
 *                 description: 视频URL
 *                 example: "https://www.bilibili.com/video/BV1Hk7TzUEL1/"
 *               platform:
 *                 type: string
 *                 description: 视频平台
 *                 enum: [douyin, kuaishou, xiaohongshu, bilibili, youtube, tiktok]
 *                 example: "bilibili"
 *               videoId:
 *                 type: string
 *                 description: 视频ID
 *                 example: "BV1Hk7TzUEL1"
 *     responses:
 *       200:
 *         description: 视频信息解析成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "视频信息解析成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     platform:
 *                       type: string
 *                       example: "bilibili"
 *                     title:
 *                       type: string
 *                       example: "精彩视频标题"
 *                     cover:
 *                       type: string
 *                       example: "https://example.com/cover.jpg"
 *                     author:
 *                       type: string
 *                       example: "UP主名称"
 *                     description:
 *                       type: string
 *                       example: "视频描述"
 *                     duration:
 *                       type: number
 *                       example: 300
 *                     videoId:
 *                       type: string
 *                       example: "BV1Hk7TzUEL1"
 *                     originalUrl:
 *                       type: string
 *                       example: "https://www.bilibili.com/video/BV1Hk7TzUEL1/"
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/parse', parseVideoInfo);

/**
 * @swagger
 * /video/proxy/image:
 *   get:
 *     summary: 图片代理接口
 *     tags: [视频]
 *     parameters:
 *       - in: query
 *         name: url
 *         required: true
 *         schema:
 *           type: string
 *         description: 图片URL
 *         example: "https://i2.hdslb.com/bfs/archive/example.jpg"
 *     responses:
 *       200:
 *         description: 图片数据
 *         content:
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: 缺少URL参数
 *       404:
 *         description: 图片获取失败
 */
router.get('/proxy/image', proxyImage);

export default router;
