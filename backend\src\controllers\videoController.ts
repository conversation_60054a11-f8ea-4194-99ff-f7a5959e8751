import { Request, Response } from 'express';
import { asyncHandler } from '@/middleware/errorHandler';
import { ApiResponse } from '@/types';
import axios from 'axios';
import * as cheerio from 'cheerio';

// 视频平台类型
export enum VideoPlatform {
  DOUYIN = 'douyin',
  KUAISHOU = 'kuaishou', 
  XIAOHONGSHU = 'xiaohongshu',
  BILIBILI = 'bilibili',
  YOUTUBE = 'youtube',
  TIKTOK = 'tiktok',
  UNKNOWN = 'unknown'
}

// 视频信息接口
export interface VideoInfo {
  platform: VideoPlatform;
  title: string;
  cover: string;
  author: string;
  duration?: number;
  description?: string;
  videoId: string;
  originalUrl: string;
}

/**
 * 解析Bilibili视频信息
 */
async function parseBilibiliVideo(url: string, videoId: string): Promise<VideoInfo> {
  try {
    // 构建API URL
    const apiUrl = `https://api.bilibili.com/x/web-interface/view?bvid=${videoId}`;
    
    const response = await axios.get(apiUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.bilibili.com/'
      },
      timeout: 10000
    });

    if (response.data && response.data.code === 0 && response.data.data) {
      const data = response.data.data;

      // 处理封面URL，确保使用HTTPS并添加代理
      let coverUrl = data.pic || '/placeholder-cover.svg';
      if (coverUrl.startsWith('http://')) {
        coverUrl = coverUrl.replace('http://', 'https://');
      }

      // 对于Bilibili图片，使用代理避免跨域问题
      if (coverUrl.includes('hdslb.com')) {
        coverUrl = `/api/video/proxy/image?url=${encodeURIComponent(coverUrl)}`;
      }

      console.log('Bilibili封面URL:', coverUrl);

      return {
        platform: VideoPlatform.BILIBILI,
        videoId,
        originalUrl: url,
        title: data.title || '无法获取标题',
        cover: coverUrl,
        author: data.owner?.name || '未知UP主',
        description: data.desc || '',
        duration: data.duration || 0
      };
    }
  } catch (error) {
    console.error('解析Bilibili视频失败:', error);
  }

  // 返回默认信息
  return {
    platform: VideoPlatform.BILIBILI,
    videoId,
    originalUrl: url,
    title: '无法获取B站视频标题',
    cover: '/placeholder-cover.svg',
    author: '未知UP主',
    description: '',
    duration: 0
  };
}

/**
 * 解析YouTube视频信息
 */
async function parseYouTubeVideo(url: string, videoId: string): Promise<VideoInfo> {
  try {
    // 使用oEmbed API获取基本信息
    const oembedUrl = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`;
    
    const response = await axios.get(oembedUrl, {
      timeout: 10000
    });

    if (response.data) {
      const data = response.data;
      return {
        platform: VideoPlatform.YOUTUBE,
        videoId,
        originalUrl: url,
        title: data.title || '无法获取标题',
        cover: data.thumbnail_url || '/placeholder-cover.svg',
        author: data.author_name || '未知作者',
        description: '',
        duration: 0
      };
    }
  } catch (error) {
    console.error('解析YouTube视频失败:', error);
  }

  // 返回默认信息
  return {
    platform: VideoPlatform.YOUTUBE,
    videoId,
    originalUrl: url,
    title: '无法获取YouTube视频标题',
    cover: '/placeholder-cover.svg',
    author: '未知作者',
    description: '',
    duration: 0
  };
}

/**
 * 通用网页抓取解析
 */
async function parseGenericVideo(url: string, platform: VideoPlatform, videoId: string): Promise<VideoInfo> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);
    
    // 尝试从meta标签获取信息
    const title = $('meta[property="og:title"]').attr('content') || 
                  $('meta[name="twitter:title"]').attr('content') || 
                  $('title').text() || 
                  '无法获取标题';
    
    const cover = $('meta[property="og:image"]').attr('content') || 
                  $('meta[name="twitter:image"]').attr('content') || 
                  '/placeholder-cover.svg';
    
    const description = $('meta[property="og:description"]').attr('content') || 
                       $('meta[name="description"]').attr('content') || 
                       '';

    const author = $('meta[name="author"]').attr('content') || '未知作者';

    return {
      platform,
      videoId,
      originalUrl: url,
      title: title.trim(),
      cover,
      author,
      description: description.trim(),
      duration: 0
    };
  } catch (error) {
    console.error('通用解析失败:', error);
    
    // 返回默认信息
    return {
      platform,
      videoId,
      originalUrl: url,
      title: '无法获取视频标题',
      cover: '/placeholder-cover.svg',
      author: '未知作者',
      description: '',
      duration: 0
    };
  }
}

/**
 * 解析视频信息
 */
export const parseVideoInfo = asyncHandler(async (req: Request, res: Response<ApiResponse>): Promise<void> => {
  const { url, platform, videoId } = req.body;

  if (!url || !platform || !videoId) {
    res.status(400).json({ 
      success: false, 
      message: '缺少必要参数: url, platform, videoId' 
    });
    return;
  }

  try {
    let videoInfo: VideoInfo;

    switch (platform) {
      case VideoPlatform.BILIBILI:
        videoInfo = await parseBilibiliVideo(url, videoId);
        break;
      case VideoPlatform.YOUTUBE:
        videoInfo = await parseYouTubeVideo(url, videoId);
        break;
      default:
        // 对于其他平台，使用通用解析
        videoInfo = await parseGenericVideo(url, platform, videoId);
        break;
    }

    res.json({
      success: true,
      message: '视频信息解析成功',
      data: videoInfo
    });
  } catch (error) {
    console.error('视频解析错误:', error);
    res.status(500).json({
      success: false,
      message: '视频信息解析失败'
    });
  }
});

/**
 * 图片代理接口 - 解决跨域问题
 */
export const proxyImage = asyncHandler(async (req: Request, res: Response) => {
  const { url } = req.query;

  if (!url || typeof url !== 'string') {
    return res.status(400).json({
      success: false,
      message: '缺少图片URL参数'
    });
  }

  try {
    const response = await axios.get(url, {
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.bilibili.com/'
      },
      timeout: 10000
    });

    // 设置响应头
    res.set({
      'Content-Type': response.headers['content-type'] || 'image/jpeg',
      'Cache-Control': 'public, max-age=86400', // 缓存24小时
      'Access-Control-Allow-Origin': '*'
    });

    // 流式传输图片
    response.data.pipe(res);
  } catch (error) {
    console.error('图片代理失败:', error);
    res.status(404).json({
      success: false,
      message: '图片获取失败'
    });
  }
});
