<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>任务验证系统测试</h1>
    
    <div class="test-section">
        <h2>跨域验证测试</h2>
        <p>测试改进后的跨域验证逻辑，模拟用户在外部视频平台完成任务的场景。</p>
        
        <button onclick="testCrossDomainVerification()">测试跨域验证</button>
        <button onclick="testSameDomainVerification()">测试同域验证</button>
        <button onclick="testQuickCompletion()">测试快速完成</button>
        <button onclick="testReasonableTime()">测试合理时间</button>
        
        <div id="testResults"></div>
        <div id="testLogs" class="log"></div>
    </div>

    <script type="module">
        // 模拟导入验证服务
        const mockTaskExecutionService = {
            // 模拟跨域验证方法
            performCrossDomainVerification(session, duration, durationSeconds) {
                const isQuickCompletion = duration < 5000; // 5秒阈值
                const isReasonableTime = duration >= 10000; // 至少10秒
                const isNotTooLong = duration <= 300000; // 不超过5分钟

                if (isQuickCompletion) {
                    return {
                        success: false,
                        completedRequirements: [],
                        failedRequirements: session.selectedRequirements,
                        pointsEarned: 0,
                        message: `任务完成时间过短（${durationSeconds}秒），请确保实际执行了所需操作后再关闭窗口。`,
                        proofRequired: true,
                        needsManualReview: true
                    };
                }

                if (!isNotTooLong) {
                    return {
                        success: false,
                        completedRequirements: [],
                        failedRequirements: session.selectedRequirements,
                        pointsEarned: 0,
                        message: '任务执行时间过长，可能存在异常，请重新完成。',
                        proofRequired: false,
                        needsManualReview: false
                    };
                }

                if (isReasonableTime) {
                    const completedRequirements = [...session.selectedRequirements];
                    const pointsEarned = this.calculatePoints(completedRequirements);
                    
                    return {
                        success: true,
                        completedRequirements,
                        failedRequirements: [],
                        pointsEarned,
                        message: `任务已完成（执行时间：${durationSeconds}秒）。由于技术限制无法完全验证操作，如有异议请联系客服。`,
                        needsManualReview: false,
                        proofRequired: false
                    };
                }

                return {
                    success: false,
                    completedRequirements: [],
                    failedRequirements: session.selectedRequirements,
                    pointsEarned: 0,
                    message: `任务执行时间较短（${durationSeconds}秒），请提供完成截图作为证明，或重新执行任务并停留更长时间。`,
                    proofRequired: true,
                    needsManualReview: true
                };
            },

            calculatePoints(requirements) {
                const pointsMap = {
                    like: 10,
                    share: 15,
                    comment: 20,
                    follow: 25
                };
                return requirements.reduce((total, req) => total + (pointsMap[req] || 0), 0);
            }
        };

        // 测试函数
        window.testCrossDomainVerification = function() {
            log('开始测试跨域验证...');
            
            const session = {
                selectedRequirements: ['like'],
                isTrackingBehavior: false
            };
            
            // 测试合理时间（15秒）
            const duration = 15000;
            const durationSeconds = Math.round(duration / 1000);
            
            const result = mockTaskExecutionService.performCrossDomainVerification(session, duration, durationSeconds);
            
            displayResult('跨域验证测试（15秒）', result);
            log(`测试结果: ${result.success ? '成功' : '失败'} - ${result.message}`);
        };

        window.testSameDomainVerification = function() {
            log('开始测试同域验证...');
            
            // 模拟同域场景（有行为数据）
            const behaviorData = {
                metrics: {
                    clickCount: 3,
                    mouseMovements: 25,
                    totalTime: 12000
                }
            };
            
            const result = {
                success: true,
                completedRequirements: ['like'],
                failedRequirements: [],
                pointsEarned: 10,
                message: '任务验证成功，检测到真实用户行为。'
            };
            
            displayResult('同域验证测试（有行为数据）', result);
            log(`测试结果: ${result.success ? '成功' : '失败'} - ${result.message}`);
        };

        window.testQuickCompletion = function() {
            log('开始测试快速完成场景...');
            
            const session = {
                selectedRequirements: ['like'],
                isTrackingBehavior: false
            };
            
            // 测试快速完成（3秒）
            const duration = 3000;
            const durationSeconds = Math.round(duration / 1000);
            
            const result = mockTaskExecutionService.performCrossDomainVerification(session, duration, durationSeconds);
            
            displayResult('快速完成测试（3秒）', result);
            log(`测试结果: ${result.success ? '成功' : '失败'} - ${result.message}`);
        };

        window.testReasonableTime = function() {
            log('开始测试合理时间场景...');
            
            const session = {
                selectedRequirements: ['like', 'share'],
                isTrackingBehavior: false
            };
            
            // 测试合理时间（30秒）
            const duration = 30000;
            const durationSeconds = Math.round(duration / 1000);
            
            const result = mockTaskExecutionService.performCrossDomainVerification(session, duration, durationSeconds);
            
            displayResult('合理时间测试（30秒，多任务）', result);
            log(`测试结果: ${result.success ? '成功' : '失败'} - ${result.message}`);
        };

        function displayResult(testName, result) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${result.success ? 'success' : 'error'}`;
            
            resultDiv.innerHTML = `
                <h4>${testName}</h4>
                <p><strong>结果:</strong> ${result.success ? '✅ 成功' : '❌ 失败'}</p>
                <p><strong>消息:</strong> ${result.message}</p>
                <p><strong>完成任务:</strong> ${result.completedRequirements.join(', ') || '无'}</p>
                <p><strong>失败任务:</strong> ${result.failedRequirements.join(', ') || '无'}</p>
                <p><strong>获得积分:</strong> ${result.pointsEarned}</p>
                <p><strong>需要证明:</strong> ${result.proofRequired ? '是' : '否'}</p>
                <p><strong>需要人工审核:</strong> ${result.needsManualReview ? '是' : '否'}</p>
            `;
            
            resultsDiv.appendChild(resultDiv);
        }

        function log(message) {
            const logsDiv = document.getElementById('testLogs');
            const timestamp = new Date().toLocaleTimeString();
            logsDiv.textContent += `[${timestamp}] ${message}\n`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        // 初始化
        log('验证系统测试页面已加载');
        log('点击按钮开始测试改进后的验证逻辑');
    </script>
</body>
</html>
