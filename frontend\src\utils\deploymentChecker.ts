/**
 * 扩展式检测器部署状态检查工具
 */

export interface DeploymentStatus {
  isDeployed: boolean;
  components: {
    browserExtensionDetector: boolean;
    realCrossDomainDetector: boolean;
    taskExecutionService: boolean;
    testPages: boolean;
  };
  features: {
    extensionDetection: boolean;
    realDetection: boolean;
    hybridDetection: boolean;
    advancedValidation: boolean;
  };
  performance: {
    detectionAccuracy: number;
    responseTime: number;
    crossDomainSupport: boolean;
  };
  issues: string[];
  recommendations: string[];
}

class DeploymentChecker {
  /**
   * 检查扩展式检测器部署状态
   */
  async checkDeploymentStatus(): Promise<DeploymentStatus> {
    console.log('🔍 开始检查扩展式检测器部署状态...');

    const status: DeploymentStatus = {
      isDeployed: false,
      components: {
        browserExtensionDetector: false,
        realCrossDomainDetector: false,
        taskExecutionService: false,
        testPages: false
      },
      features: {
        extensionDetection: false,
        realDetection: false,
        hybridDetection: false,
        advancedValidation: false
      },
      performance: {
        detectionAccuracy: 0,
        responseTime: 0,
        crossDomainSupport: false
      },
      issues: [],
      recommendations: []
    };

    // 检查组件
    await this.checkComponents(status);

    // 检查功能
    await this.checkFeatures(status);

    // 检查性能
    await this.checkPerformance(status);

    // 生成建议
    this.generateRecommendations(status);

    // 计算总体部署状态
    status.isDeployed = this.calculateOverallStatus(status);

    console.log('✅ 部署状态检查完成');
    return status;
  }

  /**
   * 检查组件状态
   */
  private async checkComponents(status: DeploymentStatus): Promise<void> {
    console.log('🔧 检查组件状态...');

    try {
      // 检查浏览器扩展检测器
      const { browserExtensionDetector } = await import('../services/browserExtensionDetector');
      if (browserExtensionDetector) {
        status.components.browserExtensionDetector = true;
        console.log('✅ 浏览器扩展检测器已加载');
      }
    } catch (error) {
      status.issues.push('浏览器扩展检测器加载失败');
      console.log('❌ 浏览器扩展检测器加载失败:', error);
    }

    try {
      // 检查真实跨域检测器
      const { realCrossDomainDetector } = await import('../services/realCrossDomainDetector');
      if (realCrossDomainDetector) {
        status.components.realCrossDomainDetector = true;
        console.log('✅ 真实跨域检测器已加载');
      }
    } catch (error) {
      status.issues.push('真实跨域检测器加载失败');
      console.log('❌ 真实跨域检测器加载失败:', error);
    }

    try {
      // 检查任务执行服务
      const { taskExecutionService } = await import('../services/taskExecutionService');
      if (taskExecutionService) {
        status.components.taskExecutionService = true;
        console.log('✅ 任务执行服务已更新');
      }
    } catch (error) {
      status.issues.push('任务执行服务加载失败');
      console.log('❌ 任务执行服务加载失败:', error);
    }

    // 检查测试页面
    try {
      const response = await fetch('/test-extension-detector.html');
      if (response.ok) {
        status.components.testPages = true;
        console.log('✅ 测试页面可访问');
      }
    } catch (error) {
      status.issues.push('测试页面不可访问');
      console.log('❌ 测试页面不可访问:', error);
    }
  }

  /**
   * 检查功能状态
   */
  private async checkFeatures(status: DeploymentStatus): Promise<void> {
    console.log('⚙️ 检查功能状态...');

    try {
      // 测试扩展式检测
      const { browserExtensionDetector } = await import('../services/browserExtensionDetector');
      
      // 创建测试窗口
      const testWindow = window.open('about:blank', '_blank', 'width=100,height=100');
      if (testWindow) {
        browserExtensionDetector.startExtensionDetection(
          'test-session',
          testWindow,
          'test',
          ['like']
        );
        
        // 等待一下然后检查结果
        setTimeout(() => {
          const result = browserExtensionDetector.getExtensionDetectionResult('test-session');
          if (result) {
            status.features.extensionDetection = true;
            console.log('✅ 扩展式检测功能正常');
          }
          
          browserExtensionDetector.stopExtensionDetection('test-session');
          testWindow.close();
        }, 1000);
      }
    } catch (error) {
      status.issues.push('扩展式检测功能测试失败');
      console.log('❌ 扩展式检测功能测试失败:', error);
    }

    try {
      // 测试真实检测
      const { realCrossDomainDetector } = await import('../services/realCrossDomainDetector');
      
      const testWindow = window.open('about:blank', '_blank', 'width=100,height=100');
      if (testWindow) {
        realCrossDomainDetector.startRealDetection(
          'test-real-session',
          testWindow,
          'test',
          ['like']
        );
        
        setTimeout(() => {
          const result = realCrossDomainDetector.getRealDetectionResult('test-real-session');
          if (result) {
            status.features.realDetection = true;
            console.log('✅ 真实检测功能正常');
          }
          
          realCrossDomainDetector.stopRealDetection('test-real-session');
          testWindow.close();
        }, 1000);
      }
    } catch (error) {
      status.issues.push('真实检测功能测试失败');
      console.log('❌ 真实检测功能测试失败:', error);
    }

    // 检查混合检测
    if (status.features.extensionDetection && status.features.realDetection) {
      status.features.hybridDetection = true;
      console.log('✅ 混合检测功能可用');
    }

    // 检查高级验证
    if (status.components.taskExecutionService) {
      status.features.advancedValidation = true;
      console.log('✅ 高级验证功能可用');
    }
  }

  /**
   * 检查性能状态
   */
  private async checkPerformance(status: DeploymentStatus): Promise<void> {
    console.log('📊 检查性能状态...');

    // 模拟性能测试
    const startTime = Date.now();
    
    try {
      // 测试检测准确率
      if (status.features.extensionDetection) {
        status.performance.detectionAccuracy += 40; // 扩展式检测贡献40%
      }
      
      if (status.features.realDetection) {
        status.performance.detectionAccuracy += 35; // 真实检测贡献35%
      }
      
      if (status.features.hybridDetection) {
        status.performance.detectionAccuracy += 20; // 混合检测额外贡献20%
      }
      
      // 测试响应时间
      const endTime = Date.now();
      status.performance.responseTime = endTime - startTime;
      
      // 检查跨域支持
      if (status.features.extensionDetection || status.features.realDetection) {
        status.performance.crossDomainSupport = true;
      }
      
      console.log(`📊 性能检查完成 - 准确率: ${status.performance.detectionAccuracy}%, 响应时间: ${status.performance.responseTime}ms`);
    } catch (error) {
      status.issues.push('性能测试失败');
      console.log('❌ 性能测试失败:', error);
    }
  }

  /**
   * 生成建议
   */
  private generateRecommendations(status: DeploymentStatus): void {
    console.log('💡 生成部署建议...');

    if (!status.components.browserExtensionDetector) {
      status.recommendations.push('需要部署浏览器扩展检测器');
    }

    if (!status.components.realCrossDomainDetector) {
      status.recommendations.push('需要部署真实跨域检测器');
    }

    if (!status.features.extensionDetection) {
      status.recommendations.push('扩展式检测功能需要调试');
    }

    if (!status.features.hybridDetection) {
      status.recommendations.push('建议启用混合检测以获得最佳效果');
    }

    if (status.performance.detectionAccuracy < 80) {
      status.recommendations.push('检测准确率偏低，建议优化检测算法');
    }

    if (status.performance.responseTime > 500) {
      status.recommendations.push('响应时间较长，建议优化性能');
    }

    if (!status.performance.crossDomainSupport) {
      status.recommendations.push('跨域支持不完整，建议检查配置');
    }

    if (status.recommendations.length === 0) {
      status.recommendations.push('部署状态良好，建议进行生产环境测试');
    }
  }

  /**
   * 计算总体部署状态
   */
  private calculateOverallStatus(status: DeploymentStatus): boolean {
    const componentScore = Object.values(status.components).filter(Boolean).length / Object.keys(status.components).length;
    const featureScore = Object.values(status.features).filter(Boolean).length / Object.keys(status.features).length;
    const performanceScore = (
      (status.performance.detectionAccuracy / 100) +
      (status.performance.responseTime < 500 ? 1 : 0) +
      (status.performance.crossDomainSupport ? 1 : 0)
    ) / 3;

    const overallScore = (componentScore + featureScore + performanceScore) / 3;
    
    console.log(`📊 总体评分: ${(overallScore * 100).toFixed(1)}%`);
    
    return overallScore >= 0.8; // 80%以上认为部署成功
  }

  /**
   * 生成部署报告
   */
  generateReport(status: DeploymentStatus): string {
    const report = `
# 🔌 扩展式检测器部署报告

## 📊 部署状态: ${status.isDeployed ? '✅ 成功' : '❌ 未完成'}

### 🔧 组件状态
- 浏览器扩展检测器: ${status.components.browserExtensionDetector ? '✅' : '❌'}
- 真实跨域检测器: ${status.components.realCrossDomainDetector ? '✅' : '❌'}
- 任务执行服务: ${status.components.taskExecutionService ? '✅' : '❌'}
- 测试页面: ${status.components.testPages ? '✅' : '❌'}

### ⚙️ 功能状态
- 扩展式检测: ${status.features.extensionDetection ? '✅' : '❌'}
- 真实检测: ${status.features.realDetection ? '✅' : '❌'}
- 混合检测: ${status.features.hybridDetection ? '✅' : '❌'}
- 高级验证: ${status.features.advancedValidation ? '✅' : '❌'}

### 📊 性能指标
- 检测准确率: ${status.performance.detectionAccuracy}%
- 响应时间: ${status.performance.responseTime}ms
- 跨域支持: ${status.performance.crossDomainSupport ? '✅' : '❌'}

### 🚨 问题列表
${status.issues.length > 0 ? status.issues.map(issue => `- ❌ ${issue}`).join('\n') : '- ✅ 无问题'}

### 💡 建议
${status.recommendations.map(rec => `- 💡 ${rec}`).join('\n')}

---
报告生成时间: ${new Date().toLocaleString()}
    `;

    return report.trim();
  }

  /**
   * 运行快速检查
   */
  async quickCheck(): Promise<boolean> {
    try {
      const { browserExtensionDetector } = await import('../services/browserExtensionDetector');
      const { realCrossDomainDetector } = await import('../services/realCrossDomainDetector');
      
      return !!(browserExtensionDetector && realCrossDomainDetector);
    } catch (error) {
      return false;
    }
  }
}

export const deploymentChecker = new DeploymentChecker();
