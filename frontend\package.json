{"name": "mutual-like-frontend", "version": "1.0.0", "description": "互助点赞平台前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --report-unused-disable-directives --max-warnings 50", "lint:fix": "eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^16.2.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8"}, "engines": {"node": ">=18.0.0"}}