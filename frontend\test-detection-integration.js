// 测试改进的检测机制集成
console.log('🚀 开始测试改进的检测机制...');

// 模拟任务执行会话
const mockSession = {
  id: 'test-session-' + Date.now(),
  taskId: 1,
  selectedRequirements: ['like'],
  windowRef: null,
  startTime: Date.now(),
  minDuration: 5000,
  status: 'monitoring',
  platform: 'bilibili',
  isTrackingBehavior: false
};

// 测试智能推断检测
function testIntelligentDetection() {
  console.log('🧠 测试智能推断检测...');
  
  // 模拟智能检测结果
  const intelligentResult = {
    detectedActions: [{
      action: 'like',
      platform: 'bilibili',
      timestamp: Date.now(),
      confidence: 75,
      method: 'intelligent_inference'
    }],
    confidence: 75,
    method: 'intelligent_cross_domain',
    indicators: {
      timeSpent: 18000,
      userActivity: {
        focusTime: 12000,
        interactions: 5,
        windowChanges: 1
      },
      platformOptimized: true
    }
  };
  
  console.log('✅ 智能检测结果:', intelligentResult);
  
  // 模拟处理检测结果
  const processedResult = processDetectionResults(mockSession, intelligentResult, 18);
  console.log('📊 处理后的结果:', processedResult);
  
  return processedResult;
}

// 测试跨域检测
function testCrossDomainDetection() {
  console.log('🌐 测试跨域检测...');
  
  const crossDomainResult = {
    detectedActions: [{
      action: 'like',
      platform: 'bilibili',
      timestamp: Date.now(),
      confidence: 65,
      method: 'cross_domain_inference'
    }],
    confidence: 65,
    method: 'intelligent_cross_domain'
  };
  
  console.log('✅ 跨域检测结果:', crossDomainResult);
  
  const processedResult = processDetectionResults(mockSession, crossDomainResult, 22);
  console.log('📊 处理后的结果:', processedResult);
  
  return processedResult;
}

// 测试检测失败场景
function testDetectionFailure() {
  console.log('❌ 测试检测失败场景...');
  
  const failureResult = {
    detectedActions: [],
    confidence: 25,
    method: 'intelligent_cross_domain',
    failureReason: '用户活动不足，无法确认操作完成'
  };
  
  console.log('⚠️ 失败检测结果:', failureResult);
  
  const processedResult = processDetectionResults(mockSession, failureResult, 8);
  console.log('📊 处理后的结果:', processedResult);
  
  return processedResult;
}

// 模拟检测结果处理函数
function processDetectionResults(session, detectionResults, durationSeconds) {
  const detectedActions = detectionResults.detectedActions || [];
  const confidence = detectionResults.confidence || 0;
  const method = detectionResults.method || 'unknown';
  
  const completedRequirements = [];
  const failedRequirements = [];

  console.log(`🔍 处理检测结果 - 方法: ${method}, 置信度: ${confidence}%, 检测到 ${detectedActions.length} 个操作`);

  // 检查每个要求是否被检测到
  session.selectedRequirements.forEach(requirement => {
    const detected = detectedActions.find(action => action.action === requirement);
    if (detected) {
      // 检查置信度阈值
      const actionConfidence = detected.confidence || confidence;
      if (actionConfidence >= 50) { // 50%以上置信度才认为完成
        completedRequirements.push(requirement);
        console.log(`✅ 检测到操作: ${requirement} (置信度: ${actionConfidence}%)`);
      } else {
        failedRequirements.push(requirement);
        console.log(`❌ 操作置信度不足: ${requirement} (置信度: ${actionConfidence}%)`);
      }
    } else {
      failedRequirements.push(requirement);
      console.log(`❌ 未检测到操作: ${requirement}`);
    }
  });

  const pointsEarned = calculatePoints(completedRequirements);
  const successRate = completedRequirements.length / session.selectedRequirements.length;

  let message = '';
  let needsManualReview = false;
  let proofRequired = false;

  if (method === 'intelligent_cross_domain') {
    // 智能推断结果
    if (successRate === 1 && confidence >= 70) {
      message = `🧠 任务智能验证成功！推断您已完成所有操作（执行时间：${durationSeconds}秒，置信度：${confidence}%）`;
    } else if (successRate > 0 && confidence >= 60) {
      message = `🔍 部分任务智能验证成功。推断已完成：${completedRequirements.join('、')}（执行时间：${durationSeconds}秒，置信度：${confidence}%）`;
      needsManualReview = confidence < 80;
    } else if (confidence >= 40) {
      message = `⚠️ 检测到用户活动但无法确认具体操作。建议提供完成截图或重新执行任务（执行时间：${durationSeconds}秒）`;
      needsManualReview = true;
      proofRequired = true;
    } else {
      message = `❌ 未检测到明显的用户操作。请确保您实际执行了所需操作：${session.selectedRequirements.join('、')}`;
      needsManualReview = true;
      proofRequired = true;
    }
  } else {
    // 传统检测结果
    if (successRate === 1) {
      message = `🎉 任务自动验证成功！检测到所有要求的操作（执行时间：${durationSeconds}秒）`;
    } else if (successRate > 0) {
      message = `✅ 部分任务自动验证成功（${completedRequirements.length}/${session.selectedRequirements.length}）。已检测到：${completedRequirements.join('、')}`;
      needsManualReview = true;
    } else {
      message = `❌ 未检测到任何要求的操作。请确保您实际执行了所需操作：${session.selectedRequirements.join('、')}`;
      needsManualReview = true;
      proofRequired = true;
    }
  }

  return {
    success: completedRequirements.length > 0,
    completedRequirements,
    failedRequirements,
    pointsEarned,
    message,
    needsManualReview,
    proofRequired
  };
}

// 计算积分
function calculatePoints(requirements) {
  const pointsMap = {
    like: 10,
    share: 15,
    comment: 20,
    follow: 25
  };
  return requirements.reduce((total, req) => total + (pointsMap[req] || 0), 0);
}

// 运行所有测试
function runAllTests() {
  console.log('🎯 开始运行所有检测测试...\n');
  
  const results = [];
  
  console.log('='.repeat(50));
  results.push(testIntelligentDetection());
  
  console.log('\n' + '='.repeat(50));
  results.push(testCrossDomainDetection());
  
  console.log('\n' + '='.repeat(50));
  results.push(testDetectionFailure());
  
  console.log('\n' + '='.repeat(50));
  console.log('📈 测试总结:');
  
  const successfulTests = results.filter(r => r.success).length;
  const totalTests = results.length;
  
  console.log(`✅ 成功测试: ${successfulTests}/${totalTests}`);
  console.log(`📊 成功率: ${Math.round(successfulTests / totalTests * 100)}%`);
  
  results.forEach((result, index) => {
    const testNames = ['智能推断测试', '跨域检测测试', '检测失败测试'];
    console.log(`${index + 1}. ${testNames[index]}: ${result.success ? '✅ 成功' : '❌ 失败'} - ${result.message}`);
  });
  
  console.log('\n🎉 所有测试完成！改进的检测机制运行正常。');
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.runDetectionTests = runAllTests;
  console.log('💡 在浏览器控制台中运行 runDetectionTests() 来执行测试');
} else {
  // 在Node.js环境中直接运行
  runAllTests();
}
