import { FileDB } from '@/services/fileDatabase';
import { User, SocialAccounts } from '@/types';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

export class UserModel {
  /**
   * 根据ID查找用户
   */
  static async findById(id: number): Promise<User | null> {
    return FileDB.getById<User>('users', id);
  }

  /**
   * 根据邮箱查找用户
   */
  static async findByEmail(email: string): Promise<User | null> {
    return FileDB.findOne<User>('users', user => user.email === email);
  }

  /**
   * 根据用户名查找用户
   */
  static async findByUsername(username: string): Promise<User | null> {
    return FileDB.findOne<User>('users', user => user.username === username);
  }

  /**
   * 创建新用户
   */
  static async create(userData: {
    username: string;
    email: string;
    password: string;
    points_balance?: number;
  }): Promise<User> {
    const hashedPassword = await bcrypt.hash(userData.password, 12);
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');

    const newUser: Omit<User, 'id' | 'created_at' | 'updated_at' | 'is_active' | 'email_verified' | 'social_accounts'> & { password_hash: string } = {
      username: userData.username,
      email: userData.email,
      password_hash: hashedPassword,
      points_balance: userData.points_balance || 100,
      email_verification_token: emailVerificationToken,
    };
    
    const now = new Date();
    
    // Casting to any to satisfy the create method signature which is generic
    return FileDB.create<User>('users', {
        ...newUser,
        is_active: true,
        email_verified: false,
        social_accounts: {},
        created_at: now,
        updated_at: now,
    } as any);
  }

  /**
   * 验证密码
   */
  static async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  /**
   * 更新用户信息
   */
  static async update(id: number, updateData: Partial<User>): Promise<User | null> {
    return FileDB.update<User>('users', id, {
        ...updateData,
        updated_at: new Date(),
    });
  }

  /**
   * 更新积分余额
   */
  static async updatePointsBalance(id: number, amount: number): Promise<User | null> {
    const user = await this.findById(id);
    if (!user) return null;
    
    return FileDB.update<User>('users', id, { 
        points_balance: user.points_balance + amount
    });
  }

  /**
   * 验证邮箱
   */
  static async verifyEmail(token: string): Promise<User | null> {
    const user = await FileDB.findOne<User>('users', u => u.email_verification_token === token);
    if (!user) return null;

    return FileDB.update<User>('users', user.id, {
        email_verified: true,
        email_verification_token: null,
        updated_at: new Date(),
    } as any);
  }

  /**
   * 设置密码重置令牌
   */
  static async setPasswordResetToken(email: string): Promise<string | null> {
    const user = await this.findByEmail(email);
    if (!user) return null;

    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 3600000); // 1小时后过期

    await FileDB.update<User>('users', user.id, {
        password_reset_token: token,
        password_reset_expires: expires,
        updated_at: new Date(),
    });

    return token;
  }

  /**
   * 重置密码
   */
  static async resetPassword(token: string, newPassword: string): Promise<User | null> {
    const user = await FileDB.findOne<User>('users', u => u.password_reset_token === token && u.password_reset_expires! > new Date());
    if(!user) return null;

    const hashedPassword = await bcrypt.hash(newPassword, 12);

    return FileDB.update<User>('users', user.id, {
        password_hash: hashedPassword,
        password_reset_token: null,
        password_reset_expires: null,
        updated_at: new Date(),
    } as any);
  }

  /**
   * 更新社交账号绑定
   */
  static async updateSocialAccounts(id: number, socialAccounts: SocialAccounts): Promise<User | null> {
    return FileDB.update<User>('users', id, {
        social_accounts: socialAccounts,
        updated_at: new Date(),
    });
  }

  /**
   * 更新最后登录信息
   */
  static async updateLastLogin(id: number, ip: string): Promise<void> {
    await FileDB.update<User>('users', id, {
        last_login_ip: ip,
        last_login_at: new Date(),
        updated_at: new Date(),
    });
  }

  /**
   * 检查用户名是否存在
   */
  static async isUsernameExists(username: string): Promise<boolean> {
    const user = await this.findByUsername(username);
    return !!user;
  }

  /**
   * 检查邮箱是否存在
   */
  static async isEmailExists(email: string): Promise<boolean> {
    const user = await this.findByEmail(email);
    return !!user;
  }

  /**
   * 获取用户统计数据
   */
  static async getUserStats(id: number): Promise<{
    totalTasksPublished: number;
    totalTasksCompleted: number;
    totalPointsEarned: number;
    totalPointsSpent: number;
  }> {
    const tasks = await FileDB.find('tasks', (task: any) => task.publisher_id === id);
    const completed = await FileDB.find('taskExecutions', (exec: any) => exec.executor_id === id && exec.status === 'completed');
    const earned = await FileDB.find('pointTransactions', (pt: any) => pt.user_id === id && pt.transaction_type === 'reward');
    const spent = await FileDB.find('pointTransactions', (pt: any) => pt.user_id === id && pt.transaction_type === 'cost');

    return {
      totalTasksPublished: tasks.length,
      totalTasksCompleted: completed.length,
      totalPointsEarned: earned.reduce((sum, pt: any) => sum + pt.amount, 0),
      totalPointsSpent: Math.abs(spent.reduce((sum, pt: any) => sum + pt.amount, 0)),
    };
  }
}
