/**
 * 测试脚本：验证任务检测系统的改进
 * 
 * 运行方式：在浏览器控制台中执行此脚本
 */

// 模拟测试数据
const testScenarios = [
  {
    name: 'B站分享任务 - 成功检测',
    platform: 'bilibili',
    requirements: ['share'],
    duration: 25000,
    mockDetections: [
      { action: 'share', confidence: 85, method: 'modal_detection' }
    ],
    expected: 'success'
  },
  {
    name: '抖音快速点赞 - 时间过短',
    platform: 'douyin',
    requirements: ['like'],
    duration: 3000,
    mockDetections: [],
    expected: 'failure'
  },
  {
    name: '跨域智能推断 - 分享任务',
    platform: 'douyin',
    requirements: ['share'],
    duration: 30000,
    mockDetections: [],
    expected: 'success'
  }
];

// 测试函数
async function testDetectionImprovements() {
  console.log('🧪 开始测试任务检测系统改进...\n');
  
  let passedTests = 0;
  let totalTests = testScenarios.length;
  
  for (let i = 0; i < testScenarios.length; i++) {
    const scenario = testScenarios[i];
    console.log(`📋 测试 ${i + 1}/${totalTests}: ${scenario.name}`);
    
    try {
      // 创建模拟会话
      const mockSession = {
        id: `test_${Date.now()}_${i}`,
        taskId: 1,
        selectedRequirements: scenario.requirements,
        windowRef: null,
        startTime: Date.now() - scenario.duration,
        minDuration: 5000,
        status: 'completed',
        platform: scenario.platform,
        isTrackingBehavior: false
      };
      
      // 模拟检测结果（如果有）
      if (scenario.mockDetections && scenario.mockDetections.length > 0) {
        // 这里应该调用实际的检测服务，但在测试环境中我们模拟结果
        console.log(`   🔍 模拟检测结果: ${scenario.mockDetections.map(d => `${d.action}(${d.confidence}%)`).join(', ')}`);
      }
      
      // 分析时间模式
      const timeAnalysis = analyzeTimePattern(scenario.duration, scenario.requirements);
      console.log(`   ⏱️  时间分析: ${timeAnalysis.analysis} (置信度: ${timeAnalysis.confidence}%)`);
      
      // 分析任务特定模式
      for (const requirement of scenario.requirements) {
        const taskAnalysis = analyzeTaskSpecificPattern(requirement, scenario.duration, scenario.platform);
        console.log(`   📊 ${requirement}任务分析: ${taskAnalysis.reason} (置信度: ${taskAnalysis.confidence}%)`);
      }
      
      // 计算综合置信度
      let totalConfidence = timeAnalysis.confidence;
      
      // 添加平台加分
      const platformBonus = getPlatformReliabilityBonus(scenario.platform);
      totalConfidence += platformBonus;
      console.log(`   🏢 平台加分: +${platformBonus}%`);
      
      // 添加检测结果加分
      if (scenario.mockDetections && scenario.mockDetections.length > 0) {
        const detectionBonus = Math.max(...scenario.mockDetections.map(d => d.confidence)) * 0.5;
        totalConfidence += detectionBonus;
        console.log(`   🎯 检测加分: +${Math.round(detectionBonus)}%`);
      }
      
      totalConfidence = Math.min(totalConfidence, 100);
      
      // 判断结果
      const isSuccess = totalConfidence >= 60;
      const actualResult = isSuccess ? 'success' : 'failure';
      
      console.log(`   📈 综合置信度: ${Math.round(totalConfidence)}%`);
      console.log(`   🎯 预期结果: ${scenario.expected}, 实际结果: ${actualResult}`);
      
      // 检查测试是否通过
      const testPassed = actualResult === scenario.expected;
      if (testPassed) {
        console.log(`   ✅ 测试通过\n`);
        passedTests++;
      } else {
        console.log(`   ❌ 测试失败\n`);
      }
      
    } catch (error) {
      console.log(`   💥 测试出错: ${error.message}\n`);
    }
  }
  
  // 输出测试摘要
  const passRate = Math.round((passedTests / totalTests) * 100);
  console.log('📊 测试摘要:');
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过数: ${passedTests}`);
  console.log(`失败数: ${totalTests - passedTests}`);
  console.log(`通过率: ${passRate}%`);
  
  if (passRate >= 80) {
    console.log('🎉 检测系统改进验证成功！');
  } else {
    console.log('⚠️  检测系统需要进一步优化');
  }
}

// 辅助函数：分析时间模式
function analyzeTimePattern(duration, requirements) {
  const durationSeconds = duration / 1000;
  let confidence = 0;
  let analysis = '';

  if (durationSeconds < 3) {
    confidence = 0;
    analysis = '时间过短，不太可能完成任何操作';
  } else if (durationSeconds < 8) {
    confidence = 20;
    analysis = '时间较短，可能完成简单操作';
  } else if (durationSeconds < 30) {
    confidence = 50;
    analysis = '时间适中，可能完成基本操作';
  } else if (durationSeconds < 120) {
    confidence = 70;
    analysis = '时间充足，很可能完成所需操作';
  } else {
    confidence = 30;
    analysis = '时间过长，可能存在其他活动';
  }

  return { confidence: Math.max(0, confidence), analysis };
}

// 辅助函数：分析任务特定模式
function analyzeTaskSpecificPattern(taskType, duration, platform) {
  const durationSeconds = duration / 1000;
  
  const taskTimeRanges = {
    like: { min: 2, max: 15, optimal: 5 },
    share: { min: 8, max: 60, optimal: 20 },
    comment: { min: 15, max: 120, optimal: 45 },
    follow: { min: 3, max: 30, optimal: 10 }
  };

  const range = taskTimeRanges[taskType];
  if (!range) {
    return { likely: false, confidence: 0, reason: '未知任务类型' };
  }

  let confidence = 0;
  let reason = '';

  if (durationSeconds >= range.min && durationSeconds <= range.max) {
    const distanceFromOptimal = Math.abs(durationSeconds - range.optimal);
    const maxDistance = Math.max(range.optimal - range.min, range.max - range.optimal);
    confidence = 70 - (distanceFromOptimal / maxDistance) * 30;
    reason = `时间在${taskType}操作的合理范围内`;
  } else if (durationSeconds < range.min) {
    confidence = 20;
    reason = `时间可能不足以完成${taskType}操作`;
  } else {
    confidence = 40;
    reason = `时间超出${taskType}操作的典型范围，但仍有可能完成`;
  }

  return {
    likely: confidence >= 50,
    confidence: Math.round(confidence),
    reason
  };
}

// 辅助函数：获取平台可靠性加分
function getPlatformReliabilityBonus(platform) {
  const bonuses = {
    'bilibili': 15,
    'douyin': 10,
    'kuaishou': 8,
    'xiaohongshu': 12,
    'youtube': 10
  };
  return bonuses[platform] || 5;
}

// 运行测试
console.log('🚀 任务检测系统改进测试');
console.log('=====================================');
testDetectionImprovements();
