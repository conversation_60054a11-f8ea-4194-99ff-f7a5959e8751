/**
 * 浏览器扩展式检测器
 * 使用浏览器原生API和扩展技术来检测跨域点击
 */

export interface ExtensionDetectionData {
  clicks: {
    total: number;
    byType: Record<string, number>;
    positions: Array<{ x: number; y: number; timestamp: number }>;
    targets: Array<{ selector: string; action: string; timestamp: number }>;
  };
  navigation: {
    urlChanges: number;
    pageLoads: number;
    hashChanges: number;
  };
  performance: {
    userInteractions: number;
    focusEvents: number;
    scrollEvents: number;
  };
  confidence: number;
}

class BrowserExtensionDetector {
  private detectionData = new Map<string, ExtensionDetectionData>();
  private contentScripts = new Map<string, string>();

  constructor() {
    this.initializeContentScripts();
    this.setupGlobalListeners();
  }

  /**
   * 初始化内容脚本
   */
  private initializeContentScripts(): void {
    // B站检测脚本
    this.contentScripts.set('bilibili', this.generateBilibiliScript());
    
    // 抖音检测脚本
    this.contentScripts.set('douyin', this.generateDouyinScript());
    
    // 通用检测脚本
    this.contentScripts.set('universal', this.generateUniversalScript());
  }

  /**
   * 生成B站专用检测脚本
   */
  private generateBilibiliScript(): string {
    return `
      (function() {
        // B站特定的检测逻辑
        const detector = {
          sessionId: null,
          clickCount: 0,
          detectedActions: [],
          
          init(sessionId) {
            this.sessionId = sessionId;
            this.setupClickDetection();
            this.setupMutationObserver();
            this.reportReady();
          },
          
          setupClickDetection() {
            // 监听所有点击事件
            document.addEventListener('click', (e) => {
              this.handleClick(e);
            }, true);
            
            // 监听特定按钮
            this.monitorSpecificButtons();
          },
          
          handleClick(event) {
            this.clickCount++;
            
            const clickData = {
              x: event.clientX,
              y: event.clientY,
              timestamp: Date.now(),
              target: this.getTargetInfo(event.target)
            };
            
            // 分析点击目标
            const action = this.analyzeClickTarget(event.target);
            if (action) {
              this.detectedActions.push({
                action,
                timestamp: Date.now(),
                confidence: this.calculateConfidence(event.target, action)
              });
            }
            
            // 发送点击数据
            this.sendMessage('CLICK_DETECTED', {
              sessionId: this.sessionId,
              clickData,
              totalClicks: this.clickCount,
              detectedAction: action
            });
          },
          
          monitorSpecificButtons() {
            // B站特定按钮选择器
            const selectors = {
              like: '.video-like, .like-btn, [class*="like"]',
              share: '.video-share, .share-btn, [class*="share"]',
              follow: '.follow-btn, .subscribe-btn',
              comment: '.comment-submit, .reply-btn'
            };
            
            Object.entries(selectors).forEach(([action, selector]) => {
              document.querySelectorAll(selector).forEach(button => {
                button.addEventListener('click', () => {
                  this.handleSpecificAction(action, button);
                });
              });
            });
          },
          
          handleSpecificAction(action, button) {
            // 延迟检查状态变化
            setTimeout(() => {
              const success = this.verifyActionSuccess(action, button);
              this.sendMessage('ACTION_COMPLETED', {
                sessionId: this.sessionId,
                action,
                success,
                confidence: success ? 95 : 70,
                timestamp: Date.now()
              });
            }, 500);
          },
          
          verifyActionSuccess(action, button) {
            switch(action) {
              case 'like':
                return button.classList.contains('liked') || 
                       button.getAttribute('aria-pressed') === 'true';
              case 'follow':
                return button.textContent.includes('已关注') ||
                       button.textContent.includes('已订阅');
              case 'share':
                return document.querySelector('.share-modal, .share-dialog') !== null;
              default:
                return true;
            }
          },
          
          analyzeClickTarget(target) {
            const className = target.className || '';
            const textContent = target.textContent || '';
            
            if (className.includes('like') || textContent.includes('点赞')) return 'like';
            if (className.includes('share') || textContent.includes('分享')) return 'share';
            if (className.includes('follow') || textContent.includes('关注')) return 'follow';
            if (className.includes('comment') || textContent.includes('评论')) return 'comment';
            
            return null;
          },
          
          calculateConfidence(target, action) {
            let confidence = 70;
            
            // 基于元素特征提高置信度
            if (target.getAttribute('data-action') === action) confidence += 20;
            if (target.closest(\`[class*="\${action}"]\`)) confidence += 10;
            
            return Math.min(confidence, 95);
          },
          
          getTargetInfo(target) {
            return {
              tagName: target.tagName,
              className: target.className,
              id: target.id,
              textContent: target.textContent?.substring(0, 50)
            };
          },
          
          setupMutationObserver() {
            const observer = new MutationObserver((mutations) => {
              mutations.forEach((mutation) => {
                if (mutation.type === 'attributes') {
                  this.checkAttributeChange(mutation);
                }
              });
            });
            
            observer.observe(document.body, {
              attributes: true,
              subtree: true,
              attributeFilter: ['class', 'aria-pressed', 'data-liked']
            });
          },
          
          checkAttributeChange(mutation) {
            const target = mutation.target;
            const attributeName = mutation.attributeName;
            
            if (attributeName === 'class' || attributeName === 'aria-pressed') {
              const action = this.analyzeClickTarget(target);
              if (action) {
                this.sendMessage('STATE_CHANGE_DETECTED', {
                  sessionId: this.sessionId,
                  action,
                  confidence: 90,
                  timestamp: Date.now()
                });
              }
            }
          },
          
          sendMessage(type, data) {
            // 尝试多种通信方式
            try {
              // 方式1: postMessage
              window.parent.postMessage({
                type: 'EXTENSION_DETECTION',
                subType: type,
                data: data
              }, '*');
            } catch (e) {
              console.log('postMessage failed:', e);
            }
            
            try {
              // 方式2: 自定义事件
              window.dispatchEvent(new CustomEvent('crossDomainDetection', {
                detail: { type, data }
              }));
            } catch (e) {
              console.log('CustomEvent failed:', e);
            }
            
            try {
              // 方式3: localStorage (如果同源)
              localStorage.setItem('crossDomainDetection', JSON.stringify({
                type, data, timestamp: Date.now()
              }));
            } catch (e) {
              console.log('localStorage failed:', e);
            }
          },
          
          reportReady() {
            this.sendMessage('DETECTOR_READY', {
              sessionId: this.sessionId,
              platform: 'bilibili',
              timestamp: Date.now()
            });
          }
        };
        
        // 等待初始化信号
        window.addEventListener('message', (event) => {
          if (event.data.type === 'INIT_DETECTION') {
            detector.init(event.data.sessionId);
          }
        });
        
        // 如果页面已经加载完成，立即初始化
        if (document.readyState === 'complete') {
          // 从URL参数获取sessionId
          const urlParams = new URLSearchParams(window.location.search);
          const sessionId = urlParams.get('__click_detection');
          if (sessionId) {
            detector.init(sessionId);
          }
        }
        
        // 暴露到全局
        window.crossDomainDetector = detector;
      })();
    `;
  }

  /**
   * 生成抖音专用检测脚本
   */
  private generateDouyinScript(): string {
    return `
      (function() {
        // 抖音特定的检测逻辑
        const detector = {
          sessionId: null,
          clickCount: 0,
          detectedActions: [],
          
          init(sessionId) {
            this.sessionId = sessionId;
            this.setupDouyinDetection();
            this.reportReady();
          },
          
          setupDouyinDetection() {
            // 抖音特定的选择器
            const selectors = {
              like: '[data-e2e="like-icon"], [data-e2e="video-like"], .digg-btn',
              share: '[data-e2e="share-icon"], [data-e2e="video-share"]',
              follow: '[data-e2e="follow-icon"], .follow-btn',
              comment: '[data-e2e="comment-icon"], .comment-btn'
            };
            
            // 监听点击事件
            document.addEventListener('click', (e) => {
              this.handleDouyinClick(e, selectors);
            }, true);
          },
          
          handleDouyinClick(event, selectors) {
            this.clickCount++;
            
            const target = event.target;
            let detectedAction = null;
            
            // 检查是否点击了特定按钮
            Object.entries(selectors).forEach(([action, selector]) => {
              if (target.matches(selector) || target.closest(selector)) {
                detectedAction = action;
              }
            });
            
            if (detectedAction) {
              this.detectedActions.push({
                action: detectedAction,
                timestamp: Date.now(),
                confidence: 85
              });
              
              this.sendMessage('DOUYIN_ACTION_DETECTED', {
                sessionId: this.sessionId,
                action: detectedAction,
                clickCount: this.clickCount,
                timestamp: Date.now()
              });
            }
          },
          
          sendMessage(type, data) {
            // 与B站脚本相同的通信逻辑
            try {
              window.parent.postMessage({
                type: 'EXTENSION_DETECTION',
                subType: type,
                data: data
              }, '*');
            } catch (e) {
              console.log('Communication failed:', e);
            }
          },
          
          reportReady() {
            this.sendMessage('DETECTOR_READY', {
              sessionId: this.sessionId,
              platform: 'douyin',
              timestamp: Date.now()
            });
          }
        };
        
        // 初始化逻辑
        window.addEventListener('message', (event) => {
          if (event.data.type === 'INIT_DETECTION') {
            detector.init(event.data.sessionId);
          }
        });
        
        window.crossDomainDetector = detector;
      })();
    `;
  }

  /**
   * 生成通用检测脚本
   */
  private generateUniversalScript(): string {
    return `
      (function() {
        // 通用检测逻辑，适用于所有平台
        const detector = {
          sessionId: null,
          clickCount: 0,
          
          init(sessionId) {
            this.sessionId = sessionId;
            this.setupUniversalDetection();
            this.reportReady();
          },
          
          setupUniversalDetection() {
            // 监听所有点击
            document.addEventListener('click', () => {
              this.clickCount++;
              this.sendMessage('UNIVERSAL_CLICK', {
                sessionId: this.sessionId,
                clickCount: this.clickCount,
                timestamp: Date.now()
              });
            }, true);
            
            // 监听键盘事件
            document.addEventListener('keydown', (e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                this.sendMessage('KEYBOARD_INTERACTION', {
                  sessionId: this.sessionId,
                  key: e.key,
                  timestamp: Date.now()
                });
              }
            });
          },
          
          sendMessage(type, data) {
            try {
              window.parent.postMessage({
                type: 'EXTENSION_DETECTION',
                subType: type,
                data: data
              }, '*');
            } catch (e) {
              console.log('Communication failed:', e);
            }
          },
          
          reportReady() {
            this.sendMessage('DETECTOR_READY', {
              sessionId: this.sessionId,
              platform: 'universal',
              timestamp: Date.now()
            });
          }
        };
        
        window.addEventListener('message', (event) => {
          if (event.data.type === 'INIT_DETECTION') {
            detector.init(event.data.sessionId);
          }
        });
        
        window.crossDomainDetector = detector;
      })();
    `;
  }

  /**
   * 设置全局监听器
   */
  private setupGlobalListeners(): void {
    window.addEventListener('message', (event) => {
      if (event.data.type === 'EXTENSION_DETECTION') {
        this.handleDetectionMessage(event.data);
      }
    });

    // 监听localStorage变化（用于跨域通信）
    window.addEventListener('storage', (event) => {
      if (event.key === 'crossDomainDetection') {
        try {
          const data = JSON.parse(event.newValue || '{}');
          this.handleDetectionMessage(data);
        } catch (e) {
          console.log('Failed to parse storage data:', e);
        }
      }
    });
  }

  /**
   * 处理检测消息
   */
  private handleDetectionMessage(message: any): void {
    const { subType, data } = message;
    
    if (!data.sessionId) return;
    
    let detectionData = this.detectionData.get(data.sessionId);
    if (!detectionData) {
      detectionData = this.initializeDetectionData();
      this.detectionData.set(data.sessionId, detectionData);
    }

    switch (subType) {
      case 'CLICK_DETECTED':
      case 'UNIVERSAL_CLICK':
        detectionData.clicks.total = data.clickCount || data.totalClicks || 0;
        if (data.clickData) {
          detectionData.clicks.positions.push({
            x: data.clickData.x,
            y: data.clickData.y,
            timestamp: data.clickData.timestamp
          });
        }
        break;

      case 'ACTION_COMPLETED':
      case 'DOUYIN_ACTION_DETECTED':
        if (data.action) {
          detectionData.clicks.byType[data.action] = 
            (detectionData.clicks.byType[data.action] || 0) + 1;
          
          detectionData.clicks.targets.push({
            selector: data.action,
            action: data.action,
            timestamp: data.timestamp
          });
        }
        break;

      case 'STATE_CHANGE_DETECTED':
        detectionData.performance.userInteractions++;
        break;

      case 'DETECTOR_READY':
        console.log(`✅ 检测器就绪 - 平台: ${data.platform}, 会话: ${data.sessionId}`);
        break;
    }

    // 更新置信度
    detectionData.confidence = this.calculateExtensionConfidence(detectionData);
  }

  /**
   * 初始化检测数据
   */
  private initializeDetectionData(): ExtensionDetectionData {
    return {
      clicks: {
        total: 0,
        byType: {},
        positions: [],
        targets: []
      },
      navigation: {
        urlChanges: 0,
        pageLoads: 0,
        hashChanges: 0
      },
      performance: {
        userInteractions: 0,
        focusEvents: 0,
        scrollEvents: 0
      },
      confidence: 0
    };
  }

  /**
   * 计算扩展检测置信度
   */
  private calculateExtensionConfidence(data: ExtensionDetectionData): number {
    let confidence = 0;

    // 基于点击数量
    if (data.clicks.total > 0) confidence += 40;
    if (data.clicks.total > 2) confidence += 20;

    // 基于特定操作
    const actionCount = Object.keys(data.clicks.byType).length;
    confidence += actionCount * 15;

    // 基于用户交互
    if (data.performance.userInteractions > 0) confidence += 20;

    // 基于点击位置多样性
    if (data.clicks.positions.length > 1) confidence += 10;

    return Math.min(confidence, 95);
  }

  /**
   * 启动扩展式检测
   */
  startExtensionDetection(
    sessionId: string,
    targetWindow: Window,
    platform: string,
    taskTypes: string[]
  ): void {
    console.log(`🔌 启动扩展式检测 - 会话: ${sessionId}, 平台: ${platform}`);

    // 初始化检测数据
    this.detectionData.set(sessionId, this.initializeDetectionData());

    // 尝试注入检测脚本
    this.injectDetectionScript(targetWindow, platform, sessionId);

    // 发送初始化消息
    setTimeout(() => {
      try {
        targetWindow.postMessage({
          type: 'INIT_DETECTION',
          sessionId: sessionId,
          platform: platform,
          taskTypes: taskTypes
        }, '*');
      } catch (e) {
        console.log('Failed to send init message:', e);
      }
    }, 2000);
  }

  /**
   * 注入检测脚本
   */
  private injectDetectionScript(targetWindow: Window, platform: string, sessionId: string): void {
    try {
      // 尝试直接注入
      const script = this.contentScripts.get(platform) || this.contentScripts.get('universal')!;
      
      const scriptElement = targetWindow.document.createElement('script');
      scriptElement.textContent = script;
      targetWindow.document.head.appendChild(scriptElement);
      
      console.log(`✅ 成功注入扩展式检测脚本 - 平台: ${platform}`);
    } catch (error) {
      console.log(`❌ 扩展式脚本注入失败: ${error.message}`);
      
      // 回退到URL参数方式
      this.tryURLParameterInjection(targetWindow, platform, sessionId);
    }
  }

  /**
   * 尝试URL参数注入
   */
  private tryURLParameterInjection(targetWindow: Window, platform: string, sessionId: string): void {
    try {
      const currentUrl = targetWindow.location.href;
      const separator = currentUrl.includes('?') ? '&' : '?';
      const newUrl = `${currentUrl}${separator}__click_detection=${sessionId}&__platform=${platform}`;
      
      // 重新加载页面并注入参数
      targetWindow.location.href = newUrl;
      
      console.log(`🔄 通过URL参数重新加载页面进行检测`);
    } catch (error) {
      console.log(`❌ URL参数注入也失败: ${error.message}`);
    }
  }

  /**
   * 获取扩展检测结果
   */
  getExtensionDetectionResult(sessionId: string): ExtensionDetectionData | null {
    return this.detectionData.get(sessionId) || null;
  }

  /**
   * 停止扩展检测
   */
  stopExtensionDetection(sessionId: string): void {
    this.detectionData.delete(sessionId);
    console.log(`🛑 停止扩展式检测 - 会话: ${sessionId}`);
  }
}

export const browserExtensionDetector = new BrowserExtensionDetector();
