import React, { useState, useEffect } from 'react';
import { 
  <PERSON>Circle, 
  Clock, 
  Eye, 
  Share, 
  Heart, 
  UserPlus, 
  MessageCircle,
  AlertCircle,
  X
} from 'lucide-react';
import { TaskType } from '@/types';

interface TaskGuidanceOverlayProps {
  isVisible: boolean;
  selectedRequirements: TaskType[];
  platform: string;
  onClose: () => void;
  sessionId: string;
}

interface DetectionStatus {
  action: TaskType;
  detected: boolean;
  confidence: number;
  timestamp?: number;
}

const TaskGuidanceOverlay: React.FC<TaskGuidanceOverlayProps> = ({
  isVisible,
  selectedRequirements,
  platform,
  onClose,
  sessionId
}) => {
  const [detectionStatus, setDetectionStatus] = useState<DetectionStatus[]>([]);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime] = useState(Date.now());

  // 任务类型配置
  const taskConfig = {
    like: { icon: Heart, label: '点赞', color: 'text-red-500', bgColor: 'bg-red-50' },
    share: { icon: Share, label: '分享', color: 'text-blue-500', bgColor: 'bg-blue-50' },
    comment: { icon: MessageCircle, label: '评论', color: 'text-green-500', bgColor: 'bg-green-50' },
    follow: { icon: UserPlus, label: '关注', color: 'text-purple-500', bgColor: 'bg-purple-50' }
  };

  // 平台特定指导
  const platformGuidance = {
    bilibili: {
      like: '点击视频下方的点赞按钮（❤️图标）',
      share: '点击分享按钮，选择分享方式',
      comment: '在评论区输入评论并发送',
      follow: '点击UP主头像旁的关注按钮'
    },
    douyin: {
      like: '点击视频右侧的红心按钮',
      share: '点击分享按钮，选择分享平台',
      comment: '点击评论图标，输入评论',
      follow: '点击作者头像，然后点击关注'
    },
    kuaishou: {
      like: '点击视频右侧的点赞按钮',
      share: '点击分享按钮进行分享',
      comment: '在评论区发表评论',
      follow: '点击关注按钮关注作者'
    }
  };

  // 初始化检测状态
  useEffect(() => {
    const initialStatus = selectedRequirements.map(req => ({
      action: req,
      detected: false,
      confidence: 0
    }));
    setDetectionStatus(initialStatus);
  }, [selectedRequirements]);

  // 监听检测结果
  useEffect(() => {
    const handleDetection = (event: MessageEvent) => {
      if (event.data.type === 'TASK_ACTION_DETECTED' && event.data.sessionId === sessionId) {
        const { action, confidence } = event.data;
        setDetectionStatus(prev => 
          prev.map(status => 
            status.action === action 
              ? { ...status, detected: true, confidence, timestamp: Date.now() }
              : status
          )
        );
      }
    };

    window.addEventListener('message', handleDetection);
    return () => window.removeEventListener('message', handleDetection);
  }, [sessionId]);

  // 更新计时器
  useEffect(() => {
    if (!isVisible) return;

    const timer = setInterval(() => {
      setElapsedTime(Date.now() - startTime);
    }, 1000);

    return () => clearInterval(timer);
  }, [isVisible, startTime]);

  if (!isVisible) return null;

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  const completedCount = detectionStatus.filter(s => s.detected).length;
  const totalCount = selectedRequirements.length;

  return (
    <div className="fixed top-4 right-4 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Eye className="w-5 h-5 text-blue-500" />
          <h3 className="font-semibold text-gray-900">任务检测状态</h3>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* 进度概览 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">完成进度</span>
          <span className="text-sm font-medium text-gray-900">
            {completedCount}/{totalCount}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(completedCount / totalCount) * 100}%` }}
          />
        </div>
        <div className="flex items-center justify-between mt-2 text-sm text-gray-500">
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>{formatTime(elapsedTime)}</span>
          </div>
          <span className="capitalize">{platform}</span>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="p-4 space-y-3">
        {detectionStatus.map((status) => {
          const config = taskConfig[status.action];
          const Icon = config.icon;
          const guidance = platformGuidance[platform]?.[status.action] || `请完成${config.label}操作`;

          return (
            <div
              key={status.action}
              className={`p-3 rounded-lg border transition-all duration-300 ${
                status.detected 
                  ? 'border-green-200 bg-green-50' 
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Icon className={`w-4 h-4 ${config.color}`} />
                  <span className="font-medium text-gray-900">{config.label}</span>
                </div>
                {status.detected ? (
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-xs text-green-600">
                      {status.confidence}%
                    </span>
                  </div>
                ) : (
                  <AlertCircle className="w-4 h-4 text-gray-400" />
                )}
              </div>
              <p className="text-xs text-gray-600">{guidance}</p>
              {status.detected && status.timestamp && (
                <p className="text-xs text-green-600 mt-1">
                  ✓ 已检测到 - {new Date(status.timestamp).toLocaleTimeString()}
                </p>
              )}
            </div>
          );
        })}
      </div>

      {/* 底部提示 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
        <div className="text-xs text-gray-600 space-y-1">
          <p>💡 <strong>提示：</strong>完成操作后请稍等片刻，系统正在检测...</p>
          <p>🔄 如果检测失败，请关闭窗口并提供完成截图</p>
        </div>
      </div>
    </div>
  );
};

export default TaskGuidanceOverlay;
