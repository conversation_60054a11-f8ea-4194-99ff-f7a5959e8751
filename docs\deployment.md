# 部署文档

## 环境要求

- Node.js 18+
- PostgreSQL 14+
- Redis 6+ (可选)
- Docker & Docker Compose (推荐)

## 本地开发环境搭建

### 1. 克隆项目

```bash
git clone <repository-url>
cd mutual-like-platform
```

### 2. 安装依赖

```bash
# 安装根目录依赖
npm install

# 安装前后端依赖
npm run setup
```

### 3. 环境配置

```bash
# 后端环境配置
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件，配置数据库连接等信息

# 前端环境配置
cp frontend/.env.example frontend/.env
# 编辑 frontend/.env 文件，配置API地址等信息
```

### 4. 数据库初始化

```bash
cd backend

# 运行数据库迁移
npm run db:migrate

# 插入种子数据
npm run db:seed
```

### 5. 启动开发服务器

```bash
# 在项目根目录
npm run dev

# 或分别启动
npm run dev:backend  # 后端: http://localhost:3000
npm run dev:frontend # 前端: http://localhost:5173
```

## Docker 部署

### 1. 使用 Docker Compose (推荐)

```bash
# 构建镜像
npm run docker:build

# 启动所有服务
npm run docker:up

# 查看日志
docker-compose logs -f

# 停止服务
npm run docker:down
```

### 2. 单独构建镜像

```bash
# 构建后端镜像
cd backend
docker build -t mutual-like-backend .

# 构建前端镜像
cd frontend
docker build -t mutual-like-frontend .
```

## 生产环境部署

### 1. 服务器要求

- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 20GB以上
- 操作系统: Ubuntu 20.04+ / CentOS 8+

### 2. 安装 Docker

```bash
# Ubuntu
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 3. 部署步骤

```bash
# 1. 克隆代码
git clone <repository-url>
cd mutual-like-platform

# 2. 配置环境变量
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# 编辑环境变量文件
nano backend/.env
nano frontend/.env

# 3. 启动服务
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 4. 初始化数据库
docker-compose exec backend npm run db:migrate
docker-compose exec backend npm run db:seed
```

### 4. Nginx 反向代理配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 5. SSL 证书配置

```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 监控和维护

### 1. 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 2. 数据库备份

```bash
# 备份数据库
docker-compose exec postgres pg_dump -U postgres mutual_like_platform > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U postgres mutual_like_platform < backup.sql
```

### 3. 更新部署

```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose build
docker-compose up -d

# 运行数据库迁移
docker-compose exec backend npm run db:migrate
```

### 4. 性能监控

推荐使用以下工具进行监控：

- **应用监控**: PM2, New Relic, Sentry
- **服务器监控**: Prometheus + Grafana
- **日志分析**: ELK Stack (Elasticsearch, Logstash, Kibana)

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接配置是否正确
   - 确认防火墙设置

2. **前端无法访问后端API**
   - 检查CORS配置
   - 验证API地址配置
   - 查看网络连接

3. **Docker容器启动失败**
   - 检查端口占用情况
   - 查看容器日志
   - 验证环境变量配置

### 性能优化

1. **数据库优化**
   - 添加适当的索引
   - 定期清理过期数据
   - 配置连接池

2. **缓存策略**
   - 启用Redis缓存
   - 配置CDN加速
   - 使用浏览器缓存

3. **负载均衡**
   - 使用Nginx负载均衡
   - 部署多个应用实例
   - 配置健康检查
