import { Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { ApiResponse, PointTransaction, User } from '@/types';
import { FileDB } from '@/services/fileDatabase';

/**
 * 获取积分统计
 */
export const getPointsStats = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;

  if (!userId) {
    res.status(401).json({ success: false, message: '未登录' });
    return;
  }

  const user = await FileDB.getById<User>('users', userId);
  if (!user) {
    res.status(404).json({ success: false, message: '用户不存在' });
    return;
  }

  const now = new Date();
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const thisWeekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  const allTransactions = await FileDB.find<PointTransaction>('pointTransactions', t => t.user_id === userId);

  const getSum = (transactions: PointTransaction[], type: string, fromDate: Date) => {
    return transactions
      .filter(t => t.transaction_type === type && new Date(t.created_at) >= fromDate)
      .reduce((sum, t) => sum + t.amount, 0);
  };
  
  const totalEarned = allTransactions
    .filter(t => t.transaction_type === 'reward')
    .reduce((sum, t) => sum + t.amount, 0);

  const stats = {
    total_points: user.points_balance,
    earned_this_month: getSum(allTransactions, 'reward', thisMonthStart),
    spent_this_month: Math.abs(getSum(allTransactions, 'cost', thisMonthStart)),
    earned_this_week: getSum(allTransactions, 'reward', thisWeekStart),
    spent_this_week: Math.abs(getSum(allTransactions, 'cost', thisWeekStart)),
    earned_today: getSum(allTransactions, 'reward', todayStart),
    spent_today: Math.abs(getSum(allTransactions, 'cost', todayStart)),
    total_earned: totalEarned,
  };

  res.json({ success: true, message: '获取积分统计成功', data: stats });
});

/**
 * 获取积分交易记录
 */
export const getPointsTransactions = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const type = req.query.type as string;

  if (!userId) {
    res.status(401).json({ success: false, message: '未登录' });
    return;
  }

  let transactions = await FileDB.find<PointTransaction>('pointTransactions', t => t.user_id === userId);

  if (type) {
    transactions = transactions.filter(t => t.transaction_type === type);
  }

  transactions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  const total = transactions.length;
  const offset = (page - 1) * limit;
  const paginatedTransactions = transactions.slice(offset, offset + limit);

  res.json({
    success: true,
    message: '获取积分交易记录成功',
    data: {
      transactions: paginatedTransactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
});

/**
 * 扣除积分（用于任务发布）
 */
export const deductPoints = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;
  const { amount, task_id, description, transaction_type = 'task_publish' } = req.body;

  if (!userId) {
    res.status(401).json({ success: false, message: '未登录' });
    return;
  }

  if (!amount || amount <= 0) {
    res.status(400).json({ success: false, message: '扣除金额必须大于0' });
    return;
  }

  if (!description) {
    res.status(400).json({ success: false, message: '交易描述不能为空' });
    return;
  }

  const user = await FileDB.getById<User>('users', userId);
  if (!user) {
    res.status(404).json({ success: false, message: '用户不存在' });
    return;
  }

  // 检查积分余额
  if (user.points_balance < amount) {
    res.status(400).json({ success: false, message: '积分余额不足' });
    return;
  }

  // 扣除积分
  const newBalance = user.points_balance - amount;
  await FileDB.update<User>('users', userId, { points_balance: newBalance });

  // 创建交易记录
  const transaction: Omit<PointTransaction, 'id'> = {
    user_id: userId,
    amount: -amount, // 负数表示扣除
    transaction_type,
    description,
    related_task_id: task_id || null,
    created_at: new Date().toISOString(),
  };

  const savedTransaction = await FileDB.create<PointTransaction>('pointTransactions', transaction);

  res.json({
    success: true,
    message: '积分扣除成功',
    data: savedTransaction,
  });
});

/**
 * 添加积分（用于任务完成奖励）
 */
export const addPoints = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;
  const { amount, task_id, description, transaction_type = 'task_complete' } = req.body;

  if (!userId) {
    res.status(401).json({ success: false, message: '未登录' });
    return;
  }

  if (!amount || amount <= 0) {
    res.status(400).json({ success: false, message: '添加金额必须大于0' });
    return;
  }

  if (!description) {
    res.status(400).json({ success: false, message: '交易描述不能为空' });
    return;
  }

  const user = await FileDB.getById<User>('users', userId);
  if (!user) {
    res.status(404).json({ success: false, message: '用户不存在' });
    return;
  }

  // 添加积分
  const newBalance = user.points_balance + amount;
  await FileDB.update<User>('users', userId, { points_balance: newBalance });

  // 创建交易记录
  const transaction: Omit<PointTransaction, 'id'> = {
    user_id: userId,
    amount: amount, // 正数表示添加
    transaction_type,
    description,
    related_task_id: task_id || null,
    created_at: new Date().toISOString(),
  };

  const savedTransaction = await FileDB.create<PointTransaction>('pointTransactions', transaction);

  res.json({
    success: true,
    message: '积分添加成功',
    data: savedTransaction,
  });
});

/**
 * 添加测试积分（仅用于开发测试）
 */
export const addTestPoints = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const userId = (req as any).user?.userId;
  const { amount = 100 } = req.body;

  if (!userId) {
    res.status(401).json({ success: false, message: '未登录' });
    return;
  }

  const user = await FileDB.getById<User>('users', userId);
  if (!user) {
    res.status(404).json({ success: false, message: '用户不存在' });
    return;
  }

  // 添加测试积分
  const newBalance = user.points_balance + amount;
  await FileDB.update<User>('users', userId, { points_balance: newBalance });

  // 创建交易记录
  const transaction: Omit<PointTransaction, 'id'> = {
    user_id: userId,
    amount: amount,
    transaction_type: 'test',
    description: '测试积分',
    related_task_id: null,
    created_at: new Date().toISOString(),
  };

  const savedTransaction = await FileDB.create<PointTransaction>('pointTransactions', transaction);

  res.json({
    success: true,
    message: '测试积分添加成功',
    data: savedTransaction,
  });
});
