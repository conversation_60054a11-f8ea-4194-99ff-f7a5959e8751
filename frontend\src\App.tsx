import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { AuthService } from '@/services/authService';

// 功能标志
import { isFeatureEnabled, debugFeatureFlags } from '@/config/featureFlags';

// 页面组件
import LoginPage from '@/pages/LoginPage';
import RegisterPage from '@/pages/RegisterPage';
import DashboardPage from '@/pages/DashboardPage';
import TaskHallPage from '@/pages/TaskHallPage';
import MyTasksPage from '@/pages/MyTasksPage';
import ProfilePage from '@/pages/ProfilePage';
import PointsPage from '@/pages/PointsPage';
import PublishTaskPage from '@/pages/PublishTaskPage';

// 布局组件
import Layout from '@/components/Layout';
import LoadingSpinner from '@/components/LoadingSpinner';
import DebugLoginSimple from '@/components/DebugLoginSimple';

// 删除未使用的 ProtectedRoute 组件

// 公开路由组件（已登录用户重定向到首页）- 临时禁用重定向
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isLoading } = useAuthStore(); // 移除未使用的 isAuthenticated

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <LoadingSpinner size="lg" text="加载中..." />
      </div>
    );
  }

  // 临时：不重定向，允许访问登录页面
  console.log('🔧 PublicRoute - 允许访问公开页面');
  return <>{children}</>;
};

const App: React.FC = () => {
  const { isAuthenticated, tokens, setLoading, logout } = useAuthStore();

  // 应用渲染日志
  useEffect(() => {
    if (isFeatureEnabled('ENABLE_CONSOLE_LOGS')) {
      console.log('🔧 App 组件已渲染');
      console.log('🔧 当前路径:', window.location.pathname);
      debugFeatureFlags();
    }
  }, []);

  // 应用初始化
  useEffect(() => {
    const initializeApp = async () => {
      // 如果有token，检查是否为模拟token
      if (tokens?.accessToken) {
        // 验证token有效性

        // 真实token，尝试获取用户信息
        try {
          setLoading(true);
          await AuthService.getCurrentUser();
        } catch (error) {
          console.error('获取用户信息失败:', error);
          // 临时禁用logout调用，避免重定向
          console.log('🔧 跳过logout调用，避免重定向');
          // logout();
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    initializeApp();
  }, [tokens, setLoading, logout]);

  // 监听页面可见性变化，自动刷新token
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible' && isAuthenticated) {
        // 刷新token

        try {
          await AuthService.getCurrentUser();
        } catch (error) {
          console.error('刷新用户信息失败:', error);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isAuthenticated, tokens]);

  return (
    <div className="h-screen bg-gray-50 overflow-hidden">
      {/* 调试登录组件 - 根据功能标志显示 */}
      {isFeatureEnabled('ENABLE_DEBUG_LOGIN') && <DebugLoginSimple />}

      <Routes>
        {/* 公开路由 */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <LoginPage />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <RegisterPage />
            </PublicRoute>
          }
        />

        {/* 直接访问的路由 - 不需要认证保护 */}
        <Route
          path="/"
          element={<Layout />}
        >
          <Route index element={<DashboardPage />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="tasks" element={<TaskHallPage />} />
          <Route path="tasks/publish" element={<PublishTaskPage />} />
          <Route path="my-tasks" element={<MyTasksPage />} />
          <Route path="points" element={<PointsPage />} />
          <Route path="profile" element={<ProfilePage />} />
        </Route>

        {/* 404页面 */}
        <Route
          path="*"
          element={
            <div className="min-h-screen flex items-center justify-center">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                <p className="text-gray-600 mb-8">页面不存在</p>
                <button
                  onClick={() => window.history.back()}
                  className="btn btn-primary"
                >
                  返回上一页
                </button>
              </div>
            </div>
          }
        />
      </Routes>
    </div>
  );
};

export default App;
