// 简化的后端启动脚本，用于快速测试
console.log('🔄 正在启动简化后端服务...');

const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('✅ 依赖加载完成');

const app = express();
const PORT = 3000;

// 基础中间件
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: '服务运行正常',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// 模拟用户认证
app.post('/api/auth/login', (req, res) => {
  res.json({
    success: true,
    data: {
      user: {
        id: 1,
        email: '<EMAIL>',
        username: 'testuser',
        reputation_score: 85,
        points: 1000
      },
      token: 'mock-jwt-token'
    }
  });
});

app.get('/api/auth/me', (req, res) => {
  res.json({
    success: true,
    data: {
      id: 1,
      email: '<EMAIL>',
      username: 'testuser',
      reputation_score: 85,
      points: 1000
    }
  });
});

// 模拟任务API
app.get('/api/tasks/random', (req, res) => {
  const platform = req.query.platform || 'bilibili';
  
  const mockTasks = {
    bilibili: {
      id: 1,
      title: 'B站视频点赞任务',
      description: '为指定B站视频点赞',
      video_url: 'https://www.bilibili.com/video/BV1xx411c7mu',
      platform: 'bilibili',
      requirements: ['like'],
      points_per_requirement: { like: 10 },
      status: 'active',
      publisher_id: 1
    },
    douyin: {
      id: 2,
      title: '抖音视频点赞任务',
      description: '为指定抖音视频点赞',
      video_url: 'https://www.douyin.com/video/7234567890123456789',
      platform: 'douyin',
      requirements: ['like'],
      points_per_requirement: { like: 10 },
      status: 'active',
      publisher_id: 1
    }
  };

  res.json({
    success: true,
    data: mockTasks[platform] || mockTasks.bilibili
  });
});

// 视频解析API
app.post('/api/video/parse', (req, res) => {
  const { url } = req.body;
  
  if (url.includes('bilibili.com')) {
    res.json({
      success: true,
      data: {
        title: 'B站测试视频',
        thumbnail: '/api/video/proxy/image?url=https%3A%2F%2Fi1.hdslb.com%2Fbfs%2Farchive%2F744690cb92216bcb2476c804e750a0e8f82a41cf.jpg',
        duration: '03:45',
        platform: 'bilibili'
      }
    });
  } else if (url.includes('douyin.com')) {
    res.json({
      success: true,
      data: {
        title: '抖音测试视频',
        thumbnail: '/api/video/proxy/image?url=https%3A%2F%2Fexample.com%2Fthumbnail.jpg',
        duration: '00:30',
        platform: 'douyin'
      }
    });
  } else {
    res.json({
      success: true,
      data: {
        title: '未知平台视频',
        thumbnail: '',
        duration: '00:00',
        platform: 'unknown'
      }
    });
  }
});

// 图片代理API
app.get('/api/video/proxy/image', (req, res) => {
  // 返回一个简单的1x1像素图片
  const pixel = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
  res.set('Content-Type', 'image/png');
  res.send(pixel);
});

// 任务执行API
app.post('/api/tasks/execute', (req, res) => {
  const { task_id, selected_requirements, execution_duration, proof } = req.body;
  
  console.log('📝 收到任务执行请求:', {
    task_id,
    selected_requirements,
    execution_duration: Math.round(execution_duration / 1000) + '秒',
    has_proof: !!proof
  });

  res.json({
    success: true,
    data: {
      id: Date.now(),
      task_id,
      user_id: 1,
      selected_requirements,
      execution_duration,
      status: 'completed',
      points_earned: selected_requirements.length * 10,
      proof,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  });
});

// 平台识别API
app.post('/api/utils/identify-platform', (req, res) => {
  const { url } = req.body;
  
  let platform = 'unknown';
  if (url.includes('bilibili.com') || url.includes('b23.tv')) {
    platform = 'bilibili';
  } else if (url.includes('douyin.com')) {
    platform = 'douyin';
  } else if (url.includes('kuaishou.com')) {
    platform = 'kuaishou';
  } else if (url.includes('xiaohongshu.com')) {
    platform = 'xiaohongshu';
  }

  res.json({
    success: true,
    data: { platform }
  });
});

// 用户任务API
app.get('/api/tasks/my/published', (req, res) => {
  res.json({
    success: true,
    data: {
      tasks: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0
      }
    }
  });
});

app.get('/api/tasks/my/participated', (req, res) => {
  res.json({
    success: true,
    data: {
      executions: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0
      }
    }
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `API endpoint not found: ${req.method} ${req.path}`
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 简化后端服务启动成功`);
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log(`🌍 环境: development (简化模式)`);
});
