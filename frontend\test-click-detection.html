<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🖱️ 跨域点击检测测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
        }
        
        .test-card h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.3em;
        }
        
        .test-card p {
            color: #6c757d;
            margin-bottom: 15px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        
        .results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #007bff;
        }
        
        .log {
            background: #212529;
            color: #fff;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .status-info { background: #17a2b8; }
        
        .click-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖱️ 跨域点击检测测试系统</h1>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>🎯 基础点击检测</h3>
                <p>测试基本的点击检测功能，包括点击次数统计和位置记录</p>
                <button class="btn" onclick="testBasicClickDetection()">开始基础测试</button>
                <button class="btn btn-warning" onclick="simulateClicks(5)">模拟5次点击</button>
                <button class="btn btn-warning" onclick="simulateClicks(10)">模拟10次点击</button>
            </div>
            
            <div class="test-card">
                <h3>👍 任务特定检测</h3>
                <p>测试特定任务的点击检测，如点赞、分享、关注等操作</p>
                <button class="btn" onclick="testTaskSpecificDetection('like')">测试点赞检测</button>
                <button class="btn" onclick="testTaskSpecificDetection('share')">测试分享检测</button>
                <button class="btn" onclick="testTaskSpecificDetection('follow')">测试关注检测</button>
            </div>
            
            <div class="test-card">
                <h3>🌐 跨域场景测试</h3>
                <p>测试跨域窗口中的点击检测能力和智能推断功能</p>
                <button class="btn" onclick="testCrossDomainDetection('bilibili')">B站跨域测试</button>
                <button class="btn" onclick="testCrossDomainDetection('douyin')">抖音跨域测试</button>
                <button class="btn btn-success" onclick="openRealTestWindow()">真实窗口测试</button>
            </div>
            
            <div class="test-card">
                <h3>📊 智能推断测试</h3>
                <p>测试基于用户行为模式的智能推断和置信度计算</p>
                <button class="btn" onclick="testIntelligentInference()">智能推断测试</button>
                <button class="btn" onclick="testConfidenceCalculation()">置信度计算</button>
                <button class="btn btn-danger" onclick="testFalsePositive()">误判测试</button>
            </div>
        </div>
        
        <div class="results">
            <h3>📈 实时检测状态</h3>
            <div class="click-stats" id="clickStats">
                <div class="stat-card">
                    <div class="stat-number" id="totalClicks">0</div>
                    <div class="stat-label">总点击数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="likeClicks">0</div>
                    <div class="stat-label">点赞点击</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="shareClicks">0</div>
                    <div class="stat-label">分享点击</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="followClicks">0</div>
                    <div class="stat-label">关注点击</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="detectedActions">0</div>
                    <div class="stat-label">检测操作</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avgConfidence">0%</div>
                    <div class="stat-label">平均置信度</div>
                </div>
            </div>
            
            <div style="margin-top: 20px;">
                <button class="btn btn-success" onclick="startRealTimeMonitoring()">开始实时监控</button>
                <button class="btn btn-danger" onclick="stopRealTimeMonitoring()">停止监控</button>
                <button class="btn btn-warning" onclick="clearResults()">清空结果</button>
                <button class="btn" onclick="exportResults()">导出结果</button>
            </div>
        </div>
        
        <div class="log" id="testLog"></div>
    </div>

    <script>
        // 模拟点击检测器
        class MockClickDetector {
            constructor() {
                this.sessions = new Map();
                this.isMonitoring = false;
                this.monitoringInterval = null;
            }
            
            startClickDetection(sessionId, platform, taskTypes) {
                this.sessions.set(sessionId, {
                    platform,
                    taskTypes,
                    totalClicks: 0,
                    likeClicks: 0,
                    shareClicks: 0,
                    followClicks: 0,
                    commentClicks: 0,
                    detectedActions: [],
                    clickPositions: [],
                    startTime: Date.now()
                });
                
                log(`🎯 开始点击检测 - 会话: ${sessionId}, 平台: ${platform}, 任务: ${taskTypes.join(', ')}`);
                return this.sessions.get(sessionId);
            }
            
            simulateClick(sessionId, x = 0, y = 0, actionType = null) {
                const session = this.sessions.get(sessionId);
                if (!session) return;
                
                session.totalClicks++;
                session.clickPositions.push({ x, y, timestamp: Date.now() });
                
                if (actionType) {
                    switch (actionType) {
                        case 'like':
                            session.likeClicks++;
                            break;
                        case 'share':
                            session.shareClicks++;
                            break;
                        case 'follow':
                            session.followClicks++;
                            break;
                        case 'comment':
                            session.commentClicks++;
                            break;
                    }
                    
                    // 添加检测到的操作
                    const confidence = 70 + Math.random() * 25; // 70-95%
                    session.detectedActions.push({
                        action: actionType,
                        confidence: Math.round(confidence),
                        timestamp: Date.now(),
                        evidence: ['click_detected', 'button_identified']
                    });
                }
                
                this.updateUI(sessionId);
                log(`🖱️ 模拟点击 - 位置: (${x}, ${y}), 操作: ${actionType || '普通点击'}`);
            }
            
            getDetectionResult(sessionId) {
                return this.sessions.get(sessionId);
            }
            
            updateUI(sessionId) {
                const session = this.sessions.get(sessionId);
                if (!session) return;
                
                document.getElementById('totalClicks').textContent = session.totalClicks;
                document.getElementById('likeClicks').textContent = session.likeClicks;
                document.getElementById('shareClicks').textContent = session.shareClicks;
                document.getElementById('followClicks').textContent = session.followClicks;
                document.getElementById('detectedActions').textContent = session.detectedActions.length;
                
                if (session.detectedActions.length > 0) {
                    const avgConfidence = session.detectedActions.reduce((sum, action) => 
                        sum + action.confidence, 0) / session.detectedActions.length;
                    document.getElementById('avgConfidence').textContent = Math.round(avgConfidence) + '%';
                }
            }
        }
        
        const mockDetector = new MockClickDetector();
        let currentSessionId = 'test-session-' + Date.now();
        
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function testBasicClickDetection() {
            currentSessionId = 'basic-test-' + Date.now();
            mockDetector.startClickDetection(currentSessionId, 'test', ['like', 'share']);
            
            log('🧪 开始基础点击检测测试');
            
            // 模拟一系列点击
            setTimeout(() => mockDetector.simulateClick(currentSessionId, 100, 200), 500);
            setTimeout(() => mockDetector.simulateClick(currentSessionId, 150, 250), 1000);
            setTimeout(() => mockDetector.simulateClick(currentSessionId, 200, 300), 1500);
            
            setTimeout(() => {
                const result = mockDetector.getDetectionResult(currentSessionId);
                log(`✅ 基础测试完成 - 检测到 ${result.totalClicks} 次点击`);
            }, 2000);
        }
        
        function simulateClicks(count) {
            log(`🖱️ 模拟 ${count} 次随机点击`);
            
            for (let i = 0; i < count; i++) {
                setTimeout(() => {
                    const x = Math.random() * 800;
                    const y = Math.random() * 600;
                    mockDetector.simulateClick(currentSessionId, x, y);
                }, i * 200);
            }
        }
        
        function testTaskSpecificDetection(taskType) {
            currentSessionId = `${taskType}-test-` + Date.now();
            mockDetector.startClickDetection(currentSessionId, 'bilibili', [taskType]);
            
            log(`🎯 开始 ${taskType} 任务特定检测测试`);
            
            // 模拟任务相关点击
            setTimeout(() => {
                mockDetector.simulateClick(currentSessionId, 300, 400, taskType);
                log(`✅ 模拟 ${taskType} 操作完成`);
            }, 1000);
        }
        
        function testCrossDomainDetection(platform) {
            currentSessionId = `crossdomain-${platform}-` + Date.now();
            mockDetector.startClickDetection(currentSessionId, platform, ['like', 'share', 'follow']);
            
            log(`🌐 开始 ${platform} 跨域检测测试`);
            
            // 模拟跨域场景下的检测
            const actions = ['like', 'share', 'follow'];
            actions.forEach((action, index) => {
                setTimeout(() => {
                    mockDetector.simulateClick(currentSessionId, 100 + index * 50, 200 + index * 50, action);
                }, (index + 1) * 800);
            });
            
            setTimeout(() => {
                const result = mockDetector.getDetectionResult(currentSessionId);
                log(`🌐 跨域测试完成 - 平台: ${platform}, 检测操作: ${result.detectedActions.length}`);
            }, 3000);
        }
        
        function openRealTestWindow() {
            log('🚀 打开真实测试窗口');
            
            // 打开一个真实的B站页面进行测试
            const testUrl = 'https://www.bilibili.com/video/BV1GJ411x7h7';
            const testWindow = window.open(testUrl, '_blank', 'width=1200,height=800');
            
            if (testWindow) {
                currentSessionId = 'real-test-' + Date.now();
                mockDetector.startClickDetection(currentSessionId, 'bilibili', ['like', 'share']);
                
                log('✅ 真实测试窗口已打开，请在窗口中进行操作');
                
                // 模拟检测到用户操作
                setTimeout(() => {
                    if (!testWindow.closed) {
                        mockDetector.simulateClick(currentSessionId, 0, 0, 'like');
                        log('🎯 检测到疑似点赞操作');
                    }
                }, 5000);
            } else {
                log('❌ 无法打开测试窗口，请检查弹窗设置');
            }
        }
        
        function testIntelligentInference() {
            currentSessionId = 'intelligent-test-' + Date.now();
            mockDetector.startClickDetection(currentSessionId, 'douyin', ['like', 'share']);
            
            log('🧠 开始智能推断测试');
            
            // 模拟复杂的用户行为模式
            const pattern = [
                { delay: 500, action: null, message: '用户浏览页面' },
                { delay: 1200, action: 'like', message: '检测到点赞操作' },
                { delay: 2000, action: null, message: '用户继续浏览' },
                { delay: 3500, action: 'share', message: '检测到分享操作' },
                { delay: 4000, action: null, message: '用户完成操作' }
            ];
            
            pattern.forEach(step => {
                setTimeout(() => {
                    if (step.action) {
                        mockDetector.simulateClick(currentSessionId, Math.random() * 400, Math.random() * 300, step.action);
                    }
                    log(`🧠 ${step.message}`);
                }, step.delay);
            });
            
            setTimeout(() => {
                const result = mockDetector.getDetectionResult(currentSessionId);
                const avgConfidence = result.detectedActions.length > 0 ? 
                    result.detectedActions.reduce((sum, action) => sum + action.confidence, 0) / result.detectedActions.length : 0;
                log(`🧠 智能推断测试完成 - 平均置信度: ${Math.round(avgConfidence)}%`);
            }, 5000);
        }
        
        function testConfidenceCalculation() {
            log('📊 开始置信度计算测试');
            
            const testCases = [
                { action: 'like', duration: 3000, expected: '高置信度' },
                { action: 'share', duration: 8000, expected: '中等置信度' },
                { action: 'follow', duration: 15000, expected: '高置信度' },
                { action: 'comment', duration: 25000, expected: '很高置信度' }
            ];
            
            testCases.forEach((testCase, index) => {
                setTimeout(() => {
                    currentSessionId = `confidence-test-${index}-` + Date.now();
                    mockDetector.startClickDetection(currentSessionId, 'bilibili', [testCase.action]);
                    mockDetector.simulateClick(currentSessionId, 100, 100, testCase.action);
                    
                    const result = mockDetector.getDetectionResult(currentSessionId);
                    const confidence = result.detectedActions[0]?.confidence || 0;
                    log(`📊 ${testCase.action} 操作 (${testCase.duration}ms) - 置信度: ${confidence}% (预期: ${testCase.expected})`);
                }, index * 1000);
            });
        }
        
        function testFalsePositive() {
            currentSessionId = 'false-positive-test-' + Date.now();
            mockDetector.startClickDetection(currentSessionId, 'test', ['like']);
            
            log('⚠️ 开始误判测试 - 模拟快速无效点击');
            
            // 模拟快速的无效点击
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    mockDetector.simulateClick(currentSessionId, Math.random() * 100, Math.random() * 100);
                }, i * 50);
            }
            
            setTimeout(() => {
                const result = mockDetector.getDetectionResult(currentSessionId);
                log(`⚠️ 误判测试完成 - 总点击: ${result.totalClicks}, 有效操作: ${result.detectedActions.length}`);
            }, 2000);
        }
        
        function startRealTimeMonitoring() {
            if (mockDetector.isMonitoring) return;
            
            mockDetector.isMonitoring = true;
            log('📡 开始实时监控');
            
            mockDetector.monitoringInterval = setInterval(() => {
                // 随机模拟用户活动
                if (Math.random() < 0.3) {
                    const actions = ['like', 'share', 'follow', null];
                    const action = actions[Math.floor(Math.random() * actions.length)];
                    mockDetector.simulateClick(currentSessionId, Math.random() * 400, Math.random() * 300, action);
                }
            }, 2000);
        }
        
        function stopRealTimeMonitoring() {
            if (!mockDetector.isMonitoring) return;
            
            mockDetector.isMonitoring = false;
            if (mockDetector.monitoringInterval) {
                clearInterval(mockDetector.monitoringInterval);
                mockDetector.monitoringInterval = null;
            }
            log('🛑 停止实时监控');
        }
        
        function clearResults() {
            document.getElementById('testLog').innerHTML = '';
            document.getElementById('totalClicks').textContent = '0';
            document.getElementById('likeClicks').textContent = '0';
            document.getElementById('shareClicks').textContent = '0';
            document.getElementById('followClicks').textContent = '0';
            document.getElementById('detectedActions').textContent = '0';
            document.getElementById('avgConfidence').textContent = '0%';
            log('🧹 结果已清空');
        }
        
        function exportResults() {
            const result = mockDetector.getDetectionResult(currentSessionId);
            if (!result) {
                log('❌ 没有可导出的结果');
                return;
            }
            
            const exportData = {
                sessionId: currentSessionId,
                timestamp: new Date().toISOString(),
                result: result
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `click-detection-results-${currentSessionId}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📁 结果已导出');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 跨域点击检测测试系统已启动');
            currentSessionId = 'initial-session-' + Date.now();
            mockDetector.startClickDetection(currentSessionId, 'test', ['like', 'share', 'follow']);
        });
    </script>
</body>
</html>
