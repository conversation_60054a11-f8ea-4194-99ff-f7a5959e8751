import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import {
  X,
  AlertTriangle,
  FileText,
  Upload,
  Clock,
  CheckCircle,
  XCircle,
  MessageSquare,
  Calendar,
  Loader2
} from 'lucide-react';
import { ViolationRecord, AppealRequest, creditSystemService } from '@/services/creditSystemService';
import { toast } from 'react-hot-toast';

interface ViolationRecordsDialogProps {
  userId: number;
  isOpen: boolean;
  onClose: () => void;
}

const ViolationRecordsDialog: React.FC<ViolationRecordsDialogProps> = ({
  userId,
  isOpen,
  onClose
}) => {
  const [selectedViolation, setSelectedViolation] = useState<ViolationRecord | null>(null);
  const [showAppealForm, setShowAppealForm] = useState(false);
  const [appealReason, setAppealReason] = useState('');
  const [appealEvidence, setAppealEvidence] = useState<File[]>([]);
  const queryClient = useQueryClient();

  // 获取违规记录
  const { data: violations = [], isLoading: violationsLoading } = useQuery(
    ['userViolations', userId],
    () => creditSystemService.getUserViolations(userId),
    { enabled: isOpen }
  );

  // 获取申诉记录
  const { data: appeals = [], isLoading: appealsLoading } = useQuery(
    ['userAppeals', userId],
    () => creditSystemService.getUserAppeals(userId),
    { enabled: isOpen }
  );

  // 提交申诉
  const submitAppealMutation = useMutation(
    (data: { violationId: number; reason: string; evidence?: File[] }) =>
      creditSystemService.submitAppeal(data.violationId, data.reason, data.evidence),
    {
      onSuccess: () => {
        toast.success('申诉已提交，请等待审核');
        setShowAppealForm(false);
        setAppealReason('');
        setAppealEvidence([]);
        setSelectedViolation(null);
        queryClient.invalidateQueries(['userAppeals', userId]);
      },
      onError: (error: any) => {
        toast.error(error.message || '提交申诉失败');
      }
    }
  );

  const getViolationTypeLabel = (type: ViolationRecord['violation_type']) => {
    const labels = {
      fake_completion: '虚假完成',
      spam_behavior: '刷分行为',
      suspicious_activity: '可疑活动',
      multiple_accounts: '多账户违规'
    };
    return labels[type] || type;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'dismissed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAppealEvidence(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setAppealEvidence(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmitAppeal = () => {
    if (!selectedViolation) return;
    
    if (!appealReason.trim()) {
      toast.error('请填写申诉理由');
      return;
    }

    submitAppealMutation.mutate({
      violationId: selectedViolation.id,
      reason: appealReason,
      evidence: appealEvidence.length > 0 ? appealEvidence : undefined
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">违规记录与申诉</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* 左侧：违规记录列表 */}
          <div className="w-1/2 border-r border-gray-200 overflow-y-auto">
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-4">违规记录</h3>
              
              {violationsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
                </div>
              ) : violations.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="w-12 h-12 mx-auto mb-2 text-green-500" />
                  <p>暂无违规记录</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {violations.map((violation) => (
                    <div
                      key={violation.id}
                      onClick={() => setSelectedViolation(violation)}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        selectedViolation?.id === violation.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <span className="font-medium text-gray-900">
                          {getViolationTypeLabel(violation.violation_type)}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(violation.status)}`}>
                          {violation.status === 'confirmed' ? '已确认' :
                           violation.status === 'dismissed' ? '已撤销' : '待审核'}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {violation.description}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {new Date(violation.created_at).toLocaleDateString()}
                        </span>
                        {violation.points_deducted > 0 && (
                          <span className="text-red-600 font-medium">
                            -{violation.points_deducted} 积分
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 右侧：详情和申诉 */}
          <div className="w-1/2 overflow-y-auto">
            {selectedViolation ? (
              <div className="p-4">
                <h3 className="font-medium text-gray-900 mb-4">违规详情</h3>
                
                {/* 违规信息 */}
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-700">违规类型</label>
                      <p className="text-gray-900">{getViolationTypeLabel(selectedViolation.violation_type)}</p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-700">详细描述</label>
                      <p className="text-gray-900">{selectedViolation.description}</p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">扣除积分</label>
                        <p className="text-red-600 font-medium">{selectedViolation.points_deducted}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">状态</label>
                        <span className={`inline-block px-2 py-1 text-xs rounded-full border ${getStatusColor(selectedViolation.status)}`}>
                          {selectedViolation.status === 'confirmed' ? '已确认' :
                           selectedViolation.status === 'dismissed' ? '已撤销' : '待审核'}
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-700">记录时间</label>
                      <p className="text-gray-900">{new Date(selectedViolation.created_at).toLocaleString()}</p>
                    </div>
                  </div>
                </div>

                {/* 申诉状态 */}
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">申诉状态</h4>
                  {appealsLoading ? (
                    <div className="flex items-center justify-center py-4">
                      <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
                    </div>
                  ) : (
                    (() => {
                      const appeal = appeals.find(a => a.violation_id === selectedViolation.id);
                      if (appeal) {
                        return (
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <div className="flex items-center space-x-2 mb-2">
                              <MessageSquare className="w-4 h-4 text-blue-600" />
                              <span className="font-medium text-blue-900">
                                申诉状态: {appeal.status === 'pending' ? '待审核' :
                                         appeal.status === 'approved' ? '已通过' : '已拒绝'}
                              </span>
                            </div>
                            <p className="text-sm text-blue-800 mb-2">{appeal.reason}</p>
                            <p className="text-xs text-blue-600">
                              提交时间: {new Date(appeal.submitted_at).toLocaleString()}
                            </p>
                            {appeal.admin_response && (
                              <div className="mt-2 p-2 bg-white border border-blue-200 rounded">
                                <p className="text-sm text-gray-700">管理员回复: {appeal.admin_response}</p>
                              </div>
                            )}
                          </div>
                        );
                      } else if (selectedViolation.status === 'confirmed') {
                        return (
                          <div>
                            {!showAppealForm ? (
                              <button
                                onClick={() => setShowAppealForm(true)}
                                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                              >
                                提交申诉
                              </button>
                            ) : (
                              <div className="space-y-4">
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    申诉理由
                                  </label>
                                  <textarea
                                    value={appealReason}
                                    onChange={(e) => setAppealReason(e.target.value)}
                                    placeholder="请详细说明您认为此违规记录有误的原因..."
                                    className="w-full p-3 border border-gray-300 rounded-lg text-sm"
                                    rows={4}
                                  />
                                </div>

                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    证据文件（可选）
                                  </label>
                                  <input
                                    type="file"
                                    multiple
                                    accept="image/*,.pdf,.doc,.docx"
                                    onChange={handleFileUpload}
                                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                  />
                                  
                                  {appealEvidence.length > 0 && (
                                    <div className="mt-2 space-y-1">
                                      {appealEvidence.map((file, index) => (
                                        <div key={index} className="flex items-center justify-between text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                          <span>{file.name}</span>
                                          <button
                                            onClick={() => removeFile(index)}
                                            className="text-red-500 hover:text-red-700"
                                          >
                                            删除
                                          </button>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>

                                <div className="flex space-x-3">
                                  <button
                                    onClick={() => setShowAppealForm(false)}
                                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                                  >
                                    取消
                                  </button>
                                  <button
                                    onClick={handleSubmitAppeal}
                                    disabled={submitAppealMutation.isLoading}
                                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
                                  >
                                    {submitAppealMutation.isLoading ? (
                                      <>
                                        <Loader2 className="w-4 h-4 animate-spin" />
                                        <span>提交中...</span>
                                      </>
                                    ) : (
                                      <>
                                        <Upload className="w-4 h-4" />
                                        <span>提交申诉</span>
                                      </>
                                    )}
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      } else {
                        return (
                          <div className="text-center py-4 text-gray-500">
                            <p>此违规记录无需申诉</p>
                          </div>
                        );
                      }
                    })()
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <FileText className="w-12 h-12 mx-auto mb-2" />
                  <p>选择一条违规记录查看详情</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViolationRecordsDialog;
