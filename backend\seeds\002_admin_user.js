const bcrypt = require('bcryptjs');

/**
 * 创建管理员用户 lozehq
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
exports.seed = async function(knex) {
  try {
    // 检查用户是否已存在
    const existingUser = await knex('users').where('username', 'lozehq').first();
    
    if (existingUser) {
      console.log('管理员用户 lozehq 已存在，跳过创建');
      return;
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash('Hwb123123', 12);
    
    // 创建管理员用户
    await knex('users').insert({
      username: 'lozehq',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      points_balance: 10000, // 给管理员更多积分
      social_accounts: JSON.stringify({}),
      is_active: true,
      email_verified: true,
      created_at: new Date(),
      updated_at: new Date(),
    });

    console.log('管理员用户 lozehq 创建成功');
  } catch (error) {
    console.error('创建管理员用户失败:', error.message);
    throw error;
  }
};