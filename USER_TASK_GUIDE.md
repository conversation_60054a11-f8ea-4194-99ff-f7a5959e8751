# 📋 任务完成指南 - 如何确保任务验证通过

## 🎯 概述

为了保证平台的公平性和防止恶意刷分，我们的任务验证系统会检测您的真实操作行为。本指南将帮助您了解如何正确完成任务以确保验证通过。

## ⏱️ 时间要求

### 最低执行时间
- **点赞任务**: 至少 5 秒
- **分享任务**: 至少 12 秒  
- **评论任务**: 至少 20 秒
- **关注任务**: 至少 6 秒

### 💡 为什么需要最低时间？
- 确保您有足够时间找到并点击相应按钮
- 防止自动化脚本快速刷分
- 保证任务的真实性和有效性

## 🖱️ 操作要求

### 必须进行的真实操作
1. **实际点击**: 必须真正点击目标按钮（点赞、分享、关注等）
2. **页面交互**: 在目标页面上进行真实的浏览和操作
3. **保持专注**: 不要在任务执行期间切换到其他应用

### ✅ 推荐的操作流程

#### 点赞任务
1. 打开视频页面
2. 观看视频内容（至少几秒）
3. 找到点赞按钮
4. 点击点赞按钮
5. 确认点赞成功（按钮变色或数字增加）
6. 等待至少 5 秒后关闭页面

#### 分享任务
1. 打开视频页面
2. 观看视频内容
3. 找到分享按钮并点击
4. 选择分享方式（微信、QQ、微博等）
5. 完成分享操作
6. 等待至少 12 秒后关闭页面

#### 评论任务
1. 打开视频页面
2. 观看视频内容
3. 滚动到评论区
4. 点击评论输入框
5. 输入有意义的评论内容
6. 点击发送按钮
7. 确认评论发布成功
8. 等待至少 20 秒后关闭页面

#### 关注任务
1. 打开用户主页或视频页面
2. 找到关注按钮
3. 点击关注按钮
4. 确认关注成功（按钮文字变为"已关注"）
5. 等待至少 6 秒后关闭页面

## 🚫 避免的行为

### 会导致验证失败的操作
1. **快速关闭**: 打开页面后立即关闭
2. **无操作等待**: 只是等待时间而不进行任何点击
3. **假操作**: 点击页面其他位置而不是目标按钮
4. **多窗口干扰**: 同时打开多个任务窗口
5. **自动化工具**: 使用脚本或插件自动完成任务

## 🔍 验证机制说明

### 系统会检测什么？
1. **执行时间**: 任务的总执行时间
2. **点击行为**: 是否有真实的鼠标点击
3. **页面交互**: 滚动、焦点变化等用户行为
4. **操作结果**: 是否成功完成目标操作（如点赞成功）

### 验证通过的条件
- ✅ 满足最低时间要求
- ✅ 检测到真实的用户交互
- ✅ 操作行为符合任务类型特征
- ✅ 没有明显的异常模式

## ⚠️ 常见问题解决

### Q: 为什么我完成了任务但验证失败？
**A**: 可能的原因：
- 执行时间过短
- 没有进行真实的点击操作
- 页面加载问题导致操作无效
- 网络问题影响了行为检测

**解决方案**:
- 确保满足最低时间要求
- 真实点击目标按钮
- 确认操作成功（如点赞按钮变色）
- 如仍失败，可提供完成截图

### Q: 跨域页面（如抖音、B站）如何验证？
**A**: 跨域页面的验证相对宽松：
- 主要依靠时间和窗口活动检测
- 建议适当延长操作时间
- 确保在目标页面上有真实活动

### Q: 验证失败后怎么办？
**A**: 您可以：
1. 重新执行任务，注意遵循操作指南
2. 提供完成截图作为证明
3. 联系客服进行人工审核

## 📸 截图证明指南

### 何时需要提供截图？
- 自动验证失败时
- 高价值任务（50积分以上）
- 系统提示需要人工审核时

### 截图要求
1. **完成前截图**: 显示任务开始状态
2. **完成后截图**: 显示操作成功结果
3. **清晰可见**: 确保关键信息清晰
4. **完整页面**: 包含页面URL和时间戳

### 截图示例
- 点赞任务：显示点赞按钮变色或点赞数增加
- 分享任务：显示分享成功提示或分享页面
- 评论任务：显示评论发布成功
- 关注任务：显示关注按钮变为"已关注"

## 🎖️ 提高通过率的技巧

### 1. 充分的执行时间
- 不要急于关闭页面
- 适当观看视频内容
- 确认操作成功后再等待几秒

### 2. 真实的用户行为
- 像正常用户一样浏览页面
- 进行必要的滚动和点击
- 避免机械化的操作模式

### 3. 稳定的网络环境
- 确保网络连接稳定
- 避免在网络不佳时执行任务
- 等待页面完全加载后再操作

### 4. 专注的操作环境
- 关闭不必要的应用程序
- 避免在任务执行期间切换窗口
- 保持对任务页面的专注

## 📞 获取帮助

如果您在任务执行过程中遇到问题：

1. **查看本指南**: 确认是否遵循了所有操作要求
2. **重试任务**: 按照指南重新执行任务
3. **提供截图**: 如果仍然失败，提供完成截图
4. **联系客服**: 通过平台客服获取人工帮助

---

**记住**: 我们的目标是确保平台的公平性，同时为真实用户提供良好的体验。遵循本指南将大大提高您的任务验证通过率！
